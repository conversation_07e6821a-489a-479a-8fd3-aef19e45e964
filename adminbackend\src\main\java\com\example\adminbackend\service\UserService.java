package com.example.adminbackend.service;

import com.example.adminbackend.dto.UserDTO;
import com.example.adminbackend.model.Role;
import com.example.adminbackend.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.List;

public interface UserService {
    
    // 根据手机号加载用户
    UserDetails loadUserByPhone(String phone) throws UsernameNotFoundException;
    
    // 创建用户
    User createUser(UserDTO userDTO);
    
    // 更新用户
    User updateUser(Long id, UserDTO userDTO);
    
    // 删除用户
    void deleteUser(Long id);
    
    // 根据ID查询用户
    User getUserById(Long id);
    
    // 根据用户名查询用户
    User getUserByUsername(String username);
    
    // 根据手机号查询用户
    User getUserByPhone(String phone);
    
    // 查询所有用户
    List<User> getAllUsers();
    
    // 分页查询所有用户
    Page<User> getAllUsers(Pageable pageable);
    
    // 根据角色查询用户
    List<User> getUsersByRole(Role role);
    
    // 分页根据角色查询用户
    Page<User> getUsersByRole(Role role, Pageable pageable);
    
    // 查询创建者为指定用户的所有用户
    List<User> getUsersByCreator(String creator);
    
    // 查询创建者为指定用户且角色为指定角色的所有用户
    List<User> getUsersByCreatorAndRole(String creator, Role role);
    
    // 分页查询创建者为指定用户且角色为指定角色的所有用户
    Page<User> getUsersByCreatorAndRole(String creator, Role role, Pageable pageable);
    
    // 检查用户是否有权限管理指定角色的用户
    boolean hasPermissionToManageRole(User currentUser, Role targetRole);
} 