import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Paper,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Speed as SpeedIcon,
  DataUsage as DataUsageIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import { MigrationProgress as MigrationProgressType, MigrationStatus } from '../../types/migration';

interface MigrationProgressProps {
  progress: MigrationProgressType;
  showDetails?: boolean;
}

const MigrationProgress: React.FC<MigrationProgressProps> = ({
  progress,
  showDetails = true
}) => {
  const getStatusColor = (status: MigrationStatus) => {
    switch (status) {
      case MigrationStatus.COMPLETED:
        return 'success';
      case MigrationStatus.FAILED:
      case MigrationStatus.CANCELLED:
        return 'error';
      case MigrationStatus.PROCESSING:
      case MigrationStatus.UPLOADING:
      case MigrationStatus.VALIDATING:
        return 'primary';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: MigrationStatus) => {
    switch (status) {
      case MigrationStatus.IDLE:
        return '待开始';
      case MigrationStatus.VALIDATING:
        return '验证中';
      case MigrationStatus.UPLOADING:
        return '上传中';
      case MigrationStatus.PROCESSING:
        return '处理中';
      case MigrationStatus.COMPLETED:
        return '已完成';
      case MigrationStatus.FAILED:
        return '失败';
      case MigrationStatus.CANCELLED:
        return '已取消';
      default:
        return '未知状态';
    }
  };

  const calculateProgress = () => {
    if (progress.totalCount === 0) return 0;
    return Math.round((progress.processedCount / progress.totalCount) * 100);
  };

  const formatTime = (ms?: number) => {
    if (!ms) return '--';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  };

  const formatSpeed = (count: number, timeMs: number) => {
    if (timeMs === 0) return '--';
    const itemsPerSecond = (count * 1000) / timeMs;
    return `${itemsPerSecond.toFixed(1)} 条/秒`;
  };

  const progressPercentage = calculateProgress();

  return (
    <Paper sx={{ p: 3 }}>
      {/* 状态标题 */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <Typography variant="h6">
          迁移进度
        </Typography>
        <Chip
          label={getStatusText(progress.status)}
          color={getStatusColor(progress.status) as any}
          size="small"
        />
      </Box>

      {/* 主要进度条 */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            总体进度
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {progress.processedCount} / {progress.totalCount} ({progressPercentage}%)
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={progressPercentage}
          sx={{ height: 8, borderRadius: 4 }}
        />
      </Box>

      {/* 批次进度 */}
      {progress.currentBatch && progress.totalBatches && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              批次进度
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {progress.currentBatch} / {progress.totalBatches}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={(progress.currentBatch / progress.totalBatches) * 100}
            sx={{ height: 6, borderRadius: 3 }}
            color="secondary"
          />
        </Box>
      )}

      {/* 详细信息 */}
      {showDetails && (
        <Grid container spacing={2}>
          {/* 统计信息 */}
          <Grid size={{ xs: 12, md: 6 }}>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <DataUsageIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="已处理"
                  secondary={`${progress.processedCount} 条`}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary="错误数量"
                  secondary={`${progress.errorCount} 条`}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText
                  primary="成功率"
                  secondary={
                    progress.processedCount > 0
                      ? `${(((progress.processedCount - progress.errorCount) / progress.processedCount) * 100).toFixed(1)}%`
                      : '--'
                  }
                />
              </ListItem>
            </List>
          </Grid>

          {/* 时间信息 */}
          <Grid size={{ xs: 12, md: 6 }}>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <ScheduleIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="开始时间"
                  secondary={
                    progress.startTime
                      ? new Date(progress.startTime).toLocaleString()
                      : '--'
                  }
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <TimelineIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="预计剩余时间"
                  secondary={formatTime(progress.estimatedTimeRemaining)}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <SpeedIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="处理速度"
                  secondary={
                    progress.startTime
                      ? formatSpeed(
                          progress.processedCount,
                          Date.now() - new Date(progress.startTime).getTime()
                        )
                      : '--'
                  }
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      )}

      {/* 消息提示 */}
      {progress.message && (
        <Box sx={{ mt: 2 }}>
          <Divider sx={{ mb: 2 }} />
          <Alert
            severity={
              progress.status === MigrationStatus.FAILED ? 'error' :
              progress.status === MigrationStatus.COMPLETED ? 'success' :
              'info'
            }
          >
            {progress.message}
          </Alert>
        </Box>
      )}
    </Paper>
  );
};

export default MigrationProgress;
