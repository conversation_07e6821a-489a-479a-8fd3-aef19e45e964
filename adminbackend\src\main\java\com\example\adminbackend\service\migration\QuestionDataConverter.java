package com.example.adminbackend.service.migration;

import com.example.adminbackend.dto.migration.SourceQuestionData;
import com.example.adminbackend.dto.migration.SourceSubQuestion;
import com.example.adminbackend.exception.DataConversionException;
import com.example.adminbackend.model.Question;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目数据转换器
 * 负责将源数据转换为目标格式
 */
@Component
@Lazy
@RequiredArgsConstructor
@Slf4j
public class QuestionDataConverter {
    
    private final ObjectMapper objectMapper;
    private final HierarchyDataProcessor hierarchyProcessor;
    
    /**
     * 将源数据转换为目标格式
     */
    public Question convertToTargetFormat(SourceQuestionData source) {
        try {
            // 1. 构建基础body数据
            Map<String, Object> bodyMap = buildBaseBodyMap(source);
            
            // 2. 处理题型特定数据
            processQuestionTypeSpecificData(bodyMap, source);
            
            // 3. 处理多媒体内容
            processMultimediaContent(bodyMap, source);
            
            // 4. 清理和优化数据
            cleanAndOptimizeData(bodyMap);
            
            // 5. 解析知识点ID
            log.info("开始解析知识点ID，题目ID: {}, 基础信息: {}", source.getId(), source.getBasicInfo());
            Long knowledgePointId = hierarchyProcessor.resolveKnowledgePointId(source.getBasicInfo());
            log.info("知识点ID解析完成，题目ID: {}, 知识点ID: {}", source.getId(), knowledgePointId);
            
            // 6. 构建Question对象
            Question question = Question.builder()
                .questionType(mapQuestionType(source.getType()))
                .body(bodyMap)
                .knowledgePointId(knowledgePointId)
                .enabled(true)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
                
            return question;
            
        } catch (Exception e) {
            log.error("数据转换失败，源数据ID: {}", source.getId(), e);
            throw new DataConversionException("数据转换失败", e);
        }
    }
    
    /**
     * 构建基础body数据
     */
    private Map<String, Object> buildBaseBodyMap(SourceQuestionData source) {
        Map<String, Object> bodyMap = new HashMap<>();
        
        // 基础字段
        bodyMap.put("type", source.getType());
        bodyMap.put("id", String.valueOf(source.getId()));
        bodyMap.put("subject", source.getSubject());
        bodyMap.put("difficulty", source.getDifficulty());
        
        // 内容字段
        if (source.getContent() != null) {
            bodyMap.put("content", processHtmlContent(source.getContent()));
        }
        
        if (source.getExplanation() != null) {
            bodyMap.put("explanation", processHtmlContent(source.getExplanation()));
        }
        
        // 标签
        if (source.getTags() != null && !source.getTags().isEmpty()) {
            bodyMap.put("tags", source.getTags());
        }
        
        return bodyMap;
    }
    
    /**
     * 处理题型特定数据
     */
    private void processQuestionTypeSpecificData(Map<String, Object> bodyMap, SourceQuestionData source) {
        String questionType = source.getType();
        
        switch (questionType) {
            case "SINGLE_CHOICE":
            case "MULTIPLE_CHOICE":
                if (source.getOptions() != null) {
                    bodyMap.put("options", source.getOptions());
                }
                if (source.getAnswer() != null) {
                    bodyMap.put("answer", source.getAnswer());
                }
                break;
                
            case "FILL_IN_BLANK":
                if (source.getAnswer() != null) {
                    // 填空题答案可能是数组或字符串
                    bodyMap.put("answer", source.getAnswer());
                }
                break;
                
            case "TRUE_FALSE":
                if (source.getAnswer() != null) {
                    // 判断题答案转换为布尔值
                    bodyMap.put("answer", convertToBooleanAnswer(source.getAnswer()));
                }
                break;
                
            case "LISTENING":
            case "READING_COMPREHENSION":
            case "CLOZE_TEST":
                // 复合题型处理
                if (source.getMaterial() != null) {
                    bodyMap.put("material", processHtmlContent(source.getMaterial()));
                }
                if (source.getSubQuestions() != null) {
                    bodyMap.put("subQuestions", processSubQuestions(source.getSubQuestions()));
                }
                break;
                
            default:
                log.warn("未知题型: {}", questionType);
        }
    }
    
    /**
     * 处理多媒体内容
     */
    private void processMultimediaContent(Map<String, Object> bodyMap, SourceQuestionData source) {
        // 这里可以添加对图片、音频、视频等多媒体内容的处理逻辑
        // 例如：URL验证、格式转换等
        
        // 检查内容中是否包含音频标签
        String content = (String) bodyMap.get("content");
        if (content != null && content.contains("<audio")) {
            log.debug("检测到音频内容，题目ID: {}", source.getId());
        }
        
        // 检查内容中是否包含图片标签
        if (content != null && content.contains("<img")) {
            log.debug("检测到图片内容，题目ID: {}", source.getId());
        }
    }
    
    /**
     * 清理和优化数据
     */
    private void cleanAndOptimizeData(Map<String, Object> bodyMap) {
        // 移除空值
        bodyMap.entrySet().removeIf(entry -> entry.getValue() == null);
        
        // 清理空字符串
        bodyMap.entrySet().forEach(entry -> {
            if (entry.getValue() instanceof String) {
                String value = ((String) entry.getValue()).trim();
                if (value.isEmpty()) {
                    entry.setValue(null);
                }
            }
        });
        
        // 再次移除空值
        bodyMap.entrySet().removeIf(entry -> entry.getValue() == null);
    }
    
    /**
     * 处理HTML内容，保持LaTeX公式格式
     */
    private String processHtmlContent(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return htmlContent;
        }
        
        // 保持LaTeX公式格式不变
        // 这里可以添加HTML清理逻辑，但要保护LaTeX公式
        return htmlContent.trim();
    }
    
    /**
     * 处理子题目
     */
    private List<Map<String, Object>> processSubQuestions(List<SourceSubQuestion> subQuestions) {
        return subQuestions.stream()
            .map(this::convertSubQuestion)
            .collect(Collectors.toList());
    }
    
    /**
     * 转换子题目
     */
    private Map<String, Object> convertSubQuestion(SourceSubQuestion subQuestion) {
        Map<String, Object> subMap = new HashMap<>();
        subMap.put("type", subQuestion.getType());
        subMap.put("id", subQuestion.getId());
        
        if (subQuestion.getContent() != null) {
            subMap.put("content", processHtmlContent(subQuestion.getContent()));
        }
        
        if (subQuestion.getOptions() != null) {
            subMap.put("options", subQuestion.getOptions());
        }
        
        if (subQuestion.getAnswer() != null) {
            subMap.put("answer", subQuestion.getAnswer());
        }
        
        if (subQuestion.getExplanation() != null) {
            subMap.put("explanation", processHtmlContent(subQuestion.getExplanation()));
        }
        
        return subMap;
    }
    
    /**
     * 转换布尔答案
     */
    private Object convertToBooleanAnswer(Object answer) {
        if (answer instanceof Boolean) {
            return answer;
        }
        
        if (answer instanceof String) {
            String strAnswer = ((String) answer).toLowerCase().trim();
            switch (strAnswer) {
                case "true":
                case "t":
                case "1":
                case "正确":
                case "是":
                    return true;
                case "false":
                case "f":
                case "0":
                case "错误":
                case "否":
                    return false;
                default:
                    return answer; // 保持原值
            }
        }
        
        return answer; // 保持原值
    }
    
    /**
     * 题型映射
     */
    private Question.QuestionType mapQuestionType(String sourceType) {
        try {
            return Question.QuestionType.valueOf(sourceType);
        } catch (IllegalArgumentException e) {
            log.warn("未知的题型: {}, 使用默认值 SINGLE_CHOICE", sourceType);
            return Question.QuestionType.SINGLE_CHOICE;
        }
    }
    
    /**
     * 批量转换数据
     */
    public List<Question> convertBatch(List<SourceQuestionData> sourceDataList) {
        List<Question> questions = new ArrayList<>();
        
        for (SourceQuestionData sourceData : sourceDataList) {
            try {
                Question question = convertToTargetFormat(sourceData);
                questions.add(question);
            } catch (Exception e) {
                log.error("批量转换中单个数据转换失败，ID: {}", sourceData.getId(), e);
                // 继续处理其他数据
            }
        }
        
        return questions;
    }
}
