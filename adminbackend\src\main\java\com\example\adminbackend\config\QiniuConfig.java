package com.example.adminbackend.config;

import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 七牛云配置类
 */
@Component
@ConfigurationProperties(prefix = "qiniu")
@Data
public class QiniuConfig {
    
    /**
     * 访问密钥
     */
    private String accessKey;
    
    /**
     * 私有密钥
     */
    private String secretKey;
    
    /**
     * 存储空间名称
     */
    private String bucketName;
    
    /**
     * CDN域名
     */
    private String domain;
    
    /**
     * 存储区域
     */
    private String region = "huadong";

    /**
     * 上传路径配置
     */
    private UploadPaths uploadPaths = new UploadPaths();

    /**
     * 上传路径配置类
     */
    @Data
    public static class UploadPaths {
        /**
         * 图片上传路径
         */
        private String image = "image";

        /**
         * 视频上传路径
         */
        private String video = "video";

        /**
         * 音频上传路径
         */
        private String audio = "audio";
    }
    
    /**
     * 创建认证对象
     */
    @Bean
    public Auth qiniuAuth() {
        return Auth.create(accessKey, secretKey);
    }
    
    /**
     * 创建上传管理器
     */
    @Bean
    public UploadManager uploadManager() {
        Configuration cfg = new Configuration(getRegionByName(region));
        // 启用分片上传v2
        cfg.resumableUploadAPIVersion = Configuration.ResumableUploadAPIVersion.V2;
        return new UploadManager(cfg);
    }

    /**
     * 创建存储空间管理器
     */
    @Bean
    public BucketManager bucketManager() {
        Configuration cfg = new Configuration(getRegionByName(region));
        return new BucketManager(qiniuAuth(), cfg);
    }
    
    /**
     * 根据区域名称获取Region对象
     */
    private Region getRegionByName(String regionName) {
        switch (regionName.toLowerCase()) {
            case "huadong":
                return Region.region0();
            case "huabei":
                return Region.region1();
            case "huanan":
                return Region.region2();
            case "beimei":
                return Region.regionNa0();
            case "dongnanya":
                return Region.regionAs0();
            default:
                return Region.region0(); // 默认华东
        }
    }
}
