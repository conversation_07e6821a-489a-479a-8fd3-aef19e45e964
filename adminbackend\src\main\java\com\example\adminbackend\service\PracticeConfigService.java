package com.example.adminbackend.service;

import com.example.adminbackend.model.PracticeConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 练习配置服务接口
 */
public interface PracticeConfigService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建练习配置
     */
    PracticeConfig createConfig(PracticeConfig config, String createdBy);

    /**
     * 更新练习配置
     */
    PracticeConfig updateConfig(Long configId, PracticeConfig config);

    /**
     * 根据ID获取配置
     */
    PracticeConfig getConfigById(Long configId);

    /**
     * 删除配置
     */
    void deleteConfig(Long configId);

    /**
     * 启用/禁用配置
     */
    PracticeConfig toggleConfig(Long configId, boolean enabled);

    // ==================== 查询操作 ====================

    /**
     * 分页查询配置
     */
    Page<PracticeConfig> getConfigs(Pageable pageable);

    /**
     * 根据配置类型分页查询
     */
    Page<PracticeConfig> getConfigsByType(PracticeConfig.ConfigType configType, Pageable pageable);

    /**
     * 根据适用范围分页查询
     */
    Page<PracticeConfig> getConfigsByScope(PracticeConfig.ScopeType scopeType, Pageable pageable);

    /**
     * 获取所有启用的配置
     */
    List<PracticeConfig> getAllEnabledConfigs();

    /**
     * 根据配置类型获取所有启用的配置
     */
    List<PracticeConfig> getEnabledConfigsByType(PracticeConfig.ConfigType configType);

    // ==================== 核心业务方法 ====================

    /**
     * 获取知识点的有效配置
     * 按优先级查找：知识点 > 章节 > 科目 > 全局
     */
    Optional<PracticeConfig> getEffectiveConfigForKnowledgePoint(
            Long knowledgePointId, 
            PracticeConfig.ConfigType configType);

    /**
     * 获取全局默认配置
     */
    Optional<PracticeConfig> getGlobalConfig(PracticeConfig.ConfigType configType);

    /**
     * 创建或更新全局配置
     */
    PracticeConfig createOrUpdateGlobalConfig(
            PracticeConfig.ConfigType configType, 
            Integer questionCount, 
            PracticeConfig.SelectionStrategy strategy,
            String createdBy);

    /**
     * 创建或更新科目配置
     */
    PracticeConfig createOrUpdateSubjectConfig(
            Long subjectId,
            PracticeConfig.ConfigType configType, 
            Integer questionCount, 
            PracticeConfig.SelectionStrategy strategy,
            String createdBy);

    /**
     * 创建或更新章节配置
     */
    PracticeConfig createOrUpdateChapterConfig(
            Long chapterId,
            PracticeConfig.ConfigType configType, 
            Integer questionCount, 
            PracticeConfig.SelectionStrategy strategy,
            String createdBy);

    /**
     * 创建或更新知识点配置
     */
    PracticeConfig createOrUpdateKnowledgePointConfig(
            Long knowledgePointId,
            PracticeConfig.ConfigType configType, 
            Integer questionCount, 
            PracticeConfig.SelectionStrategy strategy,
            String createdBy);

    // ==================== 统计和分析 ====================

    /**
     * 获取配置统计信息
     */
    Map<String, Object> getConfigStatistics();

    /**
     * 根据目标ID获取配置
     */
    List<PracticeConfig> getConfigsByTargetId(Long targetId);

    /**
     * 验证配置是否有效
     */
    boolean validateConfig(PracticeConfig config);

    /**
     * 检查配置是否存在冲突
     */
    boolean hasConflictingConfig(PracticeConfig config);

    // ==================== 批量操作 ====================

    /**
     * 批量创建配置
     */
    List<PracticeConfig> createConfigs(List<PracticeConfig> configs, String createdBy);

    /**
     * 批量删除配置
     */
    void deleteConfigs(List<Long> configIds);

    /**
     * 批量启用/禁用配置
     */
    void toggleConfigs(List<Long> configIds, boolean enabled);
}
