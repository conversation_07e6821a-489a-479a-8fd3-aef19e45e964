import * as XLSX from 'xlsx';

/**
 * Excel模板生成器
 */
export class TemplateGenerator {
  
  /**
   * 生成题目导入模板
   */
  static generateQuestionImportTemplate(): void {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 1. 创建模板数据工作表
    const templateData = [
      {
        '题目类型': 'SINGLE_CHOICE',
        '学科': 'ENGLISH',
        '难度': 'EASY',
        '题目内容': 'She _____ to school every day.',
        '材料内容': '',
        '子题目序号': '',
        '子题目内容': '',
        '选项A': 'go',
        '选项B': 'goes',
        '选项C': 'went',
        '选项D': 'going',
        '选项E': '',
        '选项F': '',
        '答案': 'B',
        '子题目JSON': '',
        '解析': '主语She是第三人称单数，一般现在时动词要加s。',
        '标签': '动词时态,一般现在时',
        '备注': '基础语法题'
      },
      {
        '题目类型': 'MULTIPLE_CHOICE',
        '学科': 'MATH',
        '难度': 'MEDIUM',
        '题目内容': '下列哪些是质数？',
        '材料内容': '',
        '子题目序号': '',
        '子题目内容': '',
        '选项A': '2',
        '选项B': '3',
        '选项C': '4',
        '选项D': '5',
        '选项E': '6',
        '选项F': '',
        '答案': 'A,B,D',
        '子题目JSON': '',
        '解析': '质数是只能被1和自身整除的大于1的自然数。2、3、5是质数，4和6不是。',
        '标签': '数论,质数',
        '备注': '数学基础概念'
      },
      {
        '题目类型': 'FILL_IN_BLANK',
        '学科': 'MATH',
        '难度': 'EASY',
        '题目内容': '2 + 3 = _____',
        '材料内容': '',
        '子题目序号': '',
        '子题目内容': '',
        '选项A': '',
        '选项B': '',
        '选项C': '',
        '选项D': '',
        '选项E': '',
        '选项F': '',
        '答案': '5',
        '子题目JSON': '',
        '解析': '基础加法运算：2加3等于5。',
        '标签': '基础运算,加法',
        '备注': '基础计算题'
      },
      {
        '题目类型': 'READING_COMPREHENSION',
        '学科': 'ENGLISH',
        '难度': 'MEDIUM',
        '题目内容': '<p>阅读下面的信件，回答问题。</p>',
        '材料内容': '<div><p>Dear Betty,</p><p>Thank you very much for your email. I know you\'d like to visit the National Museum in our city. It is a big and famous museum. Let me tell you the way to it. Take a car from your house and drive along Hongxing Street. Turn left at the third turning, not the last one. You\'ll see a park on your right. The museum is opposite the park.</p><p>Yours, Jerry</p></div>',
        '子题目序号': '1',
        '子题目内容': 'Betty will go to the National Museum ____.',
        '选项A': 'by bus',
        '选项B': 'by taxi',
        '选项C': 'by car',
        '选项D': 'by bike',
        '选项E': '',
        '选项F': '',
        '答案': 'C',
        '子题目JSON': '',
        '解析': '根据短文中"Take a car from your house"可知贝蒂是坐车去博物馆的。',
        '标签': '阅读理解,路线指引',
        '备注': '阅读理解题第1题'
      },
      {
        '题目类型': 'READING_COMPREHENSION',
        '学科': 'ENGLISH',
        '难度': 'MEDIUM',
        '题目内容': '<p>阅读下面的信件，回答问题。</p>',
        '材料内容': '', // 第二行材料内容留空，与第一行相同
        '子题目序号': '2',
        '子题目内容': 'The museum is ____.',
        '选项A': 'next to the park',
        '选项B': 'opposite the park',
        '选项C': 'behind the park',
        '选项D': 'in the park',
        '选项E': '',
        '选项F': '',
        '答案': 'B',
        '子题目JSON': '',
        '解析': '文中提到"The museum is opposite the park"。',
        '标签': '阅读理解,路线指引',
        '备注': '阅读理解题第2题'
      },
      {
        '题目类型': 'LISTENING',
        '学科': 'ENGLISH',
        '难度': 'MEDIUM',
        '题目内容': '<p>听五段对话，选择正确的答案。每段对话读两遍。</p><audio controls><source src="https://rcdns.oss-cn-shanghai.aliyuncs.com/queaudio/example.mp3" type="audio/mpeg"></audio>',
        '材料内容': '', // 听力题不使用材料内容字段，音频在题目内容中
        '子题目序号': '1',
        '子题目内容': 'What class is Danny in?',
        '选项A': 'Class 7 Grade 2',
        '选项B': 'Class 2 Grade 7',
        '选项C': 'Class 7 Grade 7',
        '选项D': '',
        '选项E': '',
        '选项F': '',
        '答案': 'B',
        '子题目JSON': '',
        '解析': '根据听力材料："I am in Class Two Grade Seven."',
        '标签': '听力理解,对话',
        '备注': '听力题示例'
      }
    ];
    
    const templateSheet = XLSX.utils.json_to_sheet(templateData);
    
    // 设置列宽
    const columnWidths = [
      { wch: 15 }, // 题目类型
      { wch: 10 }, // 学科
      { wch: 8 },  // 难度
      { wch: 30 }, // 题目内容
      { wch: 40 }, // 材料内容
      { wch: 8 },  // 子题目序号
      { wch: 35 }, // 子题目内容
      { wch: 15 }, // 选项A
      { wch: 15 }, // 选项B
      { wch: 15 }, // 选项C
      { wch: 15 }, // 选项D
      { wch: 15 }, // 选项E
      { wch: 15 }, // 选项F
      { wch: 10 }, // 答案
      { wch: 20 }, // 子题目JSON
      { wch: 40 }, // 解析
      { wch: 20 }, // 标签
      { wch: 15 }  // 备注
    ];
    templateSheet['!cols'] = columnWidths;
    
    XLSX.utils.book_append_sheet(workbook, templateSheet, '题目模板');
    
    // 2. 创建说明工作表
    const instructionData = [
      ['字段名', '说明', '必填', '可选值/格式', '示例'],
      ['题目类型', '题目的类型', '是', 'SINGLE_CHOICE, MULTIPLE_CHOICE, FILL_IN_BLANK, TRUE_FALSE, READING_COMPREHENSION, LISTENING, CLOZE_TEST, MATCHING', 'SINGLE_CHOICE'],
      ['学科', '题目所属学科', '是', 'ENGLISH, MATH, PHYSICS, CHEMISTRY, BIOLOGY, HISTORY, GEOGRAPHY', 'ENGLISH'],
      ['难度', '题目难度等级', '是', 'EASY, MEDIUM, HARD', 'EASY'],
      ['题目内容', '题目的正文内容', '必填', 'HTML格式文本，支持LaTeX数学公式', 'She _____ to school every day. 或 <p>阅读下面的信件，回答问题。</p>'],
      ['材料内容', '阅读理解/完形填空的材料', '阅读理解和完形填空必填', 'HTML格式文本，多行题目只需第一行填写。听力题不使用此字段', '阅读材料'],
      ['子题目序号', '嵌套题型的子题目序号', '嵌套题型必填', '数字，从1开始递增', '1, 2, 3...'],
      ['子题目内容', '嵌套题型的子题目内容', '嵌套题型必填', '具体的子题目文本', 'Betty will go to the National Museum ____.'],
      ['选项A-F', '选择题的选项', '选择题必填', '文本内容，至少需要2个选项', 'go'],
      ['答案', '题目的正确答案', '必填', '单选题：A-F中的一个字母；多选题：逗号分隔的字母组合；其他题型：具体答案', 'B 或 A,B,D 或 5'],
      ['子题目JSON', '嵌套题型的子题目（备用）', '否', 'JSON格式的子题目数组，建议使用多行方式', '不推荐使用'],
      ['解析', '题目的答案解析', '是', 'HTML格式文本，支持LaTeX数学公式', '主语She是第三人称单数...'],
      ['标签', '题目的知识点标签', '否', '逗号分隔的标签列表', '动词时态,一般现在时'],
      ['备注', '题目的备注信息', '否', '文本内容', '重点题目'],
      [],
      ['重要提示：'],
      ['1. 必填字段不能为空'],
      ['2. 题目ID会自动生成时间戳，无需填写'],
      ['3. 选择题至少需要2个选项'],
      ['4. 多选题答案用逗号分隔，如：A,B,D'],
      ['5. 支持HTML格式和LaTeX数学公式'],
      ['6. 建议先下载模板，按格式填写后导入'],
      ['7. 嵌套题型使用多行方式：每个子题目占一行'],
      ['8. 阅读理解/完形填空：需要填写材料内容字段'],
      ['9. 听力题：音频内容直接写在题目内容字段中，不使用材料内容字段'],
      ['10. 嵌套题型多行中，材料内容只需在第一行填写'],
      ['11. 简单题型（单选/多选/填空/判断）填写题目内容和答案即可'],
      [],
      ['题目类型说明：'],
      ['SINGLE_CHOICE - 单选题'],
      ['MULTIPLE_CHOICE - 多选题'],
      ['FILL_IN_BLANK - 填空题'],
      ['TRUE_FALSE - 判断题'],
      ['READING_COMPREHENSION - 阅读理解'],
      ['LISTENING - 听力题'],
      ['CLOZE_TEST - 完形填空'],
      ['MATCHING - 匹配题'],
      [],
      ['学科说明：'],
      ['ENGLISH - 英语'],
      ['MATH - 数学'],
      ['PHYSICS - 物理'],
      ['CHEMISTRY - 化学'],
      ['BIOLOGY - 生物'],
      ['HISTORY - 历史'],
      ['GEOGRAPHY - 地理']
    ];
    
    const instructionSheet = XLSX.utils.aoa_to_sheet(instructionData);
    
    // 设置说明工作表的列宽
    const instructionColumnWidths = [
      { wch: 15 }, // 字段名
      { wch: 40 }, // 说明
      { wch: 8 },  // 必填
      { wch: 50 }, // 可选值/格式
      { wch: 30 }  // 示例
    ];
    instructionSheet['!cols'] = instructionColumnWidths;
    
    XLSX.utils.book_append_sheet(workbook, instructionSheet, '使用说明');
    
    // 3. 创建空白模板工作表
    const emptyTemplateData = [
      {
        '题目类型': '',
        '学科': '',
        '难度': '',
        '题目内容': '',
        '材料内容': '',
        '子题目序号': '',
        '子题目内容': '',
        '选项A': '',
        '选项B': '',
        '选项C': '',
        '选项D': '',
        '选项E': '',
        '选项F': '',
        '答案': '',
        '子题目JSON': '',
        '解析': '',
        '标签': '',
        '备注': ''
      }
    ];
    
    const emptyTemplateSheet = XLSX.utils.json_to_sheet(emptyTemplateData);
    emptyTemplateSheet['!cols'] = columnWidths;
    
    XLSX.utils.book_append_sheet(workbook, emptyTemplateSheet, '空白模板');
    
    // 下载文件
    const fileName = `题目导入模板_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  }
  
  /**
   * 生成单选题模板
   */
  static generateSingleChoiceTemplate(): void {
    const templateData = [
      {
        '题目类型': 'SINGLE_CHOICE',
        '学科': 'ENGLISH',
        '难度': 'EASY',
        '题目内容': 'What is the capital of France?',
        '选项A': 'London',
        '选项B': 'Berlin',
        '选项C': 'Paris',
        '选项D': 'Madrid',
        '答案': 'C',
        '解析': 'Paris is the capital and largest city of France.',
        '标签': 'geography,capitals',
        '备注': 'Basic geography question'
      }
    ];

    this.generateTemplateFile(templateData, '单选题模板');
  }
  
  /**
   * 生成多选题模板
   */
  static generateMultipleChoiceTemplate(): void {
    const templateData = [
      {
        '题目类型': 'MULTIPLE_CHOICE',
        '学科': 'MATH',
        '难度': 'MEDIUM',
        '题目内容': '下列哪些数字是偶数？',
        '选项A': '2',
        '选项B': '3',
        '选项C': '4',
        '选项D': '5',
        '选项E': '6',
        '答案': 'A,C,E',
        '解析': '偶数是能被2整除的整数。2、4、6都是偶数。',
        '标签': '数论,偶数',
        '备注': '基础数学概念'
      }
    ];

    this.generateTemplateFile(templateData, '多选题模板');
  }
  
  /**
   * 生成填空题模板
   */
  static generateFillInBlankTemplate(): void {
    const templateData = [
      {
        '题目类型': 'FILL_IN_BLANK',
        '学科': 'MATH',
        '难度': 'EASY',
        '题目内容': '5 × 3 = _____',
        '答案': '15',
        '解析': '5乘以3等于15。',
        '标签': '基础运算,乘法',
        '备注': '基础乘法题'
      }
    ];

    this.generateTemplateFile(templateData, '填空题模板');
  }
  
  /**
   * 生成模板文件的通用方法
   */
  private static generateTemplateFile(data: any[], sheetName: string): void {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // 设置列宽
    const columnWidths = [
      { wch: 15 }, { wch: 10 }, { wch: 8 }, { wch: 30 },
      { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 },
      { wch: 10 }, { wch: 40 }, { wch: 20 }, { wch: 15 }
    ];
    worksheet['!cols'] = columnWidths;
    
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    const fileName = `${sheetName}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  }
}

// 导出默认实例
export const templateGenerator = new TemplateGenerator();
