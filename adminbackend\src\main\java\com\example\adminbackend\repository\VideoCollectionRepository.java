package com.example.adminbackend.repository;

import com.example.adminbackend.model.VideoCollection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 视频合集数据访问层
 */
@Repository
public interface VideoCollectionRepository extends JpaRepository<VideoCollection, Long> {

    /**
     * 根据名称查找视频合集
     */
    Optional<VideoCollection> findByName(String name);

    /**
     * 检查视频合集名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 根据名称模糊查询视频合集
     */
    @Query("SELECT vc FROM VideoCollection vc WHERE vc.name LIKE %:keyword% OR vc.description LIKE %:keyword%")
    List<VideoCollection> findByKeyword(@Param("keyword") String keyword);

    /**
     * 获取所有视频合集，按创建时间排序
     */
    List<VideoCollection> findAllByOrderByCreatedAtDesc();

    /**
     * 获取所有视频合集，按名称排序
     */
    List<VideoCollection> findAllByOrderByNameAsc();
}
