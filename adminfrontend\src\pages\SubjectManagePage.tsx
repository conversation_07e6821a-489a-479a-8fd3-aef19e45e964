import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Chip,
  Snackbar,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  School,
  Refresh,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { curriculumAPI } from '../services/api';
import { Subject } from '../types';

const SubjectManagePage: React.FC = () => {
  const { isSuperAdmin } = useAuth();
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 加载学科列表
  const loadSubjects = async () => {
    try {
      setLoading(true);
      const data = await curriculumAPI.getSubjects();
      setSubjects(data);
    } catch (error: any) {
      console.error('加载学科列表失败:', error);
      setError('加载学科列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSubjects();
  }, []);

  // 打开新增对话框
  const handleAdd = () => {
    setEditingSubject(null);
    setFormData({ name: '', description: '' });
    setOpenDialog(true);
  };

  // 打开编辑对话框
  const handleEdit = (subject: Subject) => {
    setEditingSubject(subject);
    setFormData({
      name: subject.name,
      description: subject.description || '',
    });
    setOpenDialog(true);
  };

  // 保存学科
  const handleSave = async () => {
    if (!formData.name.trim()) {
      setError('学科名称不能为空');
      return;
    }

    try {
      if (editingSubject) {
        // 更新学科
        await curriculumAPI.updateSubject(editingSubject.id, formData);
        setSuccess('学科更新成功');
      } else {
        // 创建新学科
        await curriculumAPI.createSubject(formData);
        setSuccess('学科创建成功');
      }

      setOpenDialog(false);
      await loadSubjects();
    } catch (error: any) {
      console.error('保存学科失败:', error);
      setError('保存学科失败: ' + (error.message || '未知错误'));
    }
  };

  // 删除学科
  const handleDelete = async (subject: Subject) => {
    const confirmMessage = `⚠️ 警告：级联删除操作

确定要删除学科"${subject.name}"吗？

此操作将同时删除：
• 该学科下的所有版本
• 所有版本下的章节
• 所有章节下的知识点
• 知识点关联的视频和图片文件

此操作不可撤销，请谨慎操作！`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const response = await curriculumAPI.deleteSubject(subject.id);
      if (response.deletedFilesCount > 0) {
        setSuccess(`学科删除成功，已同时删除 ${response.deletedFilesCount} 个文件`);
      } else {
        setSuccess('学科删除成功');
      }
      await loadSubjects();
    } catch (error: any) {
      console.error('删除学科失败:', error);
      setError('删除学科失败: ' + (error.message || '未知错误'));
    }
  };

  return (
    <Layout>
      <Box sx={{ p: 3 }}>
        {/* 页面标题 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <School sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h4" fontWeight="bold">
              学科管理
            </Typography>
          </Box>
          <Box>
            <Button
              startIcon={<Refresh />}
              onClick={loadSubjects}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              刷新
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleAdd}
            >
              新增学科
            </Button>
          </Box>
        </Box>

        {/* 学科列表 */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>学科名称</TableCell>
                  <TableCell>描述</TableCell>
                  <TableCell>版本数量</TableCell>
                  <TableCell>创建时间</TableCell>
                  <TableCell>更新时间</TableCell>
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : subjects.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography color="text.secondary">
                        暂无学科数据
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  subjects.map((subject) => (
                    <TableRow key={subject.id}>
                      <TableCell>{subject.id}</TableCell>
                      <TableCell>
                        <Typography fontWeight="medium">
                          {subject.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {subject.description || '-'}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={subject.versions?.length || 0} 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        {subject.createdAt ? new Date(subject.createdAt).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell>
                        {subject.updatedAt ? new Date(subject.updatedAt).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(subject)}
                          color="primary"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(subject)}
                          color="error"
                          disabled={!isSuperAdmin}
                          title={!isSuperAdmin ? "只有超级管理员可以删除学科" : "删除学科"}
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* 新增/编辑对话框 */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingSubject ? '编辑学科' : '新增学科'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="学科名称 *"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="描述"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                multiline
                rows={3}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>取消</Button>
            <Button onClick={handleSave} variant="contained">
              {editingSubject ? '更新' : '创建'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 成功提示 */}
        <Snackbar
          open={!!success}
          autoHideDuration={3000}
          onClose={() => setSuccess('')}
        >
          <Alert severity="success" onClose={() => setSuccess('')}>
            {success}
          </Alert>
        </Snackbar>

        {/* 错误提示 */}
        <Snackbar
          open={!!error}
          autoHideDuration={5000}
          onClose={() => setError('')}
        >
          <Alert severity="error" onClose={() => setError('')}>
            {error}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default SubjectManagePage;
