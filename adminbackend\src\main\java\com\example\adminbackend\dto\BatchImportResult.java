package com.example.adminbackend.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量导入结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchImportResult {
    
    /**
     * 成功数量
     */
    @Builder.Default
    private int successCount = 0;
    
    /**
     * 失败数量
     */
    @Builder.Default
    private int failCount = 0;
    
    /**
     * 更新数量
     */
    @Builder.Default
    private int updateCount = 0;
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 成功的题目ID列表
     */
    @Builder.Default
    private List<String> successIds = new ArrayList<>();
    
    /**
     * 增加成功计数
     */
    public void incrementSuccess() {
        this.successCount++;
    }
    
    /**
     * 增加失败计数
     */
    public void incrementFail() {
        this.failCount++;
    }
    
    /**
     * 增加更新计数
     */
    public void incrementUpdate() {
        this.updateCount++;
    }
    
    /**
     * 添加错误信息
     */
    public void addError(String questionId, String message) {
        this.errors.add("题目ID " + questionId + ": " + message);
    }
    
    /**
     * 添加警告信息
     */
    public void addWarning(String questionId, String message) {
        this.warnings.add("题目ID " + questionId + ": " + message);
    }
    
    /**
     * 添加成功的题目ID
     */
    public void addSuccessId(String questionId) {
        this.successIds.add(questionId);
    }
    
    /**
     * 合并另一个结果
     */
    public void merge(BatchImportResult other) {
        this.successCount += other.successCount;
        this.failCount += other.failCount;
        this.updateCount += other.updateCount;
        this.errors.addAll(other.errors);
        this.warnings.addAll(other.warnings);
        this.successIds.addAll(other.successIds);
    }
    
    /**
     * 获取总处理数量
     */
    public int getTotalCount() {
        return successCount + failCount + updateCount;
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return failCount > 0 || !errors.isEmpty();
    }
    
    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
}
