import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
  Typography,
  Chip,
  IconButton,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  SelectChangeEvent,
  LinearProgress,
} from '@mui/material';
import RichTextButton from './RichTextButton';
import { PendingFile } from './WangEditor';
import { uploadPendingFiles, replaceTemporaryUrls, cleanupTempUrls } from '../utils/fileUpload';

import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import {
  Question,
  QuestionFormData,
  QuestionType,
  SubjectEnum,
  SubQuestion,
  QuestionCreateRequest,
  QuestionUpdateRequest,
  Subject,
  SubjectVersion,
  Chapter,
  KnowledgePoint,
  KnowledgePointHierarchyInfo,
} from '../types';
import { questionAPI, curriculumAPI } from '../services/api';
import UnifiedRenderer from './UnifiedRenderer';

interface QuestionFormProps {
  question?: Question | null;
  onSubmit: () => void;
  onCancel: () => void;
}

const QuestionForm: React.FC<QuestionFormProps> = ({ question, onSubmit, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 文件上传相关状态
  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ current: number; total: number } | null>(null);

  // 层级选择数据
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [subjectVersions, setSubjectVersions] = useState<SubjectVersion[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [knowledgePoints, setKnowledgePoints] = useState<KnowledgePoint[]>([]);

  // 选择的层级ID
  const [selectedSubjectId, setSelectedSubjectId] = useState<number | null>(null);
  const [selectedSubjectVersionId, setSelectedSubjectVersionId] = useState<number | null>(null);
  const [selectedChapterId, setSelectedChapterId] = useState<number | null>(null);

  // 编辑模式标记：在编辑现有题目时为true，避免级联重置
  const [isEditMode, setIsEditMode] = useState(false);

  // 表单数据
  const [formData, setFormData] = useState<QuestionFormData>({
    knowledgePointId: 0, // 将从层级选择中获取
    questionType: 'SINGLE_CHOICE',
    subject: 'ENGLISH',
    difficulty: 'EASY',
    tags: [],
    content: '',
    answer: '',
    explanation: '',
    options: ['', '', '', ''],
    material: '',
    subQuestions: [],
    enabled: true,
  });

  // 标签输入
  const [tagInput, setTagInput] = useState('');

  // 科目选择变化处理器
  const handleSubjectChange = (event: SelectChangeEvent<number>) => {
    const subjectId = event.target.value as number | '';
    setIsEditMode(false); // 用户手动选择时退出编辑模式
    setSelectedSubjectId(subjectId === '' ? null : subjectId);

    if (subjectId !== '') {
      const subject = subjects.find(s => s.id === subjectId);
      if (subject) {
        // 中文科目名称到英文枚举值的映射
        const subjectMapping: Record<string, SubjectEnum> = {
          '英语': 'ENGLISH',
          '数学': 'MATH',
          '物理': 'PHYSICS',
          '化学': 'CHEMISTRY'
        };

        const subjectEnum = subjectMapping[subject.name];
        if (subjectEnum) {
          handleFieldChange('subject', subjectEnum);
        } else {
          console.warn('未知的科目名称:', subject.name);
        }
      }
    }
  };

  // 加载科目列表
  useEffect(() => {
    const loadSubjects = async () => {
      try {
        // 首先尝试获取有版本的科目列表
        const subjectsData = await curriculumAPI.getSubjectsWithVersions();
        setSubjects(subjectsData);
      } catch (error) {
        console.error('加载科目列表失败:', error);
        
        // 如果获取有版本的科目列表失败，尝试获取所有科目列表
        try {
          const allSubjectsData = await curriculumAPI.getSubjects();
          setSubjects(allSubjectsData);
        } catch (secondError) {
          console.error('加载所有科目列表也失败:', secondError);
          setError('加载科目列表失败，请刷新页面重试');
        }
      }
    };
    loadSubjects();
  }, []);

  // 科目选择变化
  useEffect(() => {
    // 编辑模式下跳过级联重置
    if (isEditMode) return;

    if (selectedSubjectId) {
      const loadSubjectVersions = async () => {
        try {
          const versionsData = await curriculumAPI.getVersionsBySubjectId(selectedSubjectId);
          setSubjectVersions(versionsData);
          setChapters([]);
          setKnowledgePoints([]);
          setSelectedSubjectVersionId(null);
          setSelectedChapterId(null);
        } catch (error) {
          console.error('加载科目版本失败:', error);
          setError('加载科目版本失败');
        }
      };
      loadSubjectVersions();
    } else {
      setSubjectVersions([]);
      setChapters([]);
      setKnowledgePoints([]);
      setSelectedSubjectVersionId(null);
      setSelectedChapterId(null);
    }
  }, [selectedSubjectId, isEditMode]);

  // 科目版本选择变化
  useEffect(() => {
    // 编辑模式下跳过级联重置
    if (isEditMode) return;

    if (selectedSubjectVersionId) {
      const loadChapters = async () => {
        try {
          const chaptersData = await curriculumAPI.getChaptersBySubjectVersionId(selectedSubjectVersionId);
          setChapters(chaptersData);
          setKnowledgePoints([]);
          setSelectedChapterId(null);
        } catch (error) {
          console.error('加载章节列表失败:', error);
          setError('加载章节列表失败');
        }
      };
      loadChapters();
    } else {
      setChapters([]);
      setKnowledgePoints([]);
      setSelectedChapterId(null);
    }
  }, [selectedSubjectVersionId, isEditMode]);

  // 章节选择变化
  useEffect(() => {
    // 编辑模式下跳过级联重置
    if (isEditMode) return;

    if (selectedChapterId) {
      const loadKnowledgePoints = async () => {
        try {
          const knowledgePointsData = await curriculumAPI.getKnowledgePointsByChapterId(selectedChapterId);
          setKnowledgePoints(knowledgePointsData);
        } catch (error) {
          console.error('加载知识点列表失败:', error);
          setError('加载知识点列表失败');
        }
      };
      loadKnowledgePoints();
    } else {
      setKnowledgePoints([]);
    }
  }, [selectedChapterId, isEditMode]);

  // 验证知识点ID是否有效
  useEffect(() => {
    if (formData.knowledgePointId && knowledgePoints.length > 0) {
      const isValidKnowledgePoint = knowledgePoints.some(kp => kp.id === formData.knowledgePointId);
      if (!isValidKnowledgePoint) {
        console.warn(`知识点ID ${formData.knowledgePointId} 不在当前章节的知识点列表中`);
        // 在编辑模式下，如果知识点不在当前列表中，可能需要重新加载正确的层级数据
        if (isEditMode && formData.knowledgePointId) {
          console.log('重新加载知识点层级信息...');
          loadHierarchyInfo(formData.knowledgePointId);
        }
      }
    }
  }, [formData.knowledgePointId, knowledgePoints, isEditMode]);

  // 初始化表单数据
  useEffect(() => {
    const initializeFormData = async () => {
      if (question) {
        // 编辑模式：填充现有题目数据
        console.log('编辑模式，题目数据:', question);
        console.log('题目body.subject:', question.body.subject);

        // 中文科目名称到英文枚举值的映射
        const subjectMapping: Record<string, SubjectEnum> = {
          '英语': 'ENGLISH',
          '数学': 'MATH',
          '物理': 'PHYSICS',
          '化学': 'CHEMISTRY'
        };

        // 首先尝试从body中获取subject
        let mappedSubject: SubjectEnum = subjectMapping[question.body.subject] || question.body.subject;

        // 如果body中没有subject信息，从知识点关联信息中获取
        if (!mappedSubject) {
          console.log('body中没有subject信息，从知识点关联信息中获取...');
          setIsEditMode(true); // 先进入编辑模式
          const subjectFromHierarchy = await loadHierarchyInfo(question.knowledgePointId);
          if (subjectFromHierarchy) {
            mappedSubject = subjectFromHierarchy as SubjectEnum;
            console.log('从层级信息获取的科目:', mappedSubject);
          }
        } else {
          // 如果有subject信息，仍然需要加载层级信息用于界面显示
          setIsEditMode(true); // 进入编辑模式
          loadHierarchyInfo(question.knowledgePointId);
        }

        // 确保mappedSubject是有效的SubjectEnum值，否则使用默认值
        const validSubject: SubjectEnum = (['ENGLISH', 'MATH', 'PHYSICS', 'CHEMISTRY'] as SubjectEnum[]).includes(mappedSubject)
          ? mappedSubject
          : 'ENGLISH';

        console.log('最终使用的科目:', validSubject);

        setFormData({
          knowledgePointId: question.knowledgePointId,
          questionType: question.questionType,
          subject: validSubject,
          difficulty: question.body.difficulty,
          tags: question.body.tags || [],
          content: question.body.content,
          answer: question.body.answer,
          explanation: question.body.explanation,
          options: question.body.options || ['', '', '', ''],
          material: question.body.material || '',
          subQuestions: question.body.subQuestions || [],
          enabled: question.enabled,
        });
      } else {
        // 添加模式：重置为默认数据
        setFormData({
          knowledgePointId: 0,
          questionType: 'SINGLE_CHOICE',
          subject: 'ENGLISH',
          difficulty: 'EASY',
          tags: [],
          content: '',
          answer: '',
          explanation: '',
          options: ['', '', '', ''],
          material: '',
          subQuestions: [],
          enabled: true,
        });
        setIsEditMode(false); // 退出编辑模式
        // 重置层级选择
        setSelectedSubjectId(null);
        setSelectedSubjectVersionId(null);
        setSelectedChapterId(null);
      }
    };

    initializeFormData();
  }, [question]);

  // 加载知识点层级信息，并返回科目信息
  const loadHierarchyInfo = async (knowledgePointId: number): Promise<string | null> => {
    try {
      const hierarchyInfo: KnowledgePointHierarchyInfo = await curriculumAPI.getKnowledgePointHierarchy(knowledgePointId);

      // 并行加载所有数据
      const [versionsData, chaptersData, knowledgePointsData] = await Promise.all([
        curriculumAPI.getVersionsBySubjectId(hierarchyInfo.subjectId),
        curriculumAPI.getChaptersBySubjectVersionId(hierarchyInfo.subjectVersionId),
        curriculumAPI.getKnowledgePointsByChapterId(hierarchyInfo.chapterId)
      ]);

      // 一次性设置所有数据
      setSelectedSubjectId(hierarchyInfo.subjectId);
      setSubjectVersions(versionsData);
      setSelectedSubjectVersionId(hierarchyInfo.subjectVersionId);
      setChapters(chaptersData);
      setSelectedChapterId(hierarchyInfo.chapterId);
      setKnowledgePoints(knowledgePointsData);

      // 验证知识点是否在加载的列表中
      const targetKnowledgePoint = knowledgePointsData.find((kp: KnowledgePoint) => kp.id === knowledgePointId);
      if (!targetKnowledgePoint) {
        console.warn(`知识点ID ${knowledgePointId} 不在章节 ${hierarchyInfo.chapterId} 的知识点列表中`);
        // 尝试直接获取知识点信息
        try {
          const knowledgePointInfo = await curriculumAPI.getKnowledgePointById(knowledgePointId);
          if (knowledgePointInfo) {
            // 将知识点添加到列表中
            setKnowledgePoints(prev => [...prev, knowledgePointInfo]);
          }
        } catch (kpError) {
          console.error('获取知识点详情失败:', kpError);
        }
      }

      // 获取科目信息并返回英文枚举值
      const subject = subjects.find(s => s.id === hierarchyInfo.subjectId);
      if (subject) {
        const subjectMapping: Record<string, string> = {
          '英语': 'ENGLISH',
          '数学': 'MATH',
          '物理': 'PHYSICS',
          '化学': 'CHEMISTRY'
        };
        return subjectMapping[subject.name] || subject.name;
      }

      return null;

    } catch (error) {
      console.error('加载知识点层级信息失败:', error);
      setError('加载知识点层级信息失败');
      return null;
    }
  };



  // 判断是否为嵌套题型
  const isNestedQuestion = (type: QuestionType): boolean => {
    return ['READING_COMPREHENSION', 'LISTENING', 'CLOZE_TEST'].includes(type);
  };

  // 判断是否为选择题
  const isChoiceQuestion = (type: QuestionType): boolean => {
    return ['SINGLE_CHOICE', 'MULTIPLE_CHOICE'].includes(type);
  };

  // 处理数组答案的添加
  const handleAddAnswerItem = () => {
    const currentAnswer = Array.isArray(formData.answer) ? formData.answer : [];
    const newAnswer = [...currentAnswer, ''];
    handleFieldChange('answer', newAnswer);
  };

  // 处理数组答案的删除
  const handleRemoveAnswerItem = (index: number) => {
    const currentAnswer = Array.isArray(formData.answer) ? formData.answer : [];
    const newAnswer = currentAnswer.filter((_, i) => i !== index);
    handleFieldChange('answer', newAnswer);
  };

  // 处理数组答案项的修改
  const handleAnswerItemChange = (index: number, value: string) => {
    const currentAnswer = Array.isArray(formData.answer) ? formData.answer : [];
    const newAnswer = [...currentAnswer];
    newAnswer[index] = value;
    handleFieldChange('answer', newAnswer);
  };

  // 确保答案是数组格式
  const ensureAnswerArray = () => {
    if (!Array.isArray(formData.answer)) {
      handleFieldChange('answer', formData.answer ? [formData.answer] : ['']);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof QuestionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // 题型变化时重置相关字段
    if (field === 'questionType') {
      const newType = value as QuestionType;
      if (isChoiceQuestion(newType) && !formData.options?.length) {
        setFormData(prev => ({ ...prev, options: ['', '', '', ''] }));
      }
      if (isNestedQuestion(newType) && !formData.subQuestions?.length) {
        setFormData(prev => ({ ...prev, subQuestions: [] }));
      }
      // 多选题和填空题需要数组格式的答案
      if (newType === 'MULTIPLE_CHOICE' || newType === 'FILL_IN_BLANK') {
        if (!Array.isArray(formData.answer)) {
          setFormData(prev => ({ ...prev, answer: formData.answer ? [formData.answer] : [''] }));
        }
      }
    }
  };

  // 处理选项变化
  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...(formData.options || [])];
    newOptions[index] = value;
    setFormData(prev => ({ ...prev, options: newOptions }));
  };

  // 添加选项
  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...(prev.options || []), '']
    }));
  };

  // 删除选项
  const removeOption = (index: number) => {
    const newOptions = formData.options?.filter((_, i) => i !== index) || [];
    setFormData(prev => ({ ...prev, options: newOptions }));
  };

  // 添加标签
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  // 删除标签
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // 添加子题目
  const addSubQuestion = () => {
    const newSubQuestion: SubQuestion = {
      id: `sub_${Date.now()}`,
      type: 'SINGLE_CHOICE',
      content: '',
      options: ['', '', '', ''],
      answer: '',
      explanation: '',
    };
    setFormData(prev => ({
      ...prev,
      subQuestions: [...(prev.subQuestions || []), newSubQuestion]
    }));
  };

  // 删除子题目
  const removeSubQuestion = (index: number) => {
    setFormData(prev => ({
      ...prev,
      subQuestions: prev.subQuestions?.filter((_, i) => i !== index) || []
    }));
  };

  // 处理子题目变化
  const handleSubQuestionChange = (index: number, field: keyof SubQuestion, value: any) => {
    const newSubQuestions = [...(formData.subQuestions || [])];
    newSubQuestions[index] = { ...newSubQuestions[index], [field]: value };
    setFormData(prev => ({ ...prev, subQuestions: newSubQuestions }));
  };

  // 将SubQuestion[]转换为后端期望的Map<String, Object>[]格式
  const convertSubQuestionsToMap = (subQuestions?: SubQuestion[]): any[] | undefined => {
    if (!subQuestions || subQuestions.length === 0) {
      return undefined;
    }

    return subQuestions.map(subQ => {
      const subQuestionMap: any = {
        id: subQ.id,
        content: subQ.content,
        answer: subQ.answer
      };

      // 可选字段
      if (subQ.type) {
        subQuestionMap.type = subQ.type;
      }
      if (subQ.options && subQ.options.length > 0) {
        subQuestionMap.options = subQ.options.filter(opt => opt.trim());
      }
      if (subQ.explanation) {
        subQuestionMap.explanation = subQ.explanation;
      }

      return subQuestionMap;
    });
  };

  // 移除了stripHtmlTags函数，现在使用普通文本

  // 验证表单
  const validateForm = (): string | null => {
    // 验证知识点是否已选择
    if (!formData.knowledgePointId) {
      return '请选择知识点';
    }

    if (!formData.content.trim()) {
      return '题目内容不能为空';
    }

    if (isChoiceQuestion(formData.questionType)) {
      const validOptions = formData.options?.filter(opt => opt.trim()) || [];
      if (validOptions.length < 2) {
        return '选择题至少需要2个选项';
      }
      if (!formData.answer) {
        return '请设置正确答案';
      }
    }

    if (isNestedQuestion(formData.questionType)) {
      if (formData.questionType !== 'LISTENING' && !(formData.material || '').trim()) {
        return '阅读理解和完形填空需要提供材料内容';
      }
      if (!formData.subQuestions?.length) {
        return '嵌套题型至少需要一个子题目';
      }
      // 验证子题目内容
      for (let i = 0; i < formData.subQuestions.length; i++) {
        const subQ = formData.subQuestions[i];
        if (!subQ.content.trim()) {
          return `第${i + 1}个子题目内容不能为空`;
        }
        if (!subQ.answer) {
          return `第${i + 1}个子题目答案不能为空`;
        }
      }
    }

    return null;
  };

  // 构建题目Body数据
  const buildQuestionBody = (content?: string, explanation?: string) => {
    const finalContent = content || formData.content;
    const finalExplanation = explanation || formData.explanation;
    console.log('构建题目Body数据，formData.subject:', formData.subject);

    // 确保subject字段不为空
    if (!formData.subject) {
      throw new Error('科目信息缺失，请重新选择知识点');
    }

    const body: any = {
      type: formData.questionType,
      id: question?.id ? question.body.id : Date.now(), // 编辑模式使用原ID，创建模式使用时间戳
      subject: formData.subject,
      difficulty: formData.difficulty,
      tags: formData.tags,
      content: finalContent,
      answer: formData.answer,
      explanation: finalExplanation,
    };

    console.log('构建的body对象:', body);

    if (isChoiceQuestion(formData.questionType)) {
      body.options = formData.options?.filter(opt => opt.trim()) || [];
    }

    if (isNestedQuestion(formData.questionType)) {
      body.material = formData.material;
      body.subQuestions = convertSubQuestionsToMap(formData.subQuestions);
    }

    return body;
  };

  // 表单提交处理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }
    
    setLoading(true);
    setError(null);

    try {
      // 1. 如果有待上传文件，先上传文件
      let processedContent = formData.content;
      let processedExplanation = formData.explanation;

      if (pendingFiles.length > 0) {
        console.log('开始上传文件，共', pendingFiles.length, '个文件');

        // 上传文件
        const uploadResults = await uploadPendingFiles(
          pendingFiles,
          (current, total) => setUploadProgress({ current, total })
        );

        // 检查是否有上传失败的文件
        const failedUploads = Array.from(uploadResults.entries())
          .filter(([_, result]) => !result.success);

        if (failedUploads.length > 0) {
          const errorMessages = failedUploads.map(([fileId, result]) =>
            `文件 ${fileId}: ${result.error}`
          ).join('\n');
          throw new Error(`文件上传失败:\n${errorMessages}`);
        }

        // 替换内容中的临时URL为真实URL
        console.log('开始替换URL，原始内容:', formData.content);
        processedContent = replaceTemporaryUrls(formData.content, uploadResults);
        processedExplanation = replaceTemporaryUrls(formData.explanation, uploadResults);
        console.log('URL替换完成，处理后内容:', processedContent);

        // 清理临时URL
        cleanupTempUrls(pendingFiles);
        setPendingFiles([]);
        setUploadProgress(null);

        console.log('文件上传完成，URL已替换');
      }

      // 2. 构建完全符合 QuestionBody 结构的 `body` 对象
      const questionBody = buildQuestionBody(processedContent, processedExplanation);

      if (question?.id) {
        // 2. 更新题目
        const requestData: QuestionUpdateRequest = {
          knowledgePointId: formData.knowledgePointId,
          questionType: formData.questionType,
          body: questionBody,
          enabled: formData.enabled
        };
        await questionAPI.updateQuestion(question.id, requestData);
      } else {
        // 3. 使用专门的表单创建API - 使用处理后的内容
        const formRequestData = {
          knowledgePointId: formData.knowledgePointId, // 通过这个ID就能查到所属科目
          questionType: formData.questionType,
          content: processedContent, // 使用处理后的内容
          answer: formData.answer,
          explanation: processedExplanation, // 使用处理后的解析
          difficulty: formData.difficulty,
          tags: formData.tags,
          options: isChoiceQuestion(formData.questionType) ? formData.options?.filter(opt => opt.trim()) : undefined,
          material: isNestedQuestion(formData.questionType) ? formData.material : undefined,
          subQuestions: isNestedQuestion(formData.questionType) ? convertSubQuestionsToMap(formData.subQuestions) : undefined,
          enabled: formData.enabled
        };
        await questionAPI.createQuestionFromForm(formRequestData);
      }
      
      onSubmit();
    } catch (error) {
      setError('保存题目失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理知识点选择
  const handleKnowledgePointChange = (event: any) => {
    const knowledgePointId = Number(event.target.value);
    setFormData(prev => ({
      ...prev,
      knowledgePointId: knowledgePointId
    }));
  };

  // 根据科目ID获取科目名称
  const getSubjectNameById = (subjectId: number | null): string => {
    if (!subjectId) return '';
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : '';
  };

  return (
      <Box sx={{ p: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

      {/* 基础信息 */}
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
        基础信息
      </Typography>

      {/* 层级选择：科目 → 版本 → 章节 → 知识点 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          选择知识点
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
          <FormControl fullWidth>
            <InputLabel>科目</InputLabel>
            <Select
              value={selectedSubjectId || ''}
              label="科目"
              onChange={handleSubjectChange as (event: SelectChangeEvent<string | number>, child: React.ReactNode) => void}
            >
              <MenuItem value="">请选择科目</MenuItem>
              {subjects.map((subject) => (
                <MenuItem key={subject.id} value={subject.id}>
                  {subject.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth disabled={!selectedSubjectId}>
            <InputLabel>版本</InputLabel>
            <Select
              value={selectedSubjectVersionId || ''}
              label="版本"
              onChange={(e) => {
                setIsEditMode(false); // 用户手动选择时退出编辑模式
                setSelectedSubjectVersionId(e.target.value as number);
              }}
            >
              <MenuItem value="">请选择版本</MenuItem>
              {subjectVersions.map((version) => (
                <MenuItem key={version.id} value={version.id}>
                  {version.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth disabled={!selectedSubjectVersionId}>
            <InputLabel>章节</InputLabel>
            <Select
              value={selectedChapterId || ''}
              label="章节"
              onChange={(e) => {
                setIsEditMode(false); // 用户手动选择时退出编辑模式
                setSelectedChapterId(e.target.value as number);
              }}
            >
              <MenuItem value="">请选择章节</MenuItem>
              {chapters.map((chapter) => (
                <MenuItem key={chapter.id} value={chapter.id}>
                  {chapter.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth disabled={!selectedChapterId} required>
            <InputLabel>知识点</InputLabel>
            <Select
              value={(() => {
                // 确保选中的值在可用选项中
                const currentValue = formData.knowledgePointId || '';
                if (currentValue === '') return '';

                // 检查当前值是否在知识点列表中
                const isValidValue = knowledgePoints.some(kp => kp.id === currentValue);
                return isValidValue ? currentValue : '';
              })()}
              label="知识点"
              onChange={handleKnowledgePointChange}
            >
              <MenuItem value="">请选择知识点</MenuItem>
              {knowledgePoints.map((knowledgePoint) => (
                <MenuItem key={knowledgePoint.id} value={knowledgePoint.id}>
                  {knowledgePoint.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <FormControl fullWidth>
          <InputLabel>题型</InputLabel>
          <Select
            value={formData.questionType}
            label="题型"
            onChange={(e) => handleFieldChange('questionType', e.target.value)}
          >
            <MenuItem value="SINGLE_CHOICE">单选题</MenuItem>
            <MenuItem value="MULTIPLE_CHOICE">多选题</MenuItem>
            <MenuItem value="FILL_IN_BLANK">填空题</MenuItem>
            <MenuItem value="TRUE_FALSE">判断题</MenuItem>
            <MenuItem value="MATCHING">匹配题</MenuItem>
            <MenuItem value="READING_COMPREHENSION">阅读理解</MenuItem>
            <MenuItem value="CLOZE_TEST">完形填空</MenuItem>
            <MenuItem value="LISTENING">听力题</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel>难度</InputLabel>
          <Select
            value={formData.difficulty}
            label="难度"
            onChange={(e) => handleFieldChange('difficulty', e.target.value)}
          >
            <MenuItem value="EASY">简单</MenuItem>
            <MenuItem value="MEDIUM">中等</MenuItem>
            <MenuItem value="HARD">困难</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* 标签 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          标签
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
          {formData.tags.map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              onDelete={() => removeTag(tag)}
              size="small"
            />
          ))}
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            size="small"
            placeholder="添加标签"
            value={tagInput}
            onChange={(e) => setTagInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addTag()}
          />
          <Button size="small" onClick={addTag}>添加</Button>
        </Box>
      </Box>

      {/* 嵌套题型的材料内容 */}
      {isNestedQuestion(formData.questionType) && formData.questionType !== 'LISTENING' && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <TextField
              fullWidth
              multiline
              rows={6}
              label="材料内容 *"
              placeholder="请输入阅读材料或完形填空内容..."
              value={formData.material || ''}
              onChange={(e) => handleFieldChange('material', e.target.value)}
              error={!formData.material?.trim()}
              helperText={!formData.material?.trim() ? "材料内容不能为空" : "阅读理解和完形填空的材料内容"}
            />
            <Box sx={{ mt: 1 }}>
              <RichTextButton
                value={formData.material || ''}
                onChange={(value) => handleFieldChange('material', value)}
                title="富文本编辑材料内容"
                placeholder="请输入阅读材料或完形填空内容..."
              />
            </Box>
          </Box>
        </Box>
      )}

      {/* 题目内容 */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label={isNestedQuestion(formData.questionType) ? '题目说明 *' : '题目内容 *'}
            placeholder="请输入题目内容..."
            value={formData.content}
            onChange={(e) => handleFieldChange('content', e.target.value)}
            error={!formData.content.trim()}
            helperText={!formData.content.trim() ? "题目内容不能为空" : "题目的详细描述"}
          />
          <Box sx={{ mt: 1 }}>
            <RichTextButton
              value={formData.content}
              onChange={(value) => handleFieldChange('content', value)}
              onFilesReady={(files) => setPendingFiles(prev => [...prev, ...files])}
              title="富文本编辑题目内容"
              placeholder="请输入题目内容..."
            />
          </Box>
        </Box>
      </Box>

      {/* 选择题选项 */}
      {isChoiceQuestion(formData.questionType) && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            选项
          </Typography>
          {formData.options?.map((option, index) => (
            <Box key={index} sx={{ display: 'flex', gap: 1, mb: 2, alignItems: 'flex-start' }}>
              <Box sx={{ flex: 1, display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder={`选项 ${String.fromCharCode(65 + index)}`}
                  value={option}
                  onChange={(e) => handleOptionChange(index, e.target.value)}
                  multiline
                  rows={2}
                />
                <Box sx={{ mt: 0.5 }}>
                  <RichTextButton
                    value={option}
                    onChange={(value) => handleOptionChange(index, value)}
                    title={`富文本编辑选项${String.fromCharCode(65 + index)}`}
                    placeholder={`请输入选项${String.fromCharCode(65 + index)}内容...`}
                    size="small"
                  />
                </Box>
              </Box>
              {formData.options && formData.options.length > 2 && (
                <IconButton
                  size="small"
                  color="error"
                  onClick={() => removeOption(index)}
                  sx={{ mt: 0.5 }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>
          ))}
          <Button
            size="small"
            startIcon={<AddIcon />}
            onClick={addOption}
          >
            添加选项
          </Button>
        </Box>
      )}

      {/* 答案 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          答案
        </Typography>
        {formData.questionType === 'SINGLE_CHOICE' && (
          <FormControl fullWidth>
            <InputLabel>正确答案</InputLabel>
            <Select
              value={formData.answer}
              label="正确答案"
              onChange={(e) => handleFieldChange('answer', e.target.value)}
            >
              {formData.options?.map((option, index) => (
                <MenuItem key={index} value={String.fromCharCode(65 + index)} disabled={!option.trim()}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: '20px' }}>
                      {String.fromCharCode(65 + index)}:
                    </Typography>
                    <Box sx={{ flex: 1, maxWidth: '300px' }}>
                      {option.trim() ? (
                        <UnifiedRenderer
                          content={option}
                          inline={true}
                          style={{
                            fontSize: '14px',
                            lineHeight: '1.4',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}
                          onError={() => {}}
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          (空选项)
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
        {formData.questionType === 'MULTIPLE_CHOICE' && (
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              多选答案选项：
            </Typography>
            {(() => {
              // 确保答案是数组格式
              const answerArray = Array.isArray(formData.answer) ? formData.answer : (formData.answer ? [formData.answer] : ['']);

              return (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {answerArray.map((item, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TextField
                        size="small"
                        placeholder={`选项 ${index + 1}`}
                        value={item}
                        onChange={(e) => handleAnswerItemChange(index, e.target.value)}
                        sx={{ flexGrow: 1 }}
                      />
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemoveAnswerItem(index)}
                        disabled={answerArray.length <= 1}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  ))}
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={handleAddAnswerItem}
                    sx={{ alignSelf: 'flex-start' }}
                  >
                    添加选项
                  </Button>
                </Box>
              );
            })()}
          </Box>
        )}
        {formData.questionType === 'TRUE_FALSE' && (
          <FormControl fullWidth>
            <InputLabel>正确答案</InputLabel>
            <Select
              value={formData.answer === true ? 'true' : formData.answer === false ? 'false' : ''}
              label="正确答案"
              onChange={(e) => handleFieldChange('answer', e.target.value === 'true')}
            >
              <MenuItem value="true">正确</MenuItem>
              <MenuItem value="false">错误</MenuItem>
            </Select>
          </FormControl>
        )}
        {formData.questionType === 'FILL_IN_BLANK' && (
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              填空答案：
            </Typography>
            {(() => {
              // 确保答案是数组格式
              const answerArray = Array.isArray(formData.answer) ? formData.answer : (formData.answer ? [formData.answer] : ['']);

              return (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {answerArray.map((item, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TextField
                        size="small"
                        placeholder={`第 ${index + 1} 个空的答案`}
                        value={item}
                        onChange={(e) => handleAnswerItemChange(index, e.target.value)}
                        sx={{ flexGrow: 1 }}
                      />
                      <RichTextButton
                        value={item}
                        onChange={(value) => handleAnswerItemChange(index, value)}
                        title={`富文本编辑第 ${index + 1} 个空的答案`}
                        placeholder={`第 ${index + 1} 个空的答案`}
                      />
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemoveAnswerItem(index)}
                        disabled={answerArray.length <= 1}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  ))}
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={handleAddAnswerItem}
                    sx={{ alignSelf: 'flex-start' }}
                  >
                    添加答案
                  </Button>
                </Box>
              );
            })()}
          </Box>
        )}
        {!isChoiceQuestion(formData.questionType) &&
         formData.questionType !== 'TRUE_FALSE' &&
         formData.questionType !== 'FILL_IN_BLANK' &&
         !isNestedQuestion(formData.questionType) && (
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <TextField
              fullWidth
              placeholder="请输入答案"
              value={formData.answer}
              onChange={(e) => handleFieldChange('answer', e.target.value)}
            />
            <Box sx={{ mt: 1 }}>
              <RichTextButton
                value={formData.answer}
                onChange={(value) => handleFieldChange('answer', value)}
                title="富文本编辑答案内容"
                placeholder="请输入答案..."
              />
            </Box>
          </Box>
        )}
      </Box>

      {/* 子题目（嵌套题型） */}
      {isNestedQuestion(formData.questionType) && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              子题目
            </Typography>
            <Button
              size="small"
              startIcon={<AddIcon />}
              onClick={addSubQuestion}
            >
              添加子题目
            </Button>
          </Box>

          {formData.subQuestions?.map((subQ, index) => (
            <Accordion key={index} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>子题目 {index + 1}</Typography>
                <IconButton
                  size="small"
                  color="error"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeSubQuestion(index);
                  }}
                  sx={{ ml: 'auto', mr: 1 }}
                >
                  <DeleteIcon />
                </IconButton>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {/* 子题目类型选择 */}
                  <FormControl fullWidth>
                    <InputLabel>子题目类型</InputLabel>
                    <Select
                      value={subQ.type || 'SINGLE_CHOICE'}
                      onChange={(e) => {
                        const newType = e.target.value as QuestionType;
                        handleSubQuestionChange(index, 'type', newType);
                        // 根据题型设置默认选项
                        if (newType === 'SINGLE_CHOICE' || newType === 'MULTIPLE_CHOICE') {
                          if (!subQ.options || subQ.options.length === 0) {
                            handleSubQuestionChange(index, 'options', ['', '', '', '']);
                          }
                        } else {
                          handleSubQuestionChange(index, 'options', undefined);
                        }
                      }}
                    >
                      <MenuItem value="SINGLE_CHOICE">单选题</MenuItem>
                      <MenuItem value="MULTIPLE_CHOICE">多选题</MenuItem>
                      <MenuItem value="FILL_IN_BLANK">填空题</MenuItem>
                      <MenuItem value="TRUE_FALSE">判断题</MenuItem>
                    </Select>
                  </FormControl>

                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="题目内容 *"
                      placeholder="请输入子题目内容..."
                      value={subQ.content}
                      onChange={(e) => handleSubQuestionChange(index, 'content', e.target.value)}
                      error={!subQ.content.trim()}
                      helperText={!subQ.content.trim() ? "子题目内容不能为空" : ""}
                    />
                    <RichTextButton
                      value={subQ.content}
                      onChange={(value) => handleSubQuestionChange(index, 'content', value)}
                      title={`富文本编辑子题目${index + 1}内容`}
                      placeholder="请输入子题目内容..."
                      size="small"
                    />
                  </Box>

                  {/* 选择题选项 */}
                  {(subQ.type === 'SINGLE_CHOICE' || subQ.type === 'MULTIPLE_CHOICE' || (!subQ.type && subQ.options)) && (
                    <Box>
                      <Typography variant="body2" gutterBottom>选项</Typography>
                      {(subQ.options || ['', '', '', '']).map((opt, optIndex) => (
                        <Box key={optIndex} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
                          <TextField
                            fullWidth
                            size="small"
                            placeholder={`选项 ${String.fromCharCode(65 + optIndex)}`}
                            value={opt}
                            onChange={(e) => {
                              const newOptions = [...(subQ.options || ['', '', '', ''])];
                              newOptions[optIndex] = e.target.value;
                              handleSubQuestionChange(index, 'options', newOptions);
                            }}
                            multiline
                            rows={2}
                          />
                          <RichTextButton
                            value={opt}
                            onChange={(value) => {
                              const newOptions = [...(subQ.options || ['', '', '', ''])];
                              newOptions[optIndex] = value;
                              handleSubQuestionChange(index, 'options', newOptions);
                            }}
                            title={`富文本编辑子题目${index + 1}选项${String.fromCharCode(65 + optIndex)}`}
                            placeholder={`请输入选项${String.fromCharCode(65 + optIndex)}内容...`}
                            size="small"
                          />
                        </Box>
                      ))}
                    </Box>
                  )}

                  {/* 答案输入 */}
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    {(subQ.type === 'SINGLE_CHOICE' || (!subQ.type && subQ.options)) ? (
                      // 单选题：下拉选择
                      <FormControl fullWidth>
                        <InputLabel>正确答案 *</InputLabel>
                        <Select
                          value={subQ.answer || ''}
                          onChange={(e) => handleSubQuestionChange(index, 'answer', e.target.value)}
                          error={!subQ.answer}
                        >
                          {(subQ.options || ['', '', '', '']).map((opt, optIndex) => (
                            <MenuItem key={optIndex} value={String.fromCharCode(65 + optIndex)} disabled={!opt.trim()}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                                <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: '20px' }}>
                                  {String.fromCharCode(65 + optIndex)}:
                                </Typography>
                                <Box sx={{ flex: 1, maxWidth: '300px' }}>
                                  {opt.trim() ? (
                                    <UnifiedRenderer
                                      content={opt}
                                      inline={true}
                                      style={{
                                        fontSize: '14px',
                                        lineHeight: '1.4',
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis'
                                      }}
                                      onError={() => {}}
                                    />
                                  ) : (
                                    <Typography variant="body2" color="text.secondary">
                                      (空选项)
                                    </Typography>
                                  )}
                                </Box>
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    ) : subQ.type === 'MULTIPLE_CHOICE' ? (
                      // 多选题：多选框
                      <FormControl fullWidth>
                        <InputLabel>正确答案 * (多选)</InputLabel>
                        <Select
                          multiple
                          value={Array.isArray(subQ.answer) ? subQ.answer : (subQ.answer ? [subQ.answer] : [])}
                          onChange={(e) => handleSubQuestionChange(index, 'answer', e.target.value)}
                          error={!subQ.answer || (Array.isArray(subQ.answer) && subQ.answer.length === 0)}
                        >
                          {(subQ.options || ['', '', '', '']).map((opt, optIndex) => (
                            <MenuItem key={optIndex} value={String.fromCharCode(65 + optIndex)} disabled={!opt.trim()}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                                <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: '20px' }}>
                                  {String.fromCharCode(65 + optIndex)}:
                                </Typography>
                                <Box sx={{ flex: 1, maxWidth: '300px' }}>
                                  {opt.trim() ? (
                                    <UnifiedRenderer
                                      content={opt}
                                      inline={true}
                                      style={{
                                        fontSize: '14px',
                                        lineHeight: '1.4',
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis'
                                      }}
                                      onError={() => {}}
                                    />
                                  ) : (
                                    <Typography variant="body2" color="text.secondary">
                                      (空选项)
                                    </Typography>
                                  )}
                                </Box>
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    ) : subQ.type === 'TRUE_FALSE' ? (
                      // 判断题：是/否选择
                      <FormControl fullWidth>
                        <InputLabel>正确答案 *</InputLabel>
                        <Select
                          value={subQ.answer || ''}
                          onChange={(e) => handleSubQuestionChange(index, 'answer', e.target.value)}
                          error={!subQ.answer}
                        >
                          <MenuItem value="true">正确</MenuItem>
                          <MenuItem value="false">错误</MenuItem>
                        </Select>
                      </FormControl>
                    ) : (
                      // 填空题：文本输入
                      <TextField
                        fullWidth
                        label="答案 *"
                        placeholder="请输入正确答案..."
                        value={subQ.answer || ''}
                        onChange={(e) => handleSubQuestionChange(index, 'answer', e.target.value)}
                        error={!subQ.answer}
                        helperText={!subQ.answer ? "答案不能为空" : ""}
                      />
                    )}

                    {/* 富文本编辑按钮（仅填空题显示） */}
                    {subQ.type === 'FILL_IN_BLANK' && (
                      <Box sx={{ mt: 1 }}>
                        <RichTextButton
                          value={subQ.answer || ''}
                          onChange={(value) => handleSubQuestionChange(index, 'answer', value)}
                          title="富文本编辑子题目答案"
                          placeholder="请输入子题目答案..."
                          size="small"
                        />
                      </Box>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="解析"
                      placeholder="请输入子题目解析..."
                      value={subQ.explanation || ''}
                      onChange={(e) => handleSubQuestionChange(index, 'explanation', e.target.value)}
                    />
                    <RichTextButton
                      value={subQ.explanation || ''}
                      onChange={(value) => handleSubQuestionChange(index, 'explanation', value)}
                      title={`富文本编辑子题目${index + 1}解析`}
                      placeholder="请输入子题目解析..."
                      size="small"
                    />
                  </Box>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}

      {/* 解析 */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="解析 *"
            placeholder="请输入题目解析..."
            value={formData.explanation}
            onChange={(e) => handleFieldChange('explanation', e.target.value)}
            error={!formData.explanation.trim()}
            helperText={!formData.explanation.trim() ? "解析内容不能为空" : "详细的解题思路和答案说明"}
          />
          <Box sx={{ mt: 1 }}>
            <RichTextButton
              value={formData.explanation}
              onChange={(value) => handleFieldChange('explanation', value)}
              title="富文本编辑解析内容"
              placeholder="请输入题目解析..."
            />
          </Box>
        </Box>
      </Box>

      {/* 启用状态 */}
      <Box sx={{ mb: 3 }}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.enabled}
              onChange={(e) => handleFieldChange('enabled', e.target.checked)}
            />
          }
          label="启用题目"
        />
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* 文件上传进度 */}
      {uploadProgress && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            正在上传文件 {uploadProgress.current}/{uploadProgress.total}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={(uploadProgress.current / uploadProgress.total) * 100}
          />
        </Box>
      )}

      {/* 待上传文件提示 */}
      {pendingFiles.length > 0 && !uploadProgress && (
        <Alert severity="info" sx={{ mb: 3 }}>
          检测到 {pendingFiles.length} 个待上传文件，提交时将自动上传
        </Alert>
      )}

      {/* 操作按钮 */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<CancelIcon />}
          onClick={onCancel}
          disabled={loading}
        >
          取消
        </Button>
        <Button
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          onClick={handleSubmit}
          disabled={loading}
        >
          {loading ? '保存中...' : (question ? '更新' : '创建')}
        </Button>
      </Box>
      </Box>
  );
};

export default QuestionForm;
