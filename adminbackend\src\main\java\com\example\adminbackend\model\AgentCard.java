package com.example.adminbackend.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 代理卡实体类
 * 用于存储代理账号可分配的卡信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agent_cards")
public class AgentCard {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 关联的代理用户
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private User agent;

    // 卡种类型
    @Column(nullable = false)
    private String cardType;

    // 有效天数
    private Integer validDays;

    // 卡类型：正式/试用
    @Column(nullable = false, name = "formal_or_trial")
    private String cardNature;

    // 卡总数量
    @Column(nullable = false)
    private Integer total;

    // 剩余数量
    @Column(nullable = false)
    private Integer remaining;

    // 创建时间
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    // 更新时间
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }
} 