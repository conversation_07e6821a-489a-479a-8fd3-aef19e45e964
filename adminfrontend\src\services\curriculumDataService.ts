import { curriculumAPI } from './api';

// 简化的数据类型定义
export interface SimpleSubject {
  id: number;
  name: string;
}

export interface SimpleChapter {
  id: number;
  name: string;
  subjectId: number;
  subjectName: string;
}

export interface SimpleKnowledgePoint {
  id: number;
  name: string;
  chapterId: number;
  chapterName: string;
  subjectName: string;
}

/**
 * 课程数据服务
 * 专门为题目数量管理功能提供简化的数据接口
 */
export class CurriculumDataService {
  
  /**
   * 获取所有科目
   */
  static async getSubjects(): Promise<SimpleSubject[]> {
    try {
      const response = await curriculumAPI.getSubjects();
      return response.map((subject: any) => ({
        id: subject.id,
        name: subject.name
      }));
    } catch (error) {
      console.error('获取科目列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有章节（包含科目信息）
   */
  static async getChapters(): Promise<SimpleChapter[]> {
    try {
      // 先获取科目列表
      const subjects = await this.getSubjects();
      const subjectMap = new Map(subjects.map(s => [s.id, s.name]));

      // 获取科目版本列表
      const subjectVersions = await curriculumAPI.getSubjectVersionsWithChapters();
      const versionToSubjectMap = new Map();
      
      subjectVersions.forEach((version: any) => {
        const subjectName = subjectMap.get(version.subjectId);
        if (subjectName) {
          versionToSubjectMap.set(version.id, {
            subjectId: version.subjectId,
            subjectName: subjectName
          });
        }
      });

      // 获取章节列表
      const chapters = await curriculumAPI.getChaptersWithKnowledgePoints();
      
      return chapters.map((chapter: any) => {
        const subjectInfo = versionToSubjectMap.get(chapter.subjectVersionId);
        return {
          id: chapter.id,
          name: chapter.name,
          subjectId: subjectInfo?.subjectId || 0,
          subjectName: subjectInfo?.subjectName || '未知科目'
        };
      });
    } catch (error) {
      console.error('获取章节列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据科目ID获取章节
   */
  static async getChaptersBySubjectId(subjectId: number): Promise<SimpleChapter[]> {
    const allChapters = await this.getChapters();
    return allChapters.filter(chapter => chapter.subjectId === subjectId);
  }

  /**
   * 获取所有知识点（包含章节和科目信息）
   */
  static async getKnowledgePoints(): Promise<SimpleKnowledgePoint[]> {
    try {
      // 先获取章节信息
      const chapters = await this.getChapters();
      const chapterMap = new Map(chapters.map(c => [c.id, c]));

      // 获取所有知识点
      const knowledgePointsData: SimpleKnowledgePoint[] = [];
      
      for (const chapter of chapters) {
        try {
          const kpResponse = await curriculumAPI.getKnowledgePointsByChapterId(chapter.id);
          const chapterKPs = kpResponse.map((kp: any) => ({
            id: kp.id,
            name: kp.name,
            chapterId: chapter.id,
            chapterName: chapter.name,
            subjectName: chapter.subjectName
          }));
          knowledgePointsData.push(...chapterKPs);
        } catch (error) {
          console.warn(`获取章节 ${chapter.name} 的知识点失败:`, error);
        }
      }

      return knowledgePointsData;
    } catch (error) {
      console.error('获取知识点列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据章节ID获取知识点
   */
  static async getKnowledgePointsByChapterId(chapterId: number): Promise<SimpleKnowledgePoint[]> {
    try {
      const chapters = await this.getChapters();
      const chapter = chapters.find(c => c.id === chapterId);
      
      if (!chapter) {
        throw new Error(`章节 ${chapterId} 不存在`);
      }

      const kpResponse = await curriculumAPI.getKnowledgePointsByChapterId(chapterId);
      return kpResponse.map((kp: any) => ({
        id: kp.id,
        name: kp.name,
        chapterId: chapter.id,
        chapterName: chapter.name,
        subjectName: chapter.subjectName
      }));
    } catch (error) {
      console.error(`获取章节 ${chapterId} 的知识点失败:`, error);
      throw error;
    }
  }

  /**
   * 根据科目ID获取知识点
   */
  static async getKnowledgePointsBySubjectId(subjectId: number): Promise<SimpleKnowledgePoint[]> {
    const allKnowledgePoints = await this.getKnowledgePoints();
    const chapters = await this.getChaptersBySubjectId(subjectId);
    const chapterIds = chapters.map(c => c.id);
    
    return allKnowledgePoints.filter(kp => chapterIds.includes(kp.chapterId));
  }

  /**
   * 搜索知识点
   */
  static async searchKnowledgePoints(keyword: string): Promise<SimpleKnowledgePoint[]> {
    const allKnowledgePoints = await this.getKnowledgePoints();
    return allKnowledgePoints.filter(kp => 
      kp.name.toLowerCase().includes(keyword.toLowerCase()) ||
      kp.chapterName.toLowerCase().includes(keyword.toLowerCase()) ||
      kp.subjectName.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 获取知识点的层级信息
   */
  static async getKnowledgePointHierarchy(knowledgePointId: number): Promise<{
    knowledgePoint: SimpleKnowledgePoint;
    chapter: SimpleChapter;
    subject: SimpleSubject;
  } | null> {
    try {
      const allKnowledgePoints = await this.getKnowledgePoints();
      const knowledgePoint = allKnowledgePoints.find(kp => kp.id === knowledgePointId);
      
      if (!knowledgePoint) {
        return null;
      }

      const chapters = await this.getChapters();
      const chapter = chapters.find(c => c.id === knowledgePoint.chapterId);
      
      if (!chapter) {
        return null;
      }

      const subjects = await this.getSubjects();
      const subject = subjects.find(s => s.id === chapter.subjectId);
      
      if (!subject) {
        return null;
      }

      return {
        knowledgePoint,
        chapter,
        subject
      };
    } catch (error) {
      console.error(`获取知识点 ${knowledgePointId} 的层级信息失败:`, error);
      throw error;
    }
  }
}

export default CurriculumDataService;
