import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Chip,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Avatar,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Psychology,
  Refresh,
  Image,
  VideoLibrary,
  CloudUpload,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { curriculumAPI } from '../services/api';
import { KnowledgePoint, Subject, Chapter, SubjectVersion } from '../types';
import VideoUploadDialog from '../components/VideoUploadDialog';
import VideoManageDialog from '../components/VideoManageDialog';

const KnowledgePointManagePage: React.FC = () => {
  const [knowledgePoints, setKnowledgePoints] = useState<KnowledgePoint[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingKnowledgePoint, setEditingKnowledgePoint] = useState<KnowledgePoint | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    orderIndex: 1,
    chapterId: 0,
    description: '',
    coverImageUrl: '',
    enabled: true,
  });
  const [selectedSubjectId, setSelectedSubjectId] = useState<number>(0);
  const [selectedSubjectVersionId, setSelectedSubjectVersionId] = useState<number>(0);
  const [availableVersions, setAvailableVersions] = useState<SubjectVersion[]>([]);
  const [availableChapters, setAvailableChapters] = useState<Chapter[]>([]);
  const [versionsLoading, setVersionsLoading] = useState(false);
  const [chaptersLoading, setChaptersLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [openVideoDialog, setOpenVideoDialog] = useState(false);
  const [openVideoManageDialog, setOpenVideoManageDialog] = useState(false);
  const [selectedKnowledgePointId, setSelectedKnowledgePointId] = useState<number | null>(null);
  const [imageUploading, setImageUploading] = useState(false);

  // 加载知识点列表
  const loadKnowledgePoints = async () => {
    try {
      setLoading(true);
      const data = await curriculumAPI.getKnowledgePoints();
      setKnowledgePoints(data);
    } catch (error: any) {
      console.error('加载知识点列表失败:', error);
      setError('加载知识点列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 加载学科列表
  const loadSubjects = async () => {
    try {
      const data = await curriculumAPI.getSubjects();
      setSubjects(data);
    } catch (error: any) {
      console.error('加载学科列表失败:', error);
    }
  };

  // 加载所有章节列表（用于显示层级信息）
  const loadAllChapters = async () => {
    try {
      const data = await curriculumAPI.getChapters();
      setChapters(data);
    } catch (error: any) {
      console.error('加载章节列表失败:', error);
    }
  };

  // 加载章节列表（用于下拉选择）
  const loadChapters = async (subjectId: number) => {
    try {
      const data = await curriculumAPI.getChaptersBySubjectId(subjectId);
      setChapters(data);
    } catch (error: any) {
      console.error('加载章节列表失败:', error);
    }
  };

  // 加载学科版本列表
  const loadSubjectVersions = async (subjectId: number) => {
    if (!subjectId) {
      setAvailableVersions([]);
      return;
    }

    try {
      setVersionsLoading(true);
      const data = await curriculumAPI.getSubjectVersions(subjectId);
      setAvailableVersions(data);
    } catch (error: any) {
      console.error('加载学科版本失败:', error);
      setError('加载学科版本失败: ' + (error.message || '未知错误'));
    } finally {
      setVersionsLoading(false);
    }
  };

  // 加载章节列表（根据学科版本ID）
  const loadChaptersByVersion = async (versionId: number) => {
    if (!versionId) {
      setAvailableChapters([]);
      return;
    }

    try {
      setChaptersLoading(true);
      const data = await curriculumAPI.getChaptersBySubjectVersionId(versionId);
      setAvailableChapters(data);
    } catch (error: any) {
      console.error('加载章节列表失败:', error);
      setError('加载章节列表失败: ' + (error.message || '未知错误'));
    } finally {
      setChaptersLoading(false);
    }
  };

  useEffect(() => {
    loadKnowledgePoints();
    loadSubjects();
    loadAllChapters(); // 加载所有章节用于显示层级信息
  }, []);

  // 监听学科选择变化，加载对应的版本列表
  useEffect(() => {
    if (selectedSubjectId > 0) {
      loadSubjectVersions(selectedSubjectId);
      // 重置版本和章节选择
      setSelectedSubjectVersionId(0);
      setAvailableChapters([]);
      setFormData(prev => ({ ...prev, chapterId: 0 }));
    } else {
      setAvailableVersions([]);
      setSelectedSubjectVersionId(0);
      setAvailableChapters([]);
      setFormData(prev => ({ ...prev, chapterId: 0 }));
    }
  }, [selectedSubjectId]);

  // 监听版本选择变化，加载对应的章节列表
  useEffect(() => {
    if (selectedSubjectVersionId > 0) {
      loadChaptersByVersion(selectedSubjectVersionId);
      // 重置章节选择
      setFormData(prev => ({ ...prev, chapterId: 0 }));
    } else {
      setAvailableChapters([]);
      setFormData(prev => ({ ...prev, chapterId: 0 }));
    }
  }, [selectedSubjectVersionId]);

  // 获取当前选择学科的章节列表 - 使用 useMemo 优化性能
  const chaptersBySelectedSubject = useMemo(() => {
    if (selectedSubjectId === 0) return [];

    const subject = subjects.find(s => s.id === selectedSubjectId);
    if (!subject || !subject.versions) return [];

    const versionIds = subject.versions.map(v => v.id);
    return chapters.filter(c => versionIds.includes(c.subjectVersionId || 0));
  }, [selectedSubjectId, subjects, chapters]);

  // 优化表单输入处理函数
  const handleFormDataChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFormDataChange('name', e.target.value);
  }, [handleFormDataChange]);

  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFormDataChange('description', e.target.value);
  }, [handleFormDataChange]);

  const handleCoverImageUrlChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFormDataChange('coverImageUrl', e.target.value);
  }, [handleFormDataChange]);

  const handleOrderIndexChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFormDataChange('orderIndex', Number(e.target.value));
  }, [handleFormDataChange]);

  const handleEnabledChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFormDataChange('enabled', e.target.checked);
  }, [handleFormDataChange]);

  const handleChapterIdChange = useCallback((e: any) => {
    handleFormDataChange('chapterId', Number(e.target.value));
  }, [handleFormDataChange]);

  const handleSubjectIdChange = useCallback((e: any) => {
    setSelectedSubjectId(Number(e.target.value));
  }, []);

  const handleSubjectVersionIdChange = useCallback((e: any) => {
    setSelectedSubjectVersionId(Number(e.target.value));
  }, []);

  // 打开视频上传对话框
  const handleAddVideo = (knowledgePointId: number) => {
    setSelectedKnowledgePointId(knowledgePointId);
    setOpenVideoDialog(true);
  };

  // 打开视频管理对话框
  const handleManageVideos = (knowledgePointId: number) => {
    setSelectedKnowledgePointId(knowledgePointId);
    setOpenVideoManageDialog(true);
  };

  // 处理视频上传成功
  const handleVideoUpload = async (videoUrl: string) => {
    if (!selectedKnowledgePointId) return;

    try {
      const result = await curriculumAPI.addVideoToKnowledgePoint(selectedKnowledgePointId, videoUrl);

      // 根据返回结果显示不同的成功消息
      if (result.success) {
        setSuccess(result.message || '视频添加成功');
      } else {
        setError(result.error || '视频添加失败');
        return;
      }

      await loadKnowledgePoints();
    } catch (error: any) {
      console.error('添加视频失败:', error);
      setError('添加视频失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理图片上传
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      setError('请选择图片文件');
      return;
    }

    // 检查文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('图片文件大小不能超过5MB');
      return;
    }

    setImageUploading(true);
    setError('');

    try {
      // 上传图片到七牛云
      const result = await curriculumAPI.uploadImage(file);

      if (result.success) {
        // 更新表单数据中的图片URL
        setFormData({ ...formData, coverImageUrl: result.url });
        setSuccess('图片上传成功');
      } else {
        setError(result.error || '图片上传失败');
      }
    } catch (error: any) {
      console.error('图片上传失败:', error);
      setError('图片上传失败: ' + (error.message || '未知错误'));
    } finally {
      setImageUploading(false);
      // 清空文件输入框
      event.target.value = '';
    }
  };

  // 移除视频
  const handleRemoveVideo = async (knowledgePointId: number) => {
    if (!window.confirm('确定要移除该知识点的视频吗？')) {
      return;
    }

    try {
      await curriculumAPI.removeVideoFromKnowledgePoint(knowledgePointId);
      setSuccess('视频移除成功');
      await loadKnowledgePoints();
    } catch (error: any) {
      console.error('移除视频失败:', error);
      setError('移除视频失败: ' + (error.message || '未知错误'));
    }
  };

  // 打开新增对话框
  const handleAdd = () => {
    setEditingKnowledgePoint(null);
    setFormData({
      name: '',
      orderIndex: 1,
      chapterId: 0,
      description: '',
      coverImageUrl: '',
      enabled: true,
    });
    setSelectedSubjectId(0);
    setSelectedSubjectVersionId(0);
    setAvailableVersions([]);
    setAvailableChapters([]);
    setOpenDialog(true);
  };

  // 打开编辑对话框
  const handleEdit = async (knowledgePoint: KnowledgePoint) => {
    setEditingKnowledgePoint(knowledgePoint);
    setFormData({
      name: knowledgePoint.name,
      orderIndex: knowledgePoint.orderIndex,
      chapterId: knowledgePoint.chapterId || 0,
      description: knowledgePoint.description || '',
      coverImageUrl: knowledgePoint.coverImageUrl || '',
      enabled: knowledgePoint.enabled ?? true,
    });

    // 找到对应的章节、学科版本和学科
    const chapter = chapters.find(c => c.id === knowledgePoint.chapterId);
    if (chapter && chapter.subjectVersionId) {
      // 找到学科
      const subject = subjects.find(s =>
        s.versions?.some(v => v.id === chapter.subjectVersionId)
      );

      if (subject) {
        // 设置学科ID
        setSelectedSubjectId(subject.id);

        // 加载该学科的版本列表
        try {
          const versions = await curriculumAPI.getSubjectVersions(subject.id);
          setAvailableVersions(versions);

          // 设置学科版本ID
          setSelectedSubjectVersionId(chapter.subjectVersionId);

          // 加载该版本的章节列表
          const chaptersData = await curriculumAPI.getChaptersBySubjectVersionId(chapter.subjectVersionId);
          setAvailableChapters(chaptersData);
        } catch (error) {
          console.error('加载编辑数据失败:', error);
        }
      }
    }

    setOpenDialog(true);
  };

  // 保存知识点
  const handleSave = async () => {
    if (!formData.name.trim()) {
      setError('知识点名称不能为空');
      return;
    }

    if (!selectedSubjectId) {
      setError('请选择学科');
      return;
    }

    if (!selectedSubjectVersionId) {
      setError('请选择学科版本');
      return;
    }

    if (!formData.chapterId) {
      setError('请选择章节');
      return;
    }

    try {
      if (editingKnowledgePoint) {
        // 更新知识点
        await curriculumAPI.updateKnowledgePoint(editingKnowledgePoint.id, {
          name: formData.name,
          orderIndex: formData.orderIndex,
          chapterId: formData.chapterId,
          description: formData.description,
          coverImageUrl: formData.coverImageUrl,
          enabled: formData.enabled,
        });
        setSuccess('知识点更新成功');
      } else {
        // 创建新知识点
        await curriculumAPI.createKnowledgePoint(formData);
        setSuccess('知识点创建成功');
      }

      setOpenDialog(false);
      await loadKnowledgePoints();
    } catch (error: any) {
      console.error('保存知识点失败:', error);
      setError('保存知识点失败: ' + (error.message || '未知错误'));
    }
  };

  // 删除知识点
  const handleDelete = async (knowledgePoint: KnowledgePoint) => {
    if (!window.confirm(`确定要删除知识点"${knowledgePoint.name}"吗？此操作不可恢复，将同时删除相关的封面图片和视频文件。`)) {
      return;
    }

    try {
      const result = await curriculumAPI.deleteKnowledgePoint(knowledgePoint.id);

      if (result.success) {
        setSuccess(result.message || '知识点删除成功');
        if (result.deletedFilesCount > 0) {
          console.log(`已删除 ${result.deletedFilesCount} 个七牛云文件`);
        }
      } else {
        setError(result.error || '删除知识点失败');
        return;
      }

      await loadKnowledgePoints();
    } catch (error: any) {
      console.error('删除知识点失败:', error);
      setError('删除知识点失败: ' + (error.message || '未知错误'));
    }
  };

  // 获取知识点的章节信息
  const getKnowledgePointChapterInfo = (knowledgePoint: KnowledgePoint) => {
    const chapter = chapters.find(c => c.id === knowledgePoint.chapterId);
    if (chapter) {
      const subject = subjects.find(s =>
        s.versions?.some(v => v.id === chapter.subjectVersionId)
      );
      if (subject) {
        const version = subject.versions?.find(v => v.id === chapter.subjectVersionId);
        return `${subject.name} - ${version?.name || '未知版本'} - ${chapter.name}`;
      }
      return `未知学科 - ${chapter.name}`;
    }
    return '未知章节';
  };

  return (
    <Layout>
      <Box sx={{ p: 3 }}>
        {/* 页面标题 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Psychology sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h4" fontWeight="bold">
              知识点管理
            </Typography>
          </Box>
          <Box>
            <Button
              startIcon={<Refresh />}
              onClick={loadKnowledgePoints}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              刷新
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleAdd}
            >
              新增知识点
            </Button>
          </Box>
        </Box>

        {/* 知识点列表 */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>封面</TableCell>
                  <TableCell>知识点名称</TableCell>
                  <TableCell>所属章节</TableCell>
                  <TableCell>排序</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>视频</TableCell>
                  <TableCell>创建时间</TableCell>
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : knowledgePoints.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <Typography color="text.secondary">
                        暂无知识点数据
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  knowledgePoints.map((knowledgePoint) => (
                    <TableRow key={knowledgePoint.id}>
                      <TableCell>{knowledgePoint.id}</TableCell>
                      <TableCell>
                        {knowledgePoint.coverImageUrl ? (
                          <Avatar
                            src={knowledgePoint.coverImageUrl}
                            sx={{ width: 40, height: 40 }}
                          >
                            <Image />
                          </Avatar>
                        ) : (
                          <Avatar sx={{ width: 40, height: 40, bgcolor: 'grey.300' }}>
                            <Image />
                          </Avatar>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography fontWeight="medium">
                          {knowledgePoint.name}
                        </Typography>
                        {knowledgePoint.description && (
                          <Typography variant="body2" color="text.secondary">
                            {knowledgePoint.description.length > 50
                              ? knowledgePoint.description.substring(0, 50) + '...'
                              : knowledgePoint.description
                            }
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {getKnowledgePointChapterInfo(knowledgePoint)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={knowledgePoint.orderIndex}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={knowledgePoint.enabled ? '启用' : '禁用'}
                          size="small"
                          color={knowledgePoint.enabled ? 'success' : 'default'}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {knowledgePoint.videoCollectionId ? (
                            <Chip
                              icon={<VideoLibrary />}
                              label={`有视频 (${knowledgePoint.videoCollectionName || ''})`}
                              size="small"
                              color="success"
                              variant="outlined"
                              title={`视频合集: ${knowledgePoint.videoCollectionName || ''}`}
                            />
                          ) : (
                            <Chip
                              label="无视频"
                              size="small"
                              color="default"
                              variant="outlined"
                            />
                          )}

                          {/* 管理视频按钮 - 有视频时显示 */}
                          {knowledgePoint.videoCollectionId ? (
                            <IconButton
                              size="small"
                              onClick={() => handleManageVideos(knowledgePoint.id)}
                              color="primary"
                              title="管理视频集合"
                            >
                              <VideoLibrary />
                            </IconButton>
                          ) : (
                            /* 添加视频按钮 - 无视频时显示 */
                            <IconButton
                              size="small"
                              onClick={() => handleAddVideo(knowledgePoint.id)}
                              color="primary"
                              title="添加视频"
                            >
                              <CloudUpload />
                            </IconButton>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {knowledgePoint.createdAt ? new Date(knowledgePoint.createdAt).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(knowledgePoint)}
                          color="primary"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(knowledgePoint)}
                          color="error"
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* 新增/编辑对话框 */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {editingKnowledgePoint ? '编辑知识点' : '新增知识点'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="知识点名称 *"
                value={formData.name}
                onChange={handleNameChange}
                sx={{ mb: 2 }}
              />
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>选择学科 *</InputLabel>
                <Select
                  value={selectedSubjectId}
                  onChange={handleSubjectIdChange}
                  label="选择学科 *"
                >
                  <MenuItem value={0}>请选择学科</MenuItem>
                  {subjects.map((subject) => (
                    <MenuItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>选择学科版本 *</InputLabel>
                <Select
                  value={selectedSubjectVersionId}
                  onChange={handleSubjectVersionIdChange}
                  label="选择学科版本 *"
                  disabled={!selectedSubjectId || versionsLoading}
                >
                  <MenuItem value={0}>
                    {versionsLoading ? '加载中...' : '请选择学科版本'}
                  </MenuItem>
                  {availableVersions.map((version) => (
                    <MenuItem key={version.id} value={version.id}>
                      {version.schoolLevel ? `${version.schoolLevel} - ${version.name}` : version.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>选择章节 *</InputLabel>
                <Select
                  value={formData.chapterId}
                  onChange={handleChapterIdChange}
                  label="选择章节 *"
                  disabled={!selectedSubjectVersionId || chaptersLoading}
                >
                  <MenuItem value={0}>
                    {chaptersLoading ? '加载中...' : '请选择章节'}
                  </MenuItem>
                  {availableChapters.map((chapter) => (
                    <MenuItem key={chapter.id} value={chapter.id}>
                      {chapter.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="描述"
                value={formData.description}
                onChange={handleDescriptionChange}
                multiline
                rows={3}
                sx={{ mb: 2 }}
              />

              {/* 封面图片上传 */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  封面图片
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
                  <TextField
                    fullWidth
                    label="图片URL"
                    value={formData.coverImageUrl}
                    onChange={handleCoverImageUrlChange}
                    placeholder="输入图片URL或点击上传按钮选择文件"
                  />
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={imageUploading ? <CircularProgress size={20} /> : <CloudUpload />}
                    disabled={imageUploading}
                    sx={{ minWidth: 120, height: 56 }}
                  >
                    {imageUploading ? '上传中...' : '上传图片'}
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={handleImageUpload}
                      disabled={imageUploading}
                    />
                  </Button>
                </Box>
                {formData.coverImageUrl && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      预览：
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <img
                        src={formData.coverImageUrl}
                        alt="封面预览"
                        style={{
                          maxWidth: '200px',
                          maxHeight: '150px',
                          objectFit: 'cover',
                          borderRadius: '8px',
                          border: '1px solid #ddd'
                        }}
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </Box>
                  </Box>
                )}
              </Box>

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <TextField
                  label="排序序号"
                  type="number"
                  value={formData.orderIndex}
                  onChange={handleOrderIndexChange}
                  inputProps={{ min: 1 }}
                  sx={{ flex: 1 }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.enabled}
                      onChange={handleEnabledChange}
                    />
                  }
                  label="启用状态"
                  sx={{ flex: 1 }}
                />
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>取消</Button>
            <Button onClick={handleSave} variant="contained">
              {editingKnowledgePoint ? '更新' : '创建'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 成功提示 */}
        <Snackbar
          open={!!success}
          autoHideDuration={3000}
          onClose={() => setSuccess('')}
        >
          <Alert severity="success" onClose={() => setSuccess('')}>
            {success}
          </Alert>
        </Snackbar>

        {/* 错误提示 */}
        <Snackbar
          open={!!error}
          autoHideDuration={5000}
          onClose={() => setError('')}
        >
          <Alert severity="error" onClose={() => setError('')}>
            {error}
          </Alert>
        </Snackbar>

        {/* 视频上传对话框 */}
        <VideoUploadDialog
          open={openVideoDialog}
          onClose={() => {
            setOpenVideoDialog(false);
            setSelectedKnowledgePointId(null);
          }}
          onUpload={handleVideoUpload}
        />

        {/* 视频管理对话框 */}
        <VideoManageDialog
          open={openVideoManageDialog}
          onClose={() => {
            setOpenVideoManageDialog(false);
            setSelectedKnowledgePointId(null);
          }}
          knowledgePointId={selectedKnowledgePointId}
          onVideoAdded={loadKnowledgePoints}
          onVideoRemoved={loadKnowledgePoints}
        />
      </Box>
    </Layout>
  );
};

export default KnowledgePointManagePage;
