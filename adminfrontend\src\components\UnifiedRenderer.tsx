import React, { useEffect, useRef, useCallback } from 'react';
import { MathJax } from 'better-react-mathjax';
import { Alert, Box } from '@mui/material';
import DOMPurify from 'dompurify';

interface UnifiedRendererProps {
  /** 内容（HTML、LaTeX、纯文本都支持） */
  content: string;
  /** 渲染模式 */
  mode?: 'auto' | 'html' | 'formula' | 'text';
  /** 是否行内显示 */
  inline?: boolean;
  /** 内容截断长度（用于列表显示） */
  maxLength?: number;
  /** CSS类名 */
  className?: string;
  /** 内联样式 */
  style?: React.CSSProperties;
  /** 错误回调 */
  onError?: (error: string) => void;
  /** 是否显示错误信息 */
  showError?: boolean;
}

/**
 * 统一渲染器 - 管理端专用
 * 
 * 功能特点：
 * - 智能检测内容类型（HTML、LaTeX、纯文本）
 * - 自动选择最佳渲染策略
 * - 支持数学公式和化学公式
 * - 零配置使用，开发者友好
 * 
 * 使用场景：
 * - 题目内容显示
 * - 题目列表预览
 * - 选项渲染
 * - 编辑器预览
 */
const UnifiedRenderer: React.FC<UnifiedRendererProps> = ({
  content,
  mode = 'auto',
  inline = false,
  maxLength,
  className = '',
  style = {},
  onError,
  showError = false
}) => {
  // 所有Hooks必须在组件顶部调用
  const containerRef = useRef<HTMLDivElement>(null);
  const isMountedRef = useRef(true);

  // 使用全局Promise链的安全渲染函数 - 严格按照官方文档
  const safeTypesetPromise = useCallback(() => {
    if (!isMountedRef.current || !containerRef.current) {
      return Promise.resolve();
    }

    const element = containerRef.current;

    // 使用全局safeTypeset函数 - 官方文档推荐的Promise链管理
    const globalSafeTypeset = (window as any).safeTypeset;
    if (globalSafeTypeset) {
      return globalSafeTypeset(() => {
        // 再次验证元素状态（官方建议）
        if (!isMountedRef.current || !element.isConnected) {
          return [];
        }
        return [element];
      }).catch((error: any) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MathJax typeset failed:', error);
        }
        if (onError) {
          onError(`数学公式渲染失败: ${error.message || error}`);
        }
        return Promise.resolve();
      });
    }

    return Promise.resolve();
  }, [onError, content]);

  // 防抖渲染函数 - 基于官方文档的最佳实践
  const debouncedRender = useCallback(() => {
    // 使用全局Promise链确保顺序执行
    safeTypesetPromise();
  }, [safeTypesetPromise]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      // 根据官方文档，不需要手动清理MathJax内容
      // better-react-mathjax会自动处理
    };
  }, []);

  // 内容变化时触发重新渲染 - 基于官方文档优化
  useEffect(() => {
    // 内容有效性检查
    if (!content || typeof content !== 'string' || content.trim() === '') {
      return;
    }

    // 内容截断处理
    const processedContent = maxLength ? truncateContent(content, maxLength) : content;

    // 智能检测内容类型
    const detectContentType = (content: string): 'html' | 'formula' | 'text' => {
      // 包含HTML标签
      if (/<[^>]+>/.test(content)) return 'html';

      // 包含LaTeX标记
      if (/\$|\\\(|\\\[|\\ce\{|\\mathrm\{|\\pu\{/.test(content)) return 'formula';

      // 纯文本
      return 'text';
    };

    // 获取实际渲染模式
    const actualMode = mode === 'auto' ? detectContentType(processedContent) : mode;

    if (actualMode === 'html' || actualMode === 'formula') {
      // 官方文档建议的延迟渲染
      const timer = setTimeout(() => {
        if (isMountedRef.current) {
          debouncedRender();
        }
      }, 50); // 减少延迟，官方文档显示50ms足够

      return () => clearTimeout(timer);
    }
  }, [content, mode, maxLength, debouncedRender]);

  // 内容有效性检查
  if (!content || typeof content !== 'string' || content.trim() === '') {
    return null;
  }

  // 内容截断处理
  const processedContent = maxLength ? truncateContent(content, maxLength) : content;

  // 智能检测内容类型
  const detectContentType = (content: string): 'html' | 'formula' | 'text' => {
    // 包含HTML标签
    if (/<[^>]+>/.test(content)) return 'html';

    // 包含LaTeX标记
    if (/\$|\\\(|\\\[|\\ce\{|\\mathrm\{|\\pu\{/.test(content)) return 'formula';

    // 纯文本
    return 'text';
  };

  // 获取实际渲染模式
  const actualMode = mode === 'auto' ? detectContentType(processedContent) : mode;

  // 内容截断函数
  function truncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) return content;
    
    let truncated = content.substring(0, maxLength);
    
    // 智能截断：避免截断公式标记
    const formulaStart = truncated.lastIndexOf('\\(');
    const formulaEnd = truncated.lastIndexOf('\\)');
    
    if (formulaStart > formulaEnd && formulaStart !== -1) {
      truncated = content.substring(0, formulaStart);
    }
    
    // 避免截断HTML标签
    const tagStart = truncated.lastIndexOf('<');
    const tagEnd = truncated.lastIndexOf('>');
    
    if (tagStart > tagEnd && tagStart !== -1) {
      truncated = content.substring(0, tagStart);
    }
    
    return truncated + '...';
  }

  // HTML内容清理
  const sanitizeHtml = (html: string): string => {
    try {
      return DOMPurify.sanitize(html, {
        ALLOWED_TAGS: [
          'p', 'div', 'span', 'br', 'strong', 'em', 'u', 'sub', 'sup',
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'ul', 'ol', 'li',
          'img', 'audio', 'video',
          'table', 'tr', 'td', 'th', 'thead', 'tbody'
        ],
        ALLOWED_ATTR: [
          'src', 'alt', 'title', 'href', 'target',
          'style', 'class', 'id',
          'controls', 'autoplay', 'loop'
        ],
        KEEP_CONTENT: true
      });
    } catch (error) {
      console.warn('HTML清理失败:', error);
      return html;
    }
  };

  // 公式预处理
  const preprocessFormula = (formula: string): string => {
    let processed = formula.trim();
    
    // 检测是否为化学公式
    const isChemistry = detectChemistryFormula(processed);
    
    if (isChemistry && !processed.includes('\\ce{') && !processed.includes('\\mathrm{')) {
      // 自动包装化学公式
      processed = `\\ce{${processed}}`;
    }
    
    return processed;
  };

  // 化学公式检测
  const detectChemistryFormula = (formula: string): boolean => {
    // 排除单字符和纯数学表达式
    if (/^[A-Za-z0-9]$/.test(formula.trim())) return false;
    if (/^[\d\+\-\*\/\(\)\.\s=<>]+$/.test(formula)) return false;
    
    // 明确的化学标记
    if (/\\ce\{|\\pu\{|\\mathrm\{/.test(formula)) return true;
    
    // 化学特征检测
    const chemPatterns = [
      /^[A-Z][a-z]?\d+$/,                    // H2O, CO2
      /^[A-Z][a-z]?\d*\([A-Z][a-z]?\d*\)\d+$/, // Ca(OH)2
      /[A-Z][a-z]?\d+\s*[\+\-→]\s*[A-Z][a-z]?\d+/, // 化学反应
      /\^[+-]\d*$/                           // 离子电荷
    ];
    
    return chemPatterns.some(pattern => pattern.test(formula));
  };

  // 渲染内容
  const renderContent = () => {
    try {
      switch (actualMode) {
        case 'html':
          // HTML内容渲染（包含公式的HTML）- 防止组件卸载时的MathJax错误
          const cleanHtml = sanitizeHtml(processedContent);

          // 如果组件即将卸载，不创建新的MathJax组件
          if (!isMountedRef.current) {
            return (
              <div
                ref={containerRef}
                dangerouslySetInnerHTML={{ __html: cleanHtml }}
                style={{
                  lineHeight: '1.6',
                  fontSize: '14px',
                  color: '#333'
                }}
              />
            );
          }

          return (
            <MathJax key={`html-${Date.now()}`}>
              <div
                ref={containerRef}
                dangerouslySetInnerHTML={{ __html: cleanHtml }}
                style={{
                  lineHeight: '1.6',
                  fontSize: '14px',
                  color: '#333'
                }}
              />
            </MathJax>
          );

        case 'formula':
          // 纯公式渲染 - 防止组件卸载时的MathJax错误
          const processedFormula = preprocessFormula(processedContent);
          const delimiter = inline ? ['\\(', '\\)'] : ['\\[', '\\]'];
          const formulaText = processedFormula.startsWith('\\(') || processedFormula.startsWith('\\[') ||
                             processedFormula.startsWith('$') || processedFormula.startsWith('$$')
                             ? processedFormula
                             : `${delimiter[0]}${processedFormula}${delimiter[1]}`;

          // 如果组件即将卸载，不创建新的MathJax组件
          if (!isMountedRef.current) {
            return (
              <span
                ref={containerRef}
                style={{ display: inline ? 'inline' : 'block' }}
                dangerouslySetInnerHTML={{ __html: formulaText }}
              />
            );
          }

          return (
            <MathJax key={`formula-${Date.now()}`}>
              <span
                ref={containerRef}
                style={{ display: inline ? 'inline' : 'block' }}
              >
                {formulaText}
              </span>
            </MathJax>
          );

        case 'text':
        default:
          // 纯文本渲染
          return (
            <span style={{ 
              lineHeight: '1.6',
              fontSize: '14px',
              color: '#333'
            }}>
              {processedContent}
            </span>
          );
      }
    } catch (error) {
      const errorMessage = `渲染失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('UnifiedRenderer错误:', error);
      
      if (onError) onError(errorMessage);
      
      if (showError) {
        return (
          <Alert severity="error">
            {errorMessage}
          </Alert>
        );
      }
      
      // 降级显示原始内容
      return (
        <span style={{ color: '#666', fontStyle: 'italic' }}>
          {processedContent}
        </span>
      );
    }
  };

  // 开发环境调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('UnifiedRenderer 渲染:', {
      原始内容: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      检测模式: actualMode,
      是否截断: !!maxLength,
      处理后长度: processedContent.length
    });
  }

  return (
    <Box
      ref={containerRef}
      component={inline ? 'span' : 'div'}
      className={`unified-renderer ${actualMode} ${className}`}
      style={{
        display: inline ? 'inline' : 'block',
        ...style
      }}
    >
      {renderContent()}
    </Box>
  );
};

export default UnifiedRenderer;
