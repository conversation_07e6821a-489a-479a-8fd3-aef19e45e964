import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { MathJaxContext } from 'better-react-mathjax';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import UsersPage from './pages/UsersPage';
import SupervisorManagePage from './pages/SupervisorManagePage';
import QuestionsPage from './pages/QuestionsPage';
import MigrationPage from './pages/MigrationPage';
import PracticeConfigPage from './pages/PracticeConfigPage';
import SubjectManagePage from './pages/SubjectManagePage';
import SubjectVersionManagePage from './pages/SubjectVersionManagePage';
import ChapterManagePage from './pages/ChapterManagePage';
import KnowledgePointManagePage from './pages/KnowledgePointManagePage';
import MathKeyboardTestPage from './pages/MathKeyboardTestPage';
import GlobalKeyboard from './components/GlobalKeyboard';


// 创建主题
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  zIndex: {
    // 确保自定义键盘的z-index高于所有Material-UI组件
    modal: 1300,
    snackbar: 1400,
    tooltip: 1500,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
    // 确保Dialog的z-index不会超过键盘
    MuiDialog: {
      styleOverrides: {
        root: {
          zIndex: 1300, // 确保Dialog在键盘之下
        },
      },
    },
  },
});

// 管理端专用的简化MathJax配置 - 增强错误处理
const mathJaxConfig = {
  loader: {
    load: ['[tex]/mhchem']
  },
  tex: {
    packages: { '[+]': ['mhchem'] },
    inlineMath: [['$', '$'], ['\\(', '\\)']],
    displayMath: [['$$', '$$'], ['\\[', '\\]']],
    processEscapes: true,
    macros: {
      placeholderbox: '\\class{placeholder-box}{\\Box}'
    }
  },
  options: {
    enableMenu: false,
    skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
    ignoreHtmlClass: 'no-mathjax'
  },
  startup: {
    ready: () => {
      try {
        const MJ = window.MathJax as any;
        if (MJ?.startup?.defaultReady) {
          MJ.startup.defaultReady();
        }

        // 官方文档推荐的全局Promise链管理
        let globalTypesetPromise = Promise.resolve();

        // 创建安全的typeset函数 - 严格按照官方文档
        const safeTypeset = function(code: () => HTMLElement[]) {
          globalTypesetPromise = globalTypesetPromise
            .then(() => {
              try {
                const elements = code();
                // 过滤有效的DOM元素
                const validElements = elements.filter(el => {
                  try {
                    return el &&
                           el.nodeType === Node.ELEMENT_NODE &&
                           el.isConnected &&
                           document.contains(el) &&
                           document.body.contains(el);
                  } catch (error) {
                    return false;
                  }
                });

                // 如果没有有效元素，直接返回resolved promise
                if (validElements.length === 0) {
                  return Promise.resolve();
                }

                // 调用原始MathJax.typesetPromise
                return window.MathJax.typesetPromise(validElements);
              } catch (error) {
                console.warn('Typeset code execution failed:', error);
                return Promise.resolve();
              }
            })
            .catch((err: any) => {
              console.warn('Typeset failed: ' + (err.message || err));
              return Promise.resolve();
            });
          return globalTypesetPromise;
        };

        // 将安全的typeset函数暴露到全局
        (window as any).safeTypeset = safeTypeset;

        // 开发环境验证
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ MathJax启动完成');
          console.log('✅ DOM生命周期保护已启用');
        }
      } catch (error) {
        console.warn('MathJax启动错误:', error);
      }
    }
  }
};

function App() {
  const handleGlobalError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 可以在这里集成错误监控服务
    // errorMonitoringService.captureException(error, {
    //   extra: errorInfo,
    //   tags: { component: 'App' }
    // });
  };

  return (
    <ErrorBoundary onError={handleGlobalError}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <MathJaxContext config={mathJaxConfig}>
          <AuthProvider>
          <Router>
            <Routes>
              {/* 公开路由 */}
              <Route path="/login" element={<LoginPage />} />

              {/* 受保护的路由 */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <ErrorBoundary>
                      <DashboardPage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/users"
                element={
                  <ProtectedRoute requireSuperAdmin>
                    <ErrorBoundary>
                      <UsersPage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/supervisors"
                element={
                  <ProtectedRoute requireAdmin>
                    <ErrorBoundary>
                      <SupervisorManagePage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/questions"
                element={
                  <ProtectedRoute requireAdmin>
                    <ErrorBoundary>
                      <QuestionsPage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/migration"
                element={
                  <ProtectedRoute requireSuperAdmin>
                    <ErrorBoundary>
                      <MigrationPage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/practice-config"
                element={
                  <ProtectedRoute requireSupervisor>
                    <ErrorBoundary>
                      <PracticeConfigPage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/subjects"
                element={
                  <ProtectedRoute requireAdmin>
                    <ErrorBoundary>
                      <SubjectManagePage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/subject-versions"
                element={
                  <ProtectedRoute requireAdmin>
                    <ErrorBoundary>
                      <SubjectVersionManagePage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/chapters"
                element={
                  <ProtectedRoute requireAdmin>
                    <ErrorBoundary>
                      <ChapterManagePage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/knowledge-points"
                element={
                  <ProtectedRoute requireAdmin>
                    <ErrorBoundary>
                      <KnowledgePointManagePage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/math-keyboard-test"
                element={
                  <ProtectedRoute>
                    <ErrorBoundary>
                      <MathKeyboardTestPage />
                    </ErrorBoundary>
                  </ProtectedRoute>
                }
              />

              {/* 默认重定向 */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Router>
          </AuthProvider>

          {/* 全局键盘组件 */}
          <GlobalKeyboard />
        </MathJaxContext>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
