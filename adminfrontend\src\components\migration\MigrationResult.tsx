import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Speed as SpeedIcon,
  Schedule as ScheduleIcon,
  DataUsage as DataUsageIcon,
  ExpandMore as ExpandMoreIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { MigrationResult as MigrationResultType, BatchResult } from '../../types/migration';

interface MigrationResultProps {
  result: MigrationResultType;
  onRetry?: () => void;
  onDownloadReport?: () => void;
}

const MigrationResult: React.FC<MigrationResultProps> = ({
  result,
  onRetry,
  onDownloadReport
}) => {
  const [expandedBatch, setExpandedBatch] = useState<string | false>(false);

  const handleBatchAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedBatch(isExpanded ? panel : false);
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  };

  const getResultSeverity = () => {
    if (result.success && result.errorCount === 0) return 'success';
    if (result.success && result.errorCount > 0) return 'warning';
    return 'error';
  };

  const getResultMessage = () => {
    const updateCount = result.skipCount || 0; // 使用skipCount作为更新数量

    if (result.success && result.errorCount === 0) {
      if (updateCount > 0) {
        return `迁移成功完成！新增 ${result.successCount} 条数据，更新 ${updateCount} 条现有数据。`;
      } else {
        return '迁移成功完成！所有数据都已成功导入。';
      }
    } else if (result.success && result.errorCount > 0) {
      if (updateCount > 0) {
        return `迁移完成，新增 ${result.successCount} 条，更新 ${updateCount} 条，失败 ${result.errorCount} 条。请查看错误详情。`;
      } else {
        return `迁移完成，但有 ${result.errorCount} 条数据失败。请查看错误详情。`;
      }
    } else {
      return '迁移失败，请检查错误信息并重试。';
    }
  };

  return (
    <Box>
      {/* 结果概览 */}
      <Alert
        severity={getResultSeverity()}
        sx={{ mb: 3 }}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            {onDownloadReport && (
              <Button
                color="inherit"
                size="small"
                startIcon={<DownloadIcon />}
                onClick={onDownloadReport}
              >
                下载报告
              </Button>
            )}
            {onRetry && !result.success && (
              <Button
                color="inherit"
                size="small"
                startIcon={<RefreshIcon />}
                onClick={onRetry}
              >
                重试
              </Button>
            )}
          </Box>
        }
      >
        <Typography variant="h6" gutterBottom>
          {result.success ? '迁移完成' : '迁移失败'}
        </Typography>
        <Typography variant="body2">
          {getResultMessage()}
        </Typography>
      </Alert>

      {/* 统计卡片 */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <DataUsageIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="primary">
                {result.totalCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                总数据量
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircleIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="success.main">
                {result.successCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                成功数量
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ErrorIcon color="error" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="error.main">
                {result.errorCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                失败数量
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* 更新数量卡片 */}
        {result.skipCount && result.skipCount > 0 && (
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <WarningIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h4" color="info.main">
                  {result.skipCount}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  更新题目
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SpeedIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="info.main">
                {result.successRate?.toFixed(1) || '0'}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                成功率
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 详细信息 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          详细信息
        </Typography>
        <List>
          <ListItem>
            <ListItemIcon>
              <ScheduleIcon />
            </ListItemIcon>
            <ListItemText
              primary="开始时间"
              secondary={new Date(result.startTime).toLocaleString()}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <ScheduleIcon />
            </ListItemIcon>
            <ListItemText
              primary="结束时间"
              secondary={new Date(result.endTime).toLocaleString()}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <SpeedIcon />
            </ListItemIcon>
            <ListItemText
              primary="总耗时"
              secondary={formatDuration(result.durationMs)}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <DataUsageIcon />
            </ListItemIcon>
            <ListItemText
              primary="处理速度"
              secondary={`${result.processingSpeed?.toFixed(1) || '0'} 条/秒`}
            />
          </ListItem>
        </List>
      </Paper>

      {/* 错误列表 */}
      {result.errors && result.errors.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom color="error">
            错误详情 ({result.errors.length})
          </Typography>
          <List>
            {result.errors.slice(0, 10).map((error, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary={`错误 ${index + 1}`}
                  secondary={error}
                />
              </ListItem>
            ))}
          </List>
          {result.errors.length > 10 && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              还有 {result.errors.length - 10} 个错误未显示...
            </Typography>
          )}
        </Paper>
      )}

      {/* 批次详情 */}
      {result.batchResults && result.batchResults.length > 0 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            批次详情 ({result.batchResults.length} 个批次)
          </Typography>
          
          {result.batchResults.map((batch, index) => (
            <Accordion
              key={batch.batchNumber}
              expanded={expandedBatch === `batch-${index}`}
              onChange={handleBatchAccordionChange(`batch-${index}`)}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  <Typography variant="subtitle1">
                    批次 {batch.batchNumber}
                  </Typography>
                  <Chip
                    label={`${batch.successCount}/${batch.batchSize}`}
                    color={batch.errorCount === 0 ? 'success' : 'warning'}
                    size="small"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 'auto' }}>
                    {formatDuration(batch.durationMs)}
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      统计信息
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemText
                          primary="批次大小"
                          secondary={`${batch.batchSize} 条`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="成功数量"
                          secondary={`${batch.successCount} 条`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="失败数量"
                          secondary={`${batch.errorCount} 条`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="成功率"
                          secondary={`${batch.successRate?.toFixed(1) || '0'}%`}
                        />
                      </ListItem>
                    </List>
                  </Grid>

                  {batch.errorCount > 0 && (
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography variant="subtitle2" gutterBottom color="error">
                        错误详情
                      </Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>题目ID</TableCell>
                              <TableCell>错误信息</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {Object.entries(batch.errors).slice(0, 5).map(([id, error]) => (
                              <TableRow key={id}>
                                <TableCell>{id}</TableCell>
                                <TableCell>
                                  <Typography variant="body2" noWrap>
                                    {error}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                      {Object.keys(batch.errors).length > 5 && (
                        <Typography variant="caption" color="text.secondary">
                          还有 {Object.keys(batch.errors).length - 5} 个错误...
                        </Typography>
                      )}
                    </Grid>
                  )}
                </Grid>
              </AccordionDetails>
            </Accordion>
          ))}
        </Paper>
      )}
    </Box>
  );
};

export default MigrationResult;
