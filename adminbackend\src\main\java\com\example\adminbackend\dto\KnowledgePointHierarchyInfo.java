package com.example.adminbackend.dto;

import lombok.Data;

/**
 * 知识点层级信息DTO
 * 包含知识点所属的科目、版本、章节信息
 */
@Data
public class KnowledgePointHierarchyInfo {
    // 知识点信息
    private Long knowledgePointId;
    private String knowledgePointName;
    
    // 章节信息
    private Long chapterId;
    private String chapterName;
    
    // 科目版本信息
    private Long subjectVersionId;
    private String subjectVersionName;
    
    // 科目信息
    private Long subjectId;
    private String subjectName;
}
