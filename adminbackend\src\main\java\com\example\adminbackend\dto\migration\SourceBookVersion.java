package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Objects;

/**
 * 源数据版本信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceBookVersion {
    
    /**
     * 版本ID
     */
    private String id;
    
    /**
     * 版本名称
     */
    private String name;
    
    /**
     * 年级
     */
    private String grade;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SourceBookVersion that = (SourceBookVersion) o;
        return Objects.equals(name, that.name) && Objects.equals(grade, that.grade);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, grade);
    }
}
