package com.example.adminbackend.service.migration;

import com.example.adminbackend.dto.migration.*;
import com.example.adminbackend.exception.HierarchyResolutionException;
import com.example.adminbackend.model.*;
import com.example.adminbackend.repository.*;
import com.example.adminbackend.service.VideoCollectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 层级数据处理器
 * 处理学科、版本、章节、知识点的层级关系
 */
@Component
@Lazy
@RequiredArgsConstructor
@Slf4j
public class HierarchyDataProcessor {
    
    private final SubjectRepository subjectRepository;
    private final SubjectVersionRepository subjectVersionRepository;
    private final ChapterRepository chapterRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final VideoCollectionService videoCollectionService;
    
    // 缓存映射，提高查询性能
    private final Map<String, Subject> subjectCache = new ConcurrentHashMap<>();
    private final Map<String, SubjectVersion> versionCache = new ConcurrentHashMap<>();
    private final Map<String, Chapter> chapterCache = new ConcurrentHashMap<>();
    private final Map<String, KnowledgePoint> knowledgePointCache = new ConcurrentHashMap<>();
    
    /**
     * 预处理层级数据，建立缓存
     */
    @Transactional
    public void preprocessHierarchyData(List<SourceQuestionData> sourceData) {
        log.info("开始预处理层级数据");
        
        Set<BasicInfo> uniqueHierarchies = new HashSet<>();
        for (SourceQuestionData data : sourceData) {
            if (data.getBasicInfo() != null) {
                uniqueHierarchies.add(data.getBasicInfo());
            }
        }
            
        log.info("发现 {} 个唯一的层级结构", uniqueHierarchies.size());
        
        for (BasicInfo basicInfo : uniqueHierarchies) {
            try {
                processHierarchy(basicInfo);
            } catch (Exception e) {
                log.error("处理层级数据失败: {}", basicInfo, e);
            }
        }
        
        log.info("层级数据预处理完成，缓存统计 - 学科: {}, 版本: {}, 章节: {}, 知识点: {}", 
            subjectCache.size(), versionCache.size(), chapterCache.size(), knowledgePointCache.size());
    }
    
    /**
     * 处理单个层级结构
     */
    @Transactional
    public void processHierarchy(BasicInfo basicInfo) {
        // 1. 处理学科
        Subject subject = findOrCreateSubject(basicInfo.getSubject());
        
        // 2. 处理版本
        SubjectVersion version = findOrCreateVersion(subject, basicInfo.getBookversion());
        
        // 3. 处理章节
        Chapter chapter = findOrCreateChapter(version, basicInfo.getChapter());
        
        // 4. 处理知识点
        if (basicInfo.getKnowledgePoints() != null && !basicInfo.getKnowledgePoints().isEmpty()) {
            for (SourceKnowledgePoint sourceKp : basicInfo.getKnowledgePoints()) {
                findOrCreateKnowledgePoint(chapter, sourceKp);
            }
        }
    }
    
    /**
     * 查找或创建学科
     */
    @Transactional
    public Subject findOrCreateSubject(SourceSubject sourceSubject) {
        if (sourceSubject == null || sourceSubject.getName() == null) {
            throw new IllegalArgumentException("学科信息不能为空");
        }
        
        String cacheKey = sourceSubject.getName();
        return subjectCache.computeIfAbsent(cacheKey, key -> {
            // 先查询数据库 - 使用findAllByName避免重复数据异常
            List<Subject> existingList = subjectRepository.findAllByName(sourceSubject.getName());
            if (!existingList.isEmpty()) {
                if (existingList.size() > 1) {
                    log.warn("发现重复的学科记录: name={}, 数量={}, 使用第一个", sourceSubject.getName(), existingList.size());
                }
                return existingList.get(0);
            }
            
            // 创建新学科
            Subject newSubject = Subject.builder()
                .name(sourceSubject.getName())
                .description("从源数据导入")
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
                
            Subject saved = subjectRepository.save(newSubject);
            log.info("创建新学科: {}", saved.getName());
            return saved;
        });
    }
    
    /**
     * 查找或创建版本
     */
    @Transactional
    public SubjectVersion findOrCreateVersion(Subject subject, SourceBookVersion sourceVersion) {
        if (sourceVersion == null || sourceVersion.getName() == null) {
            throw new IllegalArgumentException("版本信息不能为空");
        }
        
        String cacheKey = subject.getId() + "_" + sourceVersion.getName() + "_" + sourceVersion.getGrade();
        return versionCache.computeIfAbsent(cacheKey, key -> {
            // 先查询数据库 - 使用findAllBySubjectIdAndNameAndSchoolLevel避免重复数据异常
            List<SubjectVersion> existingList = subjectVersionRepository.findAllBySubjectIdAndNameAndSchoolLevel(
                subject.getId(), sourceVersion.getName(), sourceVersion.getGrade());
            if (!existingList.isEmpty()) {
                if (existingList.size() > 1) {
                    log.warn("发现重复的版本记录: subjectId={}, name={}, schoolLevel={}, 数量={}, 使用第一个",
                        subject.getId(), sourceVersion.getName(), sourceVersion.getGrade(), existingList.size());
                }
                return existingList.get(0);
            }
            
            // 创建新版本
            SubjectVersion newVersion = SubjectVersion.builder()
                .subject(subject)
                .name(sourceVersion.getName())
                .schoolLevel(sourceVersion.getGrade())
                .description("从源数据导入")
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
                
            SubjectVersion saved = subjectVersionRepository.save(newVersion);
            log.info("创建新版本: {} - {} (年级: {})", subject.getName(), saved.getName(), saved.getSchoolLevel());
            return saved;
        });
    }
    
    /**
     * 查找或创建章节
     */
    @Transactional
    public Chapter findOrCreateChapter(SubjectVersion version, SourceChapter sourceChapter) {
        if (sourceChapter == null || sourceChapter.getName() == null) {
            throw new IllegalArgumentException("章节信息不能为空");
        }
        
        String cacheKey = version.getId() + "_" + sourceChapter.getName();
        return chapterCache.computeIfAbsent(cacheKey, key -> {
            // 先查询数据库 - 使用findAllBySubjectVersionIdAndName避免重复数据异常
            List<Chapter> existingList = chapterRepository.findAllBySubjectVersionIdAndName(version.getId(), sourceChapter.getName());
            if (!existingList.isEmpty()) {
                if (existingList.size() > 1) {
                    log.warn("发现重复的章节记录: versionId={}, name={}, 数量={}, 使用第一个",
                        version.getId(), sourceChapter.getName(), existingList.size());
                    System.out.println("=== 发现重复章节记录！！！ ===");
                    System.out.println("版本ID: " + version.getId());
                    System.out.println("章节名称: " + sourceChapter.getName());
                    System.out.println("重复记录数量: " + existingList.size());
                }
                return existingList.get(0);
            }
            
            // 获取下一个顺序索引
            Integer maxOrderIndex = chapterRepository.findMaxOrderIndexBySubjectVersionId(version.getId());
            int nextOrderIndex = (maxOrderIndex == null) ? 0 : maxOrderIndex + 1;
            
            // 创建新章节
            Chapter newChapter = Chapter.builder()
                .subjectVersion(version)
                .name(sourceChapter.getName())
                .orderIndex(nextOrderIndex)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
                
            Chapter saved = chapterRepository.save(newChapter);
            log.info("创建新章节: {} - {} - {}", version.getSubject().getName(), version.getName(), saved.getName());
            return saved;
        });
    }
    
    /**
     * 查找或创建知识点
     */
    @Transactional
    public KnowledgePoint findOrCreateKnowledgePoint(Chapter chapter, SourceKnowledgePoint sourceKp) {
        if (sourceKp == null || sourceKp.getName() == null || sourceKp.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("知识点信息不能为空");
        }
        
        log.info("开始查找或创建知识点: chapter_id={}, chapter_name={}, knowledge_point_name={}",
            chapter.getId(), chapter.getName(), sourceKp.getName());
        System.out.println("=== 开始查找或创建知识点 ===");
        System.out.println("章节ID: " + chapter.getId());
        System.out.println("章节名称: " + chapter.getName());
        System.out.println("知识点名称: " + sourceKp.getName());

        String cacheKey = chapter.getId() + "_" + sourceKp.getName();
        return knowledgePointCache.computeIfAbsent(cacheKey, key -> {
            log.debug("缓存中未找到知识点，开始数据库查询: cacheKey={}", cacheKey);

            try {
                // 先查询数据库 - 使用findAllByChapterIdAndName处理重复数据
                log.debug("执行数据库查询: findAllByChapterIdAndName(chapter_id={}, name={})",
                    chapter.getId(), sourceKp.getName());

                List<KnowledgePoint> existingList = knowledgePointRepository
                    .findAllByChapterIdAndName(chapter.getId(), sourceKp.getName());

                log.info("数据库查询结果: 找到 {} 个匹配的知识点记录", existingList.size());
                System.out.println("=== 数据库查询结果 ===");
                System.out.println("找到记录数量: " + existingList.size());

                if (!existingList.isEmpty()) {
                    // 如果存在多个重复记录，记录警告并取第一个
                    if (existingList.size() > 1) {
                        log.warn("发现重复的知识点记录: chapter_id={}, name={}, 数量={}, 将使用第一个记录 ID={}",
                            chapter.getId(), sourceKp.getName(), existingList.size(), existingList.get(0).getId());

                        System.out.println("=== 发现重复记录！！！ ===");
                        System.out.println("章节ID: " + chapter.getId());
                        System.out.println("知识点名称: " + sourceKp.getName());
                        System.out.println("重复记录数量: " + existingList.size());

                        // 记录所有重复记录的详细信息
                        for (int i = 0; i < existingList.size(); i++) {
                            KnowledgePoint kp = existingList.get(i);
                            log.warn("重复记录 #{}: ID={}, name={}, chapter_id={}, order_index={}, enabled={}",
                                i + 1, kp.getId(), kp.getName(), kp.getChapter().getId(),
                                kp.getOrderIndex(), kp.getEnabled());
                            System.out.println("重复记录 #" + (i + 1) + ": ID=" + kp.getId() +
                                ", name=" + kp.getName() + ", chapter_id=" + kp.getChapter().getId() +
                                ", order_index=" + kp.getOrderIndex() + ", enabled=" + kp.getEnabled());
                        }
                    } else {
                        log.info("找到唯一的知识点记录: ID={}, name={}",
                            existingList.get(0).getId(), existingList.get(0).getName());
                    }

                    KnowledgePoint existingKp = existingList.get(0);

                // 检查是否需要更新视频信息
                if (sourceKp.getVideo() != null && !sourceKp.getVideo().trim().isEmpty()) {
                    if (existingKp.getVideoCollection() == null) {
                        // 知识点没有视频合集，创建新的
                        log.info("为已存在的知识点 {} 添加视频信息: {}", sourceKp.getName(), sourceKp.getVideo());
                        try {
                            VideoCollection videoCollection = videoCollectionService.findOrCreateVideoCollectionByUrlAndKnowledgePoint(
                                sourceKp.getVideo(), sourceKp.getName());
                            existingKp.setVideoCollection(videoCollection);
                            existingKp.setUpdatedAt(new Date());
                            existingKp = knowledgePointRepository.save(existingKp);
                            log.info("已为知识点 {} 关联视频合集: {}", sourceKp.getName(),
                                videoCollection != null ? videoCollection.getName() : "null");
                        } catch (Exception e) {
                            log.warn("为已存在知识点处理视频失败: {} - {}", sourceKp.getName(), sourceKp.getVideo(), e);
                        }
                    } else {
                        // 知识点已有视频合集，检查是否需要更新
                        log.info("知识点 {} 已有视频合集，检查是否需要更新视频", sourceKp.getName());
                        try {
                            VideoCollection existingCollection = existingKp.getVideoCollection();
                            List<String> existingUrls = videoCollectionService.getVideoUrlsInCollection(existingCollection.getId());

                            if (!existingUrls.contains(sourceKp.getVideo())) {
                                log.info("知识点 {} 的视频URL发生变化，执行更新: 旧URL={}, 新URL={}",
                                    sourceKp.getName(), existingUrls, sourceKp.getVideo());

                                boolean updated = videoCollectionService.updateVideoCollectionWithNewUrl(
                                    existingCollection.getId(), sourceKp.getVideo(), sourceKp.getName());

                                if (updated) {
                                    existingKp.setUpdatedAt(new Date());
                                    existingKp = knowledgePointRepository.save(existingKp);
                                    log.info("知识点 {} 的视频信息更新成功", sourceKp.getName());
                                } else {
                                    log.warn("知识点 {} 的视频信息更新失败", sourceKp.getName());
                                }
                            } else {
                                log.info("知识点 {} 的视频URL未变化，无需更新", sourceKp.getName());
                            }
                        } catch (Exception e) {
                            log.warn("检查或更新知识点视频失败: {} - {}", sourceKp.getName(), sourceKp.getVideo(), e);
                        }
                    }
                }

                return existingKp;
            }
            
            // 获取下一个顺序索引
            Integer maxOrderIndex = knowledgePointRepository.findMaxOrderIndexByChapterId(chapter.getId());
            int nextOrderIndex = (maxOrderIndex == null) ? 0 : maxOrderIndex + 1;

            // 处理视频信息
            VideoCollection videoCollection = null;
            if (sourceKp.getVideo() != null && !sourceKp.getVideo().trim().isEmpty()) {
                log.info("开始为新知识点 {} 处理视频: {}", sourceKp.getName(), sourceKp.getVideo());
                try {
                    videoCollection = videoCollectionService.findOrCreateVideoCollectionByUrlAndKnowledgePoint(
                        sourceKp.getVideo(), sourceKp.getName());
                    log.info("为新知识点 {} 关联视频合集: {}", sourceKp.getName(),
                        videoCollection != null ? videoCollection.getName() : "null");
                } catch (Exception e) {
                    log.error("处理新知识点视频失败: {} - {}", sourceKp.getName(), sourceKp.getVideo(), e);
                }
            } else {
                log.info("新知识点 {} 没有视频信息", sourceKp.getName());
            }

            // 创建新知识点
            KnowledgePoint newKp = KnowledgePoint.builder()
                .chapter(chapter)
                .name(sourceKp.getName())
                .orderIndex(nextOrderIndex)
                .coverImageUrl(null) // 可以后续设置
                .description("从源数据导入")
                .enabled(true) // 默认启用
                .videoCollection(videoCollection) // 关联视频合集
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
                
            KnowledgePoint saved = knowledgePointRepository.save(newKp);
            log.info("创建新知识点: {} - {} - {} - {}，视频合集ID: {}",
                chapter.getSubjectVersion().getSubject().getName(),
                chapter.getSubjectVersion().getName(),
                chapter.getName(),
                saved.getName(),
                saved.getVideoCollection() != null ? saved.getVideoCollection().getId() : "null");
            return saved;

            } catch (Exception e) {
                log.error("查找或创建知识点时发生异常: chapter_id={}, name={}",
                    chapter.getId(), sourceKp.getName(), e);
                System.out.println("=== 异常发生！！！ ===");
                System.out.println("章节ID: " + chapter.getId());
                System.out.println("知识点名称: " + sourceKp.getName());
                System.out.println("异常类型: " + e.getClass().getSimpleName());
                System.out.println("异常消息: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("查找或创建知识点失败", e);
            }
        });
    }
    
    /**
     * 解析知识点ID
     */
    public Long resolveKnowledgePointId(BasicInfo basicInfo) {
        if (basicInfo == null || basicInfo.getKnowledgePoints() == null || basicInfo.getKnowledgePoints().isEmpty()) {
            throw new IllegalArgumentException("无法解析知识点ID，基础信息不完整");
        }

        log.info("开始解析知识点ID，基础信息: subject={}, bookversion={}, chapter={}, knowledgePoints={}",
            basicInfo.getSubject() != null ? basicInfo.getSubject().getName() : "null",
            basicInfo.getBookversion() != null ? basicInfo.getBookversion().getName() : "null",
            basicInfo.getChapter() != null ? basicInfo.getChapter().getName() : "null",
            basicInfo.getKnowledgePoints().size());

        try {
            // 1. 获取学科
            log.debug("步骤1: 获取学科 - {}", basicInfo.getSubject().getName());
            Subject subject = findOrCreateSubject(basicInfo.getSubject());
            log.info("学科处理完成: ID={}, name={}", subject.getId(), subject.getName());

            // 2. 获取版本
            log.debug("步骤2: 获取版本 - {}", basicInfo.getBookversion().getName());
            SubjectVersion version = findOrCreateVersion(subject, basicInfo.getBookversion());
            log.info("版本处理完成: ID={}, name={}, schoolLevel={}, subject_id={}",
                version.getId(), version.getName(), version.getSchoolLevel(), version.getSubject().getId());

            // 3. 获取章节
            log.debug("步骤3: 获取章节 - {}", basicInfo.getChapter().getName());
            Chapter chapter = findOrCreateChapter(version, basicInfo.getChapter());
            log.info("章节处理完成: ID={}, name={}, subject_version_id={}",
                chapter.getId(), chapter.getName(), chapter.getSubjectVersion().getId());

            // 4. 获取知识点（取第一个）
            SourceKnowledgePoint sourceKp = basicInfo.getKnowledgePoints().get(0);
            log.debug("步骤4: 获取知识点 - {}", sourceKp.getName());
            KnowledgePoint knowledgePoint = findOrCreateKnowledgePoint(chapter, sourceKp);
            log.info("知识点处理完成: ID={}, name={}, chapter_id={}",
                knowledgePoint.getId(), knowledgePoint.getName(), knowledgePoint.getChapter().getId());

            log.info("知识点ID解析成功: {}", knowledgePoint.getId());
            return knowledgePoint.getId();
            
        } catch (Exception e) {
            log.error("解析知识点ID失败: {}", basicInfo, e);
            throw new HierarchyResolutionException("无法解析知识点ID", e);
        }
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        subjectCache.clear();
        versionCache.clear();
        chapterCache.clear();
        knowledgePointCache.clear();
        log.info("层级数据缓存已清理");
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Integer> getCacheStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("subjects", subjectCache.size());
        stats.put("versions", versionCache.size());
        stats.put("chapters", chapterCache.size());
        stats.put("knowledgePoints", knowledgePointCache.size());
        return stats;
    }
}
