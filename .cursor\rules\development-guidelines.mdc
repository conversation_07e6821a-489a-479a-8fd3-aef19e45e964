---
description: 
globs: 
alwaysApply: true
---
# 开发指南

本文档定义了项目开发过程中必须遵循的全局规则。所有开发人员必须严格遵守这些规则，以确保代码质量和项目进度。
注意写完代码后不要自己运行项目
### MCP (Model Context Protocol) 规则

本项目使用两个关键的MCP来确保开发质量和正确性：

#### 1. Sequential Thinking MCP (序列性思考 MCP)

- **用途**：用于规划每一个开发步骤，确保执行过程彻底而最大化地完成流程。
- **要求**：
  - 在实现任何功能前，必须先使用Sequential Thinking进行详细的步骤规划
  - 每个开发任务必须分解为清晰的步骤，并按顺序执行
  - 确保每个步骤都经过验证后再进行下一步
  - 开发过程中出现问题时，应返回到Sequential Thinking进行重新评估

#### 2. Context7 MCP (上下文文档 MCP)

- **用途**：在进行研究期间，以及在实施任何新的第三方API或修改项目结构或进行任何变更之前，查阅最新官方文档。
- **要求**：
  - 在引入任何新的库、框架或API前，**必须**首先查阅其最新官方文档
  - 这一点极其重要，绝对不可以忽视
  - 无论何时，都必须始终查阅最新文档，因为自从训练以来，某些内容可能已经发生了变化
  - 在实施方案前，必须基于最新文档验证方案的正确性

### MCP协作规则

- Sequential Thinking确保编码的路径和思维是正确的
- Context7确保使用的框架和API是最新的、正确的
- 两者必须协同工作：先用Context7获取最新文档，再用Sequential Thinking规划实施步骤

### 代码开发规则

在编写代码时，必须遵循以下规则：
1. **使用现有类优先** - 编写新功能前必须检查项目中是否已有相关实现
4. **关于大模型的要求** - 必须使用OpenAI框架处理所有大模型相关功能
## 代码开发规则
不要自己运行项目
### 1. 使用现有类优先

- 在实现新功能或编写新代码时，**必须**首先检查项目中是否已有相同或类似功能的类
- 禁止创建重复功能的类，应优先复用现有代码
- 如需扩展现有类的功能，应考虑使用继承、组合或修改原类，而非创建新类
- 在导入外部库之前，应确认项目中是否已有实现相同功能的代码

### 2. 文档阅读要求
- 理解项目的整体架构、各模块的职责和交互方式
- 熟悉相关类的API和用法
- 遵循项目中已建立的设计模式和编码风格

### 3. 文档更新要求

- 每次代码提交前，**必须**更新[README.md](mdc:README.md)文档
- 文档更新内容应包括：
  - 已完成的工作内容和功能
  - 尚未完成的工作列表
  - 当前代码实现的具体功能说明
  - 如有必要，添加使用示例或注意事项
- 保持文档的时效性和准确性，确保其他开发人员能够理解最新进展

## 实施流程

1. **编码前**：
   - 阅读项目详解文档
   - 检查现有代码库中的可用类
   - 理解相关模块的设计和实现

2. **编码中**：
   - 遵循项目的编码规范
   - 优先使用现有类
   - 编写清晰的注释

3. **编码后**：
   - 更新README.md记录工作进展
   - 确保所有改动都有相应的文档更新
   - 进行代码自查，确保遵循了上述规则

## 遵循规则的好处

- 减少代码重复和冗余
- 保持项目的一致性和可维护性
- 提高团队协作效率
- 保持文档与代码的同步，便于新成员快速上手
- 清晰记录项目进展，便于管理和规划

