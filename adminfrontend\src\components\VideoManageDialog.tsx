import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  Avatar,
} from '@mui/material';
import {
  Close,
  Delete,
  Add,
  VideoLibrary,
  PlayArrow,
  CloudUpload,
} from '@mui/icons-material';
import { curriculumAPI } from '../services/api';
import VideoUploadDialog from './VideoUploadDialog';

interface Video {
  id: number;
  title: string;
  videoUrl: string;
  coverImageUrl?: string;
  createdAt: string;
}

interface VideoCollection {
  id: number;
  name: string;
  description?: string;
  coverImageUrl?: string;
  videos: Video[];
}

interface VideoManageDialogProps {
  open: boolean;
  onClose: () => void;
  knowledgePointId: number | null;
  onVideoAdded?: () => void;
  onVideoRemoved?: () => void;
}

const VideoManageDialog: React.FC<VideoManageDialogProps> = ({
  open,
  onClose,
  knowledgePointId,
  onVideoAdded,
  onVideoRemoved,
}) => {
  const [loading, setLoading] = useState(false);
  const [videoCollection, setVideoCollection] = useState<VideoCollection | null>(null);
  const [error, setError] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);

  // 加载视频集合信息
  const loadVideoCollection = async () => {
    if (!knowledgePointId) return;

    try {
      setLoading(true);
      setError('');
      
      const response = await curriculumAPI.getKnowledgePointVideo(knowledgePointId);
      
      if (response.hasVideo) {
        // 获取视频集合的详细信息
        const collectionResponse = await curriculumAPI.getVideoCollectionDetails(response.videoCollectionId);
        setVideoCollection(collectionResponse);
      } else {
        setVideoCollection(null);
      }
    } catch (err: any) {
      console.error('加载视频集合失败:', err);
      setError('加载视频集合失败: ' + (err.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && knowledgePointId) {
      loadVideoCollection();
    }
  }, [open, knowledgePointId]);

  // 删除单个视频
  const handleDeleteVideo = async (videoId: number) => {
    if (!window.confirm('确定要删除这个视频吗？')) return;

    try {
      await curriculumAPI.removeVideoFromCollection(videoCollection!.id, videoId);
      await loadVideoCollection();
      onVideoRemoved?.();
    } catch (err: any) {
      console.error('删除视频失败:', err);
      setError('删除视频失败: ' + (err.message || '未知错误'));
    }
  };

  // 添加视频
  const handleAddVideo = () => {
    setOpenAddDialog(true);
  };

  // 视频添加成功回调
  const handleVideoAdded = async () => {
    setOpenAddDialog(false);
    await loadVideoCollection();
    onVideoAdded?.();
  };

  // 移除整个视频集合
  const handleRemoveAllVideos = async () => {
    if (!window.confirm('确定要移除该知识点的所有视频吗？')) return;

    try {
      await curriculumAPI.removeVideoFromKnowledgePoint(knowledgePointId!);
      setVideoCollection(null);
      onVideoRemoved?.();
      onClose();
    } catch (err: any) {
      console.error('移除视频集合失败:', err);
      setError('移除视频集合失败: ' + (err.message || '未知错误'));
    }
  };



  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <VideoLibrary sx={{ mr: 1, color: 'primary.main' }} />
              视频集合管理
            </Box>
            <IconButton onClick={onClose} size="small">
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          ) : videoCollection ? (
            <Box>
              {/* 视频集合信息 */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{ width: 60, height: 60, mr: 2, bgcolor: 'primary.main' }}
                    >
                      <VideoLibrary />
                    </Avatar>
                    <Box>
                      <Typography variant="h6">{videoCollection.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {videoCollection.description || '暂无描述'}
                      </Typography>
                      <Chip
                        label={`${videoCollection.videos.length} 个视频`}
                        size="small"
                        color="primary"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* 视频列表 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">视频列表</Typography>
                <Button
                  variant="outlined"
                  startIcon={<Add />}
                  onClick={handleAddVideo}
                  size="small"
                >
                  添加视频
                </Button>
              </Box>

              {videoCollection.videos.length === 0 ? (
                <Alert severity="info">
                  该视频集合暂无视频，点击"添加视频"按钮上传视频。
                </Alert>
              ) : (
                <List>
                  {videoCollection.videos.map((video, index) => (
                    <React.Fragment key={video.id}>
                      <ListItem>
                        <Avatar
                          sx={{ width: 48, height: 48, mr: 2, bgcolor: 'primary.main' }}
                        >
                          <PlayArrow />
                        </Avatar>
                        <ListItemText
                          primary={video.title}
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              创建时间: {new Date(video.createdAt).toLocaleString()}
                            </Typography>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => handleDeleteVideo(video.id)}
                            color="error"
                            title="删除视频"
                          >
                            <Delete />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < videoCollection.videos.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </Box>
          ) : (
            <Alert severity="info">
              该知识点暂无视频集合。
            </Alert>
          )}
        </DialogContent>

        <DialogActions>
          {videoCollection && (
            <Button
              onClick={handleRemoveAllVideos}
              color="error"
              startIcon={<Delete />}
            >
              移除所有视频
            </Button>
          )}
          <Button onClick={onClose}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 视频上传对话框 */}
      {openAddDialog && (
        <VideoUploadDialog
          open={openAddDialog}
          onClose={() => setOpenAddDialog(false)}
          onUpload={async (videoUrl: string) => {
            try {
              await curriculumAPI.addVideoToKnowledgePoint(knowledgePointId!, videoUrl);
              handleVideoAdded();
            } catch (err: any) {
              console.error('添加视频失败:', err);
              setError('添加视频失败: ' + (err.message || '未知错误'));
            }
          }}
        />
      )}
    </>
  );
};

export default VideoManageDialog;
