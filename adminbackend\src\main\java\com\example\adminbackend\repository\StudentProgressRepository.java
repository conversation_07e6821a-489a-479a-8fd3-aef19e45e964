package com.example.adminbackend.repository;

import com.example.adminbackend.model.StudentProgress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 学生进度数据访问层
 */
@Repository
public interface StudentProgressRepository extends JpaRepository<StudentProgress, Long> {

    /**
     * 根据知识点ID查找所有学生进度记录
     */
    List<StudentProgress> findByKnowledgePointId(Long knowledgePointId);

    /**
     * 根据知识点ID删除所有相关记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM StudentProgress sp WHERE sp.knowledgePoint.id = :knowledgePointId")
    void deleteByKnowledgePointId(@Param("knowledgePointId") Long knowledgePointId);

    /**
     * 根据学生ID查找所有进度记录
     */
    List<StudentProgress> findByStudentId(Long studentId);

    /**
     * 检查知识点是否有学生进度记录
     */
    boolean existsByKnowledgePointId(Long knowledgePointId);

    /**
     * 统计知识点的进度记录数量
     */
    @Query("SELECT COUNT(sp) FROM StudentProgress sp WHERE sp.knowledgePoint.id = :knowledgePointId")
    long countByKnowledgePointId(@Param("knowledgePointId") Long knowledgePointId);

    /**
     * 根据学生ID和知识点ID查找进度记录
     */
    List<StudentProgress> findByStudentIdAndKnowledgePointId(Long studentId, Long knowledgePointId);
}
