import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';
import { User, UserFormData, UserRole } from '../types';
import { userAPI, supervisorAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { getRoleLabel, getManageableRoles } from '../utils/roleUtils';

interface UserFormProps {
  user?: User | null;
  onSubmit: () => void;
  onCancel: () => void;
  supervisorMode?: boolean; // 是否为督学管理模式
}

const UserForm: React.FC<UserFormProps> = ({ user, onSubmit, onCancel, supervisorMode = false }) => {
  const { isSuperAdmin, user: currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    fullName: '',
    phone: '',
    password: '',
    role: 'SUPERVISOR',
    enabled: true,
  });

  // 初始化表单数据
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        fullName: user.fullName,
        phone: user.phone,
        password: '', // 编辑时密码为空，表示不修改
        role: user.role,
        enabled: user.enabled,
      });
    } else {
      setFormData({
        username: '',
        fullName: '',
        phone: '',
        password: '',
        role: 'SUPERVISOR',
        enabled: true,
      });
    }
  }, [user]);

  const handleChange = (field: keyof UserFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 表单验证
    if (!formData.username || !formData.fullName || !formData.phone) {
      setError('请填写所有必填字段');
      return;
    }

    if (!user && !formData.password) {
      setError('新用户必须设置密码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const submitData = { ...formData };
      
      // 如果是编辑且密码为空，则不提交密码字段
      if (user && !formData.password) {
        delete submitData.password;
      }

      if (supervisorMode) {
        // 督学管理模式，使用督学专用 API
        if (user) {
          await supervisorAPI.updateSupervisor(user.id, submitData);
        } else {
          await supervisorAPI.createSupervisor(submitData);
        }
      } else {
        // 普通用户管理模式
        if (user) {
          await userAPI.updateUser(user.id, submitData, isSuperAdmin);
        } else {
          await userAPI.createUser(submitData, isSuperAdmin);
        }
      }
      
      onSubmit();
    } catch (error: any) {
      console.error('保存用户失败:', error);
      setError(error.response?.data?.message || '保存用户失败');
    } finally {
      setLoading(false);
    }
  };

  // 根据当前用户角色和模式获取可选择的角色选项
  const roleOptions = supervisorMode
    ? [{ value: 'SUPERVISOR' as UserRole, label: getRoleLabel('SUPERVISOR') }] // 督学管理模式只能选择督学
    : getManageableRoles(currentUser?.role || 'SUPERVISOR').map(role => ({
        value: role,
        label: getRoleLabel(role)
      }));

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TextField
        fullWidth
        label="用户名"
        value={formData.username}
        onChange={handleChange('username')}
        margin="normal"
        required
        disabled={loading}
      />

      <TextField
        fullWidth
        label="姓名"
        value={formData.fullName}
        onChange={handleChange('fullName')}
        margin="normal"
        required
        disabled={loading}
      />

      <TextField
        fullWidth
        label="手机号"
        value={formData.phone}
        onChange={handleChange('phone')}
        margin="normal"
        required
        disabled={loading}
      />

      <TextField
        fullWidth
        label={user ? "密码（留空表示不修改）" : "密码"}
        type="password"
        value={formData.password}
        onChange={handleChange('password')}
        margin="normal"
        required={!user}
        disabled={loading}
      />

      <FormControl fullWidth margin="normal">
        <InputLabel>角色</InputLabel>
        <Select
          value={formData.role}
          label="角色"
          onChange={handleChange('role')}
          disabled={loading}
        >
          {roleOptions.map(option => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <FormControlLabel
        control={
          <Switch
            checked={formData.enabled}
            onChange={handleChange('enabled')}
            disabled={loading}
          />
        }
        label="启用用户"
        sx={{ mt: 2, mb: 2 }}
      />

      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
        <Button
          onClick={onCancel}
          disabled={loading}
          startIcon={<CancelIcon />}
        >
          取消
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
        >
          {loading ? '保存中...' : '保存'}
        </Button>
      </Box>
    </Box>
  );
};

export default UserForm;
