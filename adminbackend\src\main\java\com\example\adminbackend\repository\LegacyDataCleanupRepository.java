package com.example.adminbackend.repository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * 遗留数据清理Repository
 * 用于清理已弃用表中的数据
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class LegacyDataCleanupRepository {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 清理知识点相关的遗留数据
     * @param knowledgePointId 知识点ID
     * @return 清理的记录数量
     */
    public int cleanupKnowledgePointLegacyData(Long knowledgePointId) {
        int totalDeleted = 0;
        
        // 清理learning_steps表（如果存在）
        totalDeleted += cleanupLearningSteps(knowledgePointId);
        
        // 可以在这里添加其他遗留表的清理逻辑
        
        return totalDeleted;
    }

    /**
     * 清理learning_steps表中的数据
     */
    private int cleanupLearningSteps(Long knowledgePointId) {
        try {
            // 首先检查表是否存在
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables " +
                                  "WHERE table_schema = DATABASE() AND table_name = 'learning_steps'";
            
            Integer tableCount = jdbcTemplate.queryForObject(checkTableSql, Integer.class);
            
            if (tableCount != null && tableCount > 0) {
                // 表存在，删除相关记录
                String deleteSql = "DELETE FROM learning_steps WHERE knowledge_point_id = ?";
                int deleted = jdbcTemplate.update(deleteSql, knowledgePointId);
                
                if (deleted > 0) {
                    log.info("清理learning_steps表中知识点{}的{}条记录", knowledgePointId, deleted);
                }
                
                return deleted;
            } else {
                log.debug("learning_steps表不存在，跳过清理");
                return 0;
            }
        } catch (Exception e) {
            log.warn("清理learning_steps表失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 清理学科版本相关的遗留数据
     * @param subjectVersionId 学科版本ID
     * @return 清理的记录数量
     */
    public int cleanupSubjectVersionLegacyData(Long subjectVersionId) {
        int totalDeleted = 0;
        
        // 可以在这里添加学科版本相关的遗留表清理逻辑
        
        return totalDeleted;
    }

    /**
     * 清理章节相关的遗留数据
     * @param chapterId 章节ID
     * @return 清理的记录数量
     */
    public int cleanupChapterLegacyData(Long chapterId) {
        int totalDeleted = 0;
        
        // 可以在这里添加章节相关的遗留表清理逻辑
        
        return totalDeleted;
    }
}
