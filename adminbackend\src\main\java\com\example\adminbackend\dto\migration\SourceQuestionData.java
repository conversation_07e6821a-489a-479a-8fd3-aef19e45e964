package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 源数据题目结构DTO
 * 对应待导入数据的数据结构
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceQuestionData {
    
    /**
     * 题目类型
     */
    private String type;
    
    /**
     * 题目ID
     */
    private Long id;
    
    /**
     * 学科
     */
    private String subject;
    
    /**
     * 难度
     */
    private String difficulty;
    
    /**
     * 题目内容（HTML格式）
     */
    private String content;
    
    /**
     * 题目选项（选择题使用）
     */
    private List<String> options;
    
    /**
     * 答案
     */
    private Object answer;
    
    /**
     * 解析（HTML格式）
     */
    private String explanation;
    
    /**
     * 标签
     */
    private List<String> tags;
    
    /**
     * 材料内容（复合题型使用）
     */
    private String material;
    
    /**
     * 子题目列表（复合题型使用）
     */
    private List<SourceSubQuestion> subQuestions;
    
    /**
     * 基础信息（层级信息）
     */
    private BasicInfo basicInfo;
}
