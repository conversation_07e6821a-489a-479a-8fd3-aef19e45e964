package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Objects;

/**
 * 源数据知识点信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceKnowledgePoint {
    
    /**
     * 知识点ID
     */
    private Long id;
    
    /**
     * 知识点名称
     */
    private String name;
    
    /**
     * 视频URL
     */
    private String video;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SourceKnowledgePoint that = (SourceKnowledgePoint) o;
        return Objects.equals(name, that.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
