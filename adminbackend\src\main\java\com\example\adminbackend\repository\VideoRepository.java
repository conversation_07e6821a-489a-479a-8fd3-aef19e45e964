package com.example.adminbackend.repository;

import com.example.adminbackend.model.Video;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 视频数据访问层
 */
@Repository
public interface VideoRepository extends JpaRepository<Video, Long> {

    /**
     * 根据视频URL查找视频
     */
    Optional<Video> findByVideoUrl(String videoUrl);

    /**
     * 根据标题查找视频
     */
    Optional<Video> findByTitle(String title);

    /**
     * 检查视频URL是否存在
     */
    boolean existsByVideoUrl(String videoUrl);

    /**
     * 检查视频标题是否存在
     */
    boolean existsByTitle(String title);

    /**
     * 根据标题模糊查询视频
     */
    @Query("SELECT v FROM Video v WHERE v.title LIKE %:keyword% OR v.description LIKE %:keyword%")
    List<Video> findByKeyword(@Param("keyword") String keyword);

    /**
     * 获取所有视频，按创建时间排序
     */
    List<Video> findAllByOrderByCreatedAtDesc();

    /**
     * 获取所有视频，按标题排序
     */
    List<Video> findAllByOrderByTitleAsc();
}
