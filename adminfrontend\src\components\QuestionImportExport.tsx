import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  FileUpload as FileUploadIcon,
  FileDownload as FileDownloadIcon,
  CloudUpload as CloudUploadIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { questionAPI } from '../services/api';
import { QuestionImportResult, SubjectEnum, QuestionType } from '../types';
import { questionValidator, ValidationResult } from '../utils/questionValidator';

interface QuestionImportExportProps {
  open: boolean;
  onClose: () => void;
  mode: 'import' | 'export';
  onImportSuccess?: () => void;
}

const QuestionImportExport: React.FC<QuestionImportExportProps> = ({
  open,
  onClose,
  mode,
  onImportSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [importResult, setImportResult] = useState<QuestionImportResult | null>(null);

  // 导入相关状态
  const [jsonContent, setJsonContent] = useState('');
  const [knowledgePointId, setKnowledgePointId] = useState<number>(1);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 前端验证相关状态
  const [validationResults, setValidationResults] = useState<{ index: number; result: ValidationResult }[]>([]);
  const [showValidationDetails, setShowValidationDetails] = useState(false);
  
  // 导出相关状态
  const [exportFilters, setExportFilters] = useState({
    knowledgePointId: undefined as number | undefined,
    questionType: '' as QuestionType | '',
    subject: '' as SubjectEnum | '',
  });

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/json') {
      setError('请选择JSON格式的文件');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        // 验证JSON格式
        const parsedData = JSON.parse(content);
        setJsonContent(content);
        setError(null);

        // 执行前端预验证
        if (Array.isArray(parsedData)) {
          performPreValidation(parsedData);
        }
      } catch (err) {
        setError('JSON格式不正确，请检查文件内容');
      }
    };
    reader.readAsText(file);
  };

  // 执行前端预验证
  const performPreValidation = (questionBodies: any[]) => {
    const results = questionValidator.validateQuestionBodies(questionBodies);
    setValidationResults(results);

    const hasErrors = results.some(r => !r.result.valid);
    if (hasErrors) {
      setShowValidationDetails(true);
    }
  };

  // 处理JSON内容变化
  const handleJsonContentChange = (content: string) => {
    setJsonContent(content);
    setValidationResults([]);
    setShowValidationDetails(false);

    if (content.trim()) {
      try {
        const parsedData = JSON.parse(content);
        if (Array.isArray(parsedData)) {
          performPreValidation(parsedData);
        }
      } catch (err) {
        // JSON格式错误，不执行验证
      }
    }
  };

  // 处理导入
  const handleImport = async () => {
    if (!jsonContent.trim()) {
      setError('请输入或上传题目JSON数据');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const questionBodies = JSON.parse(jsonContent);

      // 验证数据格式
      if (!Array.isArray(questionBodies)) {
        throw new Error('数据格式错误：应该是题目数组');
      }

      // 检查前端验证结果
      const hasValidationErrors = validationResults.some(r => !r.result.valid);
      if (hasValidationErrors) {
        const errorCount = validationResults.filter(r => !r.result.valid).length;
        throw new Error(`数据验证失败：${errorCount} 个题目存在格式错误，请修复后重试`);
      }

      const result = await questionAPI.importQuestions(knowledgePointId, questionBodies);
      setImportResult(result);
      
      if (result.success > 0) {
        onImportSuccess?.();
      }
    } catch (err: any) {
      console.error('导入失败:', err);
      setError(err.message || '导入失败，请检查数据格式');
    } finally {
      setLoading(false);
    }
  };

  // 处理导出
  const handleExport = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        knowledgePointId: exportFilters.knowledgePointId,
        questionType: exportFilters.questionType || undefined,
        subject: exportFilters.subject || undefined,
      };

      console.log('导出参数:', params);

      const data = await questionAPI.exportQuestions(params);

      console.log('导出数据:', data);

      if (!data || (Array.isArray(data) && data.length === 0)) {
        setError('没有找到符合条件的题目');
        return;
      }

      // 创建下载链接
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `questions_export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      onClose();
    } catch (err: any) {
      console.error('导出失败:', err);
      if (err.response) {
        console.error('响应错误:', err.response.data);
        setError(`导出失败: ${err.response.data.message || err.response.statusText}`);
      } else if (err.request) {
        setError('网络错误：无法连接到服务器');
      } else {
        setError(err.message || '导出失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 重置状态
  const handleClose = () => {
    setJsonContent('');
    setError(null);
    setImportResult(null);
    setValidationResults([]);
    setShowValidationDetails(false);
    setExportFilters({
      knowledgePointId: undefined,
      questionType: '',
      subject: '',
    });
    onClose();
  };

  // 示例JSON数据
  const exampleJson = `[
  {
    "type": "SINGLE_CHOICE",
    "id": "example_001",
    "subject": "ENGLISH",
    "difficulty": "EASY",
    "tags": ["动词时态", "一般现在时"],
    "content": "<p>She _____ to school every day.</p>",
    "options": [
      "<p>go</p>",
      "<p>goes</p>",
      "<p>went</p>",
      "<p>going</p>"
    ],
    "answer": "B",
    "explanation": "<p>主语She是第三人称单数，一般现在时动词要加s。</p>"
  }
]`;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'import' ? '导入题目' : '导出题目'}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
            <Typography variant="body2" sx={{ mt: 1 }}>
              {mode === 'import' ? '正在导入题目...' : '正在导出题目...'}
            </Typography>
          </Box>
        )}

        {mode === 'import' ? (
          <Box>
            {/* 知识点选择 */}
            <TextField
              fullWidth
              label="知识点ID"
              type="number"
              value={knowledgePointId}
              onChange={(e) => setKnowledgePointId(Number(e.target.value))}
              sx={{ mb: 3 }}
              helperText="题目将导入到指定的知识点下"
            />

            {/* 文件上传 */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                上传JSON文件
              </Typography>
              <input
                ref={fileInputRef}
                type="file"
                accept=".json"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />
              <Button
                variant="outlined"
                startIcon={<CloudUploadIcon />}
                onClick={() => fileInputRef.current?.click()}
                sx={{ mb: 2 }}
              >
                选择文件
              </Button>
            </Box>

            <Divider sx={{ my: 2 }}>或</Divider>

            {/* 直接输入JSON */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                直接输入JSON数据
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={12}
                placeholder="请输入题目JSON数据..."
                value={jsonContent}
                onChange={(e) => handleJsonContentChange(e.target.value)}
                sx={{ fontFamily: 'monospace' }}
              />
            </Box>

            {/* 示例数据 */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                JSON格式示例：
              </Typography>
              <Box
                sx={{
                  bgcolor: 'grey.100',
                  p: 2,
                  borderRadius: 1,
                  fontSize: '12px',
                  fontFamily: 'monospace',
                  overflow: 'auto',
                  maxHeight: 200,
                }}
              >
                <pre>{exampleJson}</pre>
              </Box>
              <Button
                size="small"
                onClick={() => handleJsonContentChange(exampleJson)}
                sx={{ mt: 1 }}
              >
                使用示例数据
              </Button>
            </Box>

            {/* 前端验证结果 */}
            {validationResults.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Accordion expanded={showValidationDetails} onChange={() => setShowValidationDetails(!showValidationDetails)}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {validationResults.every(r => r.result.valid) ? (
                        <>
                          <CheckCircleIcon color="success" />
                          <Typography color="success.main">
                            数据验证通过 ({validationResults.length} 个题目)
                          </Typography>
                        </>
                      ) : (
                        <>
                          <ErrorIcon color="error" />
                          <Typography color="error.main">
                            发现 {validationResults.filter(r => !r.result.valid).length} 个题目存在错误
                          </Typography>
                        </>
                      )}
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {validationResults.map(({ index, result }) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, width: '100%' }}>
                            {result.valid ? (
                              <CheckCircleIcon color="success" fontSize="small" sx={{ mt: 0.5 }} />
                            ) : (
                              <ErrorIcon color="error" fontSize="small" sx={{ mt: 0.5 }} />
                            )}
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="body2" fontWeight="bold">
                                第 {index + 1} 题
                              </Typography>
                              {!result.valid && (
                                <Box sx={{ mt: 0.5 }}>
                                  {result.errors.map((error, errorIndex) => (
                                    <Chip
                                      key={errorIndex}
                                      label={error.fixSuggestion}
                                      size="small"
                                      color="error"
                                      variant="outlined"
                                      sx={{ mr: 0.5, mb: 0.5 }}
                                    />
                                  ))}
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}

            {/* 导入结果 */}
            {importResult && (
              <Alert 
                severity={importResult.fail > 0 ? 'warning' : 'success'} 
                sx={{ mb: 2 }}
              >
                <Typography variant="body2" gutterBottom>
                  导入完成：成功 {importResult.success} 个，失败 {importResult.fail} 个
                </Typography>
                {importResult.errors.length > 0 && (
                  <Box>
                    <Typography variant="body2" fontWeight="bold" gutterBottom>
                      错误信息：
                    </Typography>
                    <List dense>
                      {importResult.errors.map((error, index) => (
                        <ListItem key={index} sx={{ py: 0 }}>
                          <ListItemText 
                            primary={error}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}
              </Alert>
            )}
          </Box>
        ) : (
          <Box>
            {/* 导出筛选条件 */}
            <Typography variant="subtitle1" gutterBottom>
              导出筛选条件
            </Typography>
            
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
              <TextField
                label="知识点ID"
                type="number"
                value={exportFilters.knowledgePointId || ''}
                onChange={(e) => setExportFilters(prev => ({
                  ...prev,
                  knowledgePointId: e.target.value ? Number(e.target.value) : undefined
                }))}
                helperText="留空导出所有知识点"
              />
              
              <FormControl>
                <InputLabel>题型</InputLabel>
                <Select
                  value={exportFilters.questionType}
                  label="题型"
                  onChange={(e) => setExportFilters(prev => ({
                    ...prev,
                    questionType: e.target.value as QuestionType
                  }))}
                >
                  <MenuItem value="">全部题型</MenuItem>
                  <MenuItem value="SINGLE_CHOICE">单选题</MenuItem>
                  <MenuItem value="MULTIPLE_CHOICE">多选题</MenuItem>
                  <MenuItem value="FILL_IN_BLANK">填空题</MenuItem>
                  <MenuItem value="TRUE_FALSE">判断题</MenuItem>
                  <MenuItem value="MATCHING">匹配题</MenuItem>
                  <MenuItem value="READING_COMPREHENSION">阅读理解</MenuItem>
                  <MenuItem value="CLOZE_TEST">完形填空</MenuItem>
                  <MenuItem value="LISTENING">听力题</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl>
                <InputLabel>科目</InputLabel>
                <Select
                  value={exportFilters.subject}
                  label="科目"
                  onChange={(e) => setExportFilters(prev => ({
                    ...prev,
                    subject: e.target.value as SubjectEnum
                  }))}
                >
                  <MenuItem value="">全部科目</MenuItem>
                  <MenuItem value="ENGLISH">英语</MenuItem>
                  <MenuItem value="MATH">数学</MenuItem>
                  <MenuItem value="PHYSICS">物理</MenuItem>
                  <MenuItem value="CHEMISTRY">化学</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Alert severity="info">
              导出的JSON文件可以直接用于题目导入功能
            </Alert>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          取消
        </Button>
        <Button
          variant="contained"
          onClick={mode === 'import' ? handleImport : handleExport}
          disabled={loading || (mode === 'import' && !jsonContent.trim())}
          startIcon={mode === 'import' ? <FileUploadIcon /> : <FileDownloadIcon />}
        >
          {mode === 'import' ? '导入' : '导出'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default QuestionImportExport;
