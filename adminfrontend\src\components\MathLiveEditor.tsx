import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Box, Typography } from '@mui/material';
import { mathLiveInputManager } from './keyborad/MathLiveInputManager';

// 扩展HTMLElement接口以支持MathLive方法
interface MathfieldElement extends HTMLElement {
  getValue(format?: string): string;
  setValue(latex: string): void;
  executeCommand(command: string | string[]): boolean;
  insert(s: string, options?: any): boolean;
  fontsDirectory?: string;
}

// MathfieldElement类的静态属性
interface MathfieldElementConstructor {
  fontsDirectory: string;
}

declare global {
  const MathfieldElement: MathfieldElementConstructor;
}

// MathLive编辑器属性接口
export interface MathLiveEditorProps {
  /** 数学公式的LaTeX值 */
  value?: string;
  /** 值变化回调 */
  onChange?: (latex: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否只读 */
  readOnly?: boolean;
  /** 编辑器高度 */
  height?: number | string;
  /** 编辑器宽度 */
  width?: number | string;
  /** 是否显示虚拟键盘 */
  virtualKeyboard?: boolean;
  /** 虚拟键盘策略 */
  virtualKeyboardPolicy?: 'auto' | 'manual' | 'sandboxed';
  /** 是否使用自定义键盘 */
  useCustomKeyboard?: boolean;
  /** 是否启用智能围栏 */
  smartFence?: boolean;
  /** 是否启用智能模式 */
  smartMode?: boolean;
  /** 是否启用智能上标 */
  smartSuperscript?: boolean;
  /** 内联快捷键超时时间 */
  inlineShortcutTimeout?: number;
  /** 字体目录 */
  fontsDirectory?: string;
  /** 焦点事件 */
  onFocus?: () => void;
  /** 失焦事件 */
  onBlur?: () => void;
  /** 挂载事件 */
  onMount?: () => void;
  /** 错误事件 */
  onError?: (error: any) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** CSS类名 */
  className?: string;
}

// MathLive编辑器引用接口
export interface MathLiveEditorRef {
  /** 获取LaTeX值 */
  getValue: () => string;
  /** 设置LaTeX值 */
  setValue: (latex: string) => void;
  /** 获取焦点 */
  focus: () => void;
  /** 失去焦点 */
  blur: () => void;
  /** 选择全部内容 */
  selectAll: () => void;
  /** 清空内容 */
  clear: () => void;
  /** 执行命令 */
  executeCommand: (command: string) => void;
  /** 获取原生DOM元素 */
  getElement: () => HTMLElement | null;
}

const MathLiveEditor = forwardRef<MathLiveEditorRef, MathLiveEditorProps>(({
  value = '',
  onChange,
  placeholder = '输入数学公式...',
  readOnly = false,
  height = 60,
  width = '100%',
  virtualKeyboard = true,
  virtualKeyboardPolicy = 'auto',
  useCustomKeyboard = true,
  smartFence = true,
  smartMode = true,
  smartSuperscript = true,
  inlineShortcutTimeout = 0,
  fontsDirectory,
  onFocus,
  onBlur,
  onMount,
  onError,
  style,
  className,
}, ref) => {
  const mathFieldRef = useRef<HTMLElement>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 移除自定义键盘状态管理，现在由 GlobalKeyboard 和 mathLiveInputManager 处理

  // 后处理LaTeX代码，确保正确的括号格式
  const postProcessLatex = (latex: string): string => {
    if (!latex) return latex;

    let processed = latex;

    // 修复分数格式：\frac43 -> \frac{4}{3}
    // 处理两个连续的非括号字符
    processed = processed.replace(/\\frac([^{\\])([^{\\])/g, '\\frac{$1}{$2}');

    // 处理单个字符后跟数字的情况
    processed = processed.replace(/\\frac([^{\\])(\d+)/g, '\\frac{$1}{$2}');
    processed = processed.replace(/\\frac(\d+)([^{\\])/g, '\\frac{$1}{$2}');

    // 修复上标格式：确保多字符上标有括号
    processed = processed.replace(/\^([a-zA-Z0-9]{2,})/g, '^{$1}');
    processed = processed.replace(/\^([^{\\])([a-zA-Z0-9]+)/g, '^{$1$2}');

    // 修复下标格式：确保多字符下标有括号
    processed = processed.replace(/_([a-zA-Z0-9]{2,})/g, '_{$1}');
    processed = processed.replace(/_([^{\\])([a-zA-Z0-9]+)/g, '_{$1$2}');

    // 修复根号格式：\sqrt2 -> \sqrt{2}
    processed = processed.replace(/\\sqrt([^{\\][^\\]*?)(\s|$|\\)/g, '\\sqrt{$1}$2');

    // 修复其他常见函数的参数格式
    const functions = ['sin', 'cos', 'tan', 'log', 'ln', 'exp', 'lim', 'max', 'min'];
    functions.forEach(func => {
      const regex = new RegExp(`\\\\${func}([^{\\s\\\\][\\w\\d]*)`, 'g');
      processed = processed.replace(regex, `\\${func}{$1}`);
    });

    console.log('Original LaTeX:', latex);
    console.log('Post-processed LaTeX:', processed);
    return processed;
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    getValue: () => {
      const mathField = mathFieldRef.current as MathfieldElement;
      if (mathField && 'getValue' in mathField) {
        // 获取最佳格式的LaTeX
        const latexWithoutPlaceholders = mathField.getValue('latex-without-placeholders');
        const rawLatex = latexWithoutPlaceholders || mathField.getValue();

        // 后处理LaTeX以确保正确格式
        return postProcessLatex(rawLatex);
      }
      return '';
    },
    setValue: (latex: string) => {
      const mathField = mathFieldRef.current as MathfieldElement;
      if (mathField && 'setValue' in mathField) {
        mathField.setValue(latex);
      }
    },
    focus: () => {
      mathFieldRef.current?.focus();
    },
    blur: () => {
      mathFieldRef.current?.blur();
    },
    selectAll: () => {
      const mathField = mathFieldRef.current as MathfieldElement;
      if (mathField && 'executeCommand' in mathField) {
        mathField.executeCommand('selectAll');
      }
    },
    clear: () => {
      const mathField = mathFieldRef.current as MathfieldElement;
      if (mathField && 'setValue' in mathField) {
        mathField.setValue('');
      }
    },
    executeCommand: (command: string) => {
      const mathField = mathFieldRef.current as MathfieldElement;
      if (mathField && 'executeCommand' in mathField) {
        mathField.executeCommand(command);
      }
    },
    getElement: () => mathFieldRef.current,
  }));

  // 初始化MathLive
  useEffect(() => {
    let mounted = true;

    const initMathLive = async () => {
      try {
        // 动态导入MathLive
        const mathlive = await import('mathlive');

        // 配置MathLive全局设置
        // 使用本地安装的字体文件
        if (mathlive.MathfieldElement) {
          // 设置为本地node_modules中的字体路径
          mathlive.MathfieldElement.fontsDirectory = '/node_modules/mathlive/fonts/';
        }

        // 等待math-field元素定义完成
        await customElements.whenDefined('math-field');

        if (!mounted) return;

        // 添加全局CSS样式确保虚拟键盘有最高层级
        const existingStyle = document.getElementById('mathlive-keyboard-zindex');
        if (!existingStyle) {
          const style = document.createElement('style');
          style.id = 'mathlive-keyboard-zindex';
          style.textContent = `
            /* MathLive虚拟键盘最高层级样式 */
            .ML__keyboard,
            .ML__keyboard-container,
            .ML__virtual-keyboard {
              z-index: 2147483647 !important;
            }
            .ML__keyboard-backdrop {
              z-index: 2147483646 !important;
            }
            /* 确保键盘面板在最顶层 */
            .ML__keyboard-panel {
              z-index: 2147483647 !important;
            }
          `;
          document.head.appendChild(style);
        }

        setIsReady(true);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize MathLive:', err);
        setError('数学公式编辑器初始化失败');
        onError?.(err);
      }
    };

    initMathLive();

    return () => {
      mounted = false;
    };
  }, [onError]);

  // 使用 MathLiveInputManager 管理实例
  useEffect(() => {
    if (isReady && mathFieldRef.current && useCustomKeyboard) {
      const element = mathFieldRef.current;

      // 使用 mathLiveInputManager 创建输入实例
      mathLiveInputManager.createInput(element, {
        subject: 'math', // 默认数学科目
        onChange: onChange,
        onFocus: onFocus,
        onBlur: onBlur,
        disabled: readOnly
      });

      // 清理函数
      return () => {
        mathLiveInputManager.destroyInput(element);
      };
    }
  }, [isReady, useCustomKeyboard, onChange, onFocus, onBlur, readOnly]);

  // 设置事件监听器
  useEffect(() => {
    if (!isReady || !mathFieldRef.current) return;

    const mathField = mathFieldRef.current as MathfieldElement;

    // 输入事件监听
    const handleInput = () => {
      // 获取最佳格式的LaTeX
      const latexWithoutPlaceholders = mathField.getValue('latex-without-placeholders');
      const rawLatex = latexWithoutPlaceholders || mathField.getValue();

      // 后处理LaTeX以确保正确格式
      const processedLatex = postProcessLatex(rawLatex);
      console.log('Input event - raw LaTeX:', rawLatex);
      console.log('Input event - processed LaTeX:', processedLatex);

      onChange?.(processedLatex);
    };

    // 焦点事件监听
    const handleFocus = () => {
      onFocus?.();
    };

    // 失焦事件监听
    const handleBlur = () => {
      onBlur?.();
    };

    // 挂载事件监听
    const handleMount = () => {
      // 配置MathLive选项
      if (fontsDirectory) {
        mathField.fontsDirectory = fontsDirectory;
      }

      // 设置初始值
      if (value) {
        mathField.setValue(value);
      }

      onMount?.();
    };

    // 错误事件监听
    const handleError = (event: any) => {
      console.error('MathLive error:', event.detail);
      setError('数学公式编辑错误');
      onError?.(event.detail);
    };

    // 添加事件监听器
    mathField.addEventListener('input', handleInput);
    mathField.addEventListener('focus', handleFocus);
    mathField.addEventListener('blur', handleBlur);
    mathField.addEventListener('mount', handleMount);
    mathField.addEventListener('math-error', handleError);

    // 清理函数
    return () => {
      mathField.removeEventListener('input', handleInput);
      mathField.removeEventListener('focus', handleFocus);
      mathField.removeEventListener('blur', handleBlur);
      mathField.removeEventListener('mount', handleMount);
      mathField.removeEventListener('math-error', handleError);
    };
  }, [isReady, value, onChange, onFocus, onBlur, onMount, onError, fontsDirectory]);

  // 更新值
  useEffect(() => {
    if (isReady && mathFieldRef.current) {
      const mathField = mathFieldRef.current as MathfieldElement;
      if ('getValue' in mathField) {
        // 获取当前处理后的值
        const latexWithoutPlaceholders = mathField.getValue('latex-without-placeholders');
        const rawLatex = latexWithoutPlaceholders || mathField.getValue();
        const currentValue = postProcessLatex(rawLatex);

        if (currentValue !== value) {
          mathField.setValue(value || '');
        }
      }
    }
  }, [isReady, value]);

  // 如果出现错误，显示错误信息
  if (error) {
    return (
      <Box
        sx={{
          height,
          width,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #f44336',
          borderRadius: 1,
          backgroundColor: '#ffebee',
          color: '#f44336',
          ...style,
        }}
        className={className}
      >
        <Typography variant="body2">{error}</Typography>
      </Box>
    );
  }

  // 如果还未准备好，显示加载状态
  if (!isReady) {
    return (
      <Box
        sx={{
          height,
          width,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #ddd',
          borderRadius: 1,
          backgroundColor: '#f5f5f5',
          color: '#666',
          ...style,
        }}
        className={className}
      >
        <Typography variant="body2">正在加载数学公式编辑器...</Typography>
      </Box>
    );
  }

  return (
    <>
      <Box
        sx={{
          height,
          width,
          '& math-field': {
            width: '100%',
            height: '100%',
            border: '1px solid #ddd',
            borderRadius: 1,
            padding: 1,
            fontSize: '16px',
            '&:focus': {
              outline: 'none',
              borderColor: '#1976d2',
              boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
            },
          },
          // 确保虚拟键盘有最高的z-index
          '& .ML__keyboard': {
            zIndex: '2147483647 !important', // 最大z-index值
          },
          // 虚拟键盘背景层
          '& .ML__keyboard-backdrop': {
            zIndex: '2147483646 !important',
          },
          ...style,
        }}
        className={className}
      >
        {React.createElement('math-field', {
          ref: mathFieldRef,
          'read-only': readOnly,
          'virtual-keyboard-mode': (virtualKeyboard && !useCustomKeyboard) ? 'onfocus' : 'off',
          'virtual-keyboard-policy': useCustomKeyboard ? 'manual' : virtualKeyboardPolicy,
          'smart-fence': smartFence,
          'smart-mode': smartMode,
          'smart-superscript': smartSuperscript,
          'inline-shortcut-timeout': inlineShortcutTimeout,
          'fonts-directory': fontsDirectory,
        })}
      </Box>

      {/* 注意：自定义键盘现在由GlobalKeyboard组件统一管理 */}
    </>
  );
});

MathLiveEditor.displayName = 'MathLiveEditor';

export default MathLiveEditor;
