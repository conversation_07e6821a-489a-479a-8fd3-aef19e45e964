package com.example.adminbackend.controller;

import com.example.adminbackend.dto.QuestionCreateRequest;
import com.example.adminbackend.dto.QuestionFormCreateRequest;
import com.example.adminbackend.dto.QuestionUpdateRequest;
import com.example.adminbackend.model.Question;
import com.example.adminbackend.service.QuestionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 题库管理控制器
 */
@RestController
@RequestMapping("/questions")
@RequiredArgsConstructor
@Slf4j
public class QuestionController {

    private final QuestionService questionService;

    // ==================== 题目查询接口 ====================

    /**
     * 分页获取题目列表
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Page<Question>> getQuestions(
            @RequestParam(required = false) Long knowledgePointId,
            @RequestParam(required = false) Question.QuestionType questionType,
            @RequestParam(required = false) Question.Subject subject,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) String search,
            Pageable pageable) {

        try {
            Page<Question> questions = questionService.getQuestions(
                knowledgePointId, questionType, subject, difficulty, enabled, search, pageable);
            return ResponseEntity.ok(questions);
        } catch (Exception e) {
            log.error("获取题目列表失败", e);
            throw new RuntimeException("获取题目列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 健康检查 - 检查数据库连接和基本查询
     */
    @GetMapping("/health")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> statistics = questionService.getQuestionStatistics(null);
            result.put("status", "OK");
            result.put("statistics", statistics);
            result.put("message", "数据库连接正常");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            result.put("status", "ERROR");
            result.put("message", "数据库连接失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 根据知识点获取题目列表
     */
    @GetMapping("/knowledge-point/{knowledgePointId}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<Question>> getQuestionsByKnowledgePoint(
            @PathVariable Long knowledgePointId) {
        List<Question> questions = questionService.getQuestionsByKnowledgePoint(knowledgePointId);
        return ResponseEntity.ok(questions);
    }

    /**
     * 获取单个题目详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Question> getQuestion(@PathVariable Long id) {
        Question question = questionService.getQuestionById(id);
        return ResponseEntity.ok(question);
    }

    // ==================== 题目管理接口 ====================

    /**
     * 创建题目
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Question> createQuestion(@Valid @RequestBody QuestionCreateRequest request) {
        Question question = questionService.createQuestion(request);
        return ResponseEntity.ok(question);
    }

    /**
     * 专门为前端创建题目功能设计的API
     * 自动处理科目映射和数据验证
     */
    @PostMapping("/create-from-form")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Question> createQuestionFromForm(@Valid @RequestBody QuestionFormCreateRequest request) {
        Question question = questionService.createQuestionFromForm(request);
        return ResponseEntity.ok(question);
    }

    /**
     * 批量创建题目
     */
    @PostMapping("/batch")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<Question>> createQuestions(
            @Valid @RequestBody List<QuestionCreateRequest> requests) {
        List<Question> questions = questionService.createQuestions(requests);
        return ResponseEntity.ok(questions);
    }

    /**
     * 更新题目
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Question> updateQuestion(
            @PathVariable Long id, 
            @Valid @RequestBody QuestionUpdateRequest request) {
        Question question = questionService.updateQuestion(id, request);
        return ResponseEntity.ok(question);
    }

    /**
     * 启用/禁用题目
     */
    @PatchMapping("/{id}/enabled")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Question> toggleQuestionEnabled(
            @PathVariable Long id, 
            @RequestParam Boolean enabled) {
        Question question = questionService.toggleEnabled(id, enabled);
        return ResponseEntity.ok(question);
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Void> deleteQuestion(@PathVariable Long id) {
        questionService.deleteQuestion(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量删除题目
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Void> deleteQuestions(@RequestBody List<Long> ids) {
        questionService.deleteQuestions(ids);
        return ResponseEntity.ok().build();
    }

    // ==================== 题目导入导出接口 ====================

    /**
     * 导入题目（支持JSON格式）
     */
    @PostMapping("/import")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Map<String, Object>> importQuestions(
            @RequestParam Long knowledgePointId,
            @RequestBody List<Map<String, Object>> questionBodies) {
        Map<String, Object> result = questionService.importQuestions(knowledgePointId, questionBodies);
        return ResponseEntity.ok(result);
    }

    /**
     * 导出题目
     */
    @GetMapping("/export")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<Map<String, Object>>> exportQuestions(
            @RequestParam(required = false) Long knowledgePointId,
            @RequestParam(required = false) Question.QuestionType questionType,
            @RequestParam(required = false) Question.Subject subject) {
        List<Map<String, Object>> questions = questionService.exportQuestions(
            knowledgePointId, questionType, subject);
        return ResponseEntity.ok(questions);
    }

    // ==================== 题目统计接口 ====================

    /**
     * 获取题目统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Map<String, Object>> getQuestionStatistics(
            @RequestParam(required = false) Long knowledgePointId) {
        Map<String, Object> statistics = questionService.getQuestionStatistics(knowledgePointId);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 验证题目JSON格式
     */
    @PostMapping("/validate")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Map<String, Object>> validateQuestionBody(
            @RequestBody Map<String, Object> questionBody) {
        Map<String, Object> result = questionService.validateQuestionBody(questionBody);
        return ResponseEntity.ok(result);
    }
}
