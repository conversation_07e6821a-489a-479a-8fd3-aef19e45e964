package com.example.adminbackend.dto;

import lombok.Data;
import java.util.List;
import java.util.Date;

@Data
public class ChapterDTO {
    private Long id;
    private String name;
    private Integer orderIndex;
    private List<KnowledgePointDTO> knowledgePoints;
    private Date createdAt;
    private Date updatedAt;
    private Long subjectVersionId;
    private String subjectVersionName; // 学科版本名称
    private String schoolLevel; // 年级学期信息
    private String subjectName; // 学科名称
}