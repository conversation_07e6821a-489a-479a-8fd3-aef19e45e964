import React, { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Divider,
  Alert,
  Chip,
  Stack,
} from '@mui/material';
import {
  Close as CloseIcon,
  Functions as FunctionsIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import MathLiveEditor, { MathLiveEditorRef } from './MathLiveEditor';

export interface MathLiveDialogProps {
  /** 是否打开对话框 */
  open: boolean;
  /** 关闭对话框回调 */
  onClose: () => void;
  /** 确认回调，返回LaTeX字符串 */
  onConfirm: (latex: string) => void;
  /** 初始LaTeX值 */
  initialValue?: string;
  /** 对话框标题 */
  title?: string;
  /** 最大宽度 */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** 是否全屏 */
  fullScreen?: boolean;
}

const MathLiveDialog: React.FC<MathLiveDialogProps> = ({
  open,
  onClose,
  onConfirm,
  initialValue = '',
  title = '数学公式编辑器',
  maxWidth = 'md',
  fullScreen = false,
}) => {
  const [latex, setLatex] = useState(initialValue);
  const [displayLatex, setDisplayLatex] = useState(initialValue);
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(true);
  const mathEditorRef = useRef<MathLiveEditorRef>(null);

  // 当对话框打开时，重置状态
  useEffect(() => {
    if (open) {
      setLatex(initialValue);
      setDisplayLatex(initialValue);
      setError(null);
      setShowPreview(true);
    }
  }, [open, initialValue]);

  // 处理确认
  const handleConfirm = () => {
    const currentLatex = getCurrentLatex();
    if (!currentLatex.trim()) {
      setError('请输入数学公式');
      return;
    }

    onConfirm(currentLatex);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    onClose();
  };

  // 处理LaTeX值变化
  const handleLatexChange = (newLatex: string) => {
    setLatex(newLatex);
    setDisplayLatex(newLatex);
    setError(null);
  };

  // 获取当前编辑器的expanded LaTeX格式
  const getCurrentLatex = () => {
    return mathEditorRef.current?.getValue() || latex;
  };

  // 复制LaTeX到剪贴板
  const handleCopyLatex = async () => {
    try {
      const currentLatex = getCurrentLatex();
      await navigator.clipboard.writeText(currentLatex);
      // 可以添加一个成功提示
    } catch (err) {
      console.error('Failed to copy LaTeX:', err);
    }
  };

  // 清空编辑器
  const handleClear = () => {
    mathEditorRef.current?.clear();
    setLatex('');
    setDisplayLatex('');
    setError(null);
  };

  // 插入常用公式模板
  const insertTemplate = (template: string) => {
    mathEditorRef.current?.setValue(template);
    // 使用setTimeout确保setValue完成后再获取expanded格式
    setTimeout(() => {
      const expandedLatex = mathEditorRef.current?.getValue() || template;
      setLatex(expandedLatex);
      setDisplayLatex(expandedLatex);
    }, 50);
  };

  // 常用公式模板
  const templates = [
    { label: '分数', latex: '\\frac{a}{b}' },
    { label: '根号', latex: '\\sqrt{x}' },
    { label: '上标', latex: 'x^{2}' },
    { label: '下标', latex: 'x_{1}' },
    { label: '求和', latex: '\\sum_{i=1}^{n} x_i' },
    { label: '积分', latex: '\\int_{a}^{b} f(x) dx' },
    { label: '极限', latex: '\\lim_{x \\to \\infty} f(x)' },
    { label: '矩阵', latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}' },
  ];

  return (
    <Dialog
      open={open}
      onClose={handleCancel}
      maxWidth={maxWidth}
      fullWidth
      fullScreen={fullScreen}
      PaperProps={{
        sx: {
          minHeight: fullScreen ? '100vh' : '600px',
          maxHeight: fullScreen ? '100vh' : '80vh',
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 1,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FunctionsIcon color="primary" />
          <Typography variant="h6">{title}</Typography>
        </Box>
        <IconButton onClick={handleCancel} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
        {/* 常用公式模板 */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            常用公式模板：
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {templates.map((template, index) => (
              <Chip
                key={index}
                label={template.label}
                variant="outlined"
                size="small"
                onClick={() => insertTemplate(template.latex)}
                sx={{ cursor: 'pointer' }}
              />
            ))}
          </Stack>
        </Box>

        <Divider />

        {/* 数学公式编辑器 */}
        <Box sx={{ flex: 1, minHeight: 200 }}>
          <Typography variant="subtitle2" gutterBottom>
            数学公式编辑器：
          </Typography>
          <MathLiveEditor
            ref={mathEditorRef}
            value={latex}
            onChange={handleLatexChange}
            placeholder="在此输入数学公式..."
            height={120}
            virtualKeyboard={true}
            virtualKeyboardPolicy="auto"
            useCustomKeyboard={true}
            smartFence={true}
            smartMode={true}
            smartSuperscript={true}
            onError={(err) => setError('编辑器错误: ' + err.toString())}
          />
        </Box>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* LaTeX代码显示 */}
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle2">
              LaTeX代码：
            </Typography>
            <Box>
              <IconButton size="small" onClick={handleCopyLatex} title="复制LaTeX代码">
                <CopyIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" onClick={handleClear} title="清空">
                <RefreshIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
          <Box
            sx={{
              p: 1.5,
              border: '1px solid #ddd',
              borderRadius: 1,
              backgroundColor: '#f5f5f5',
              fontFamily: 'monospace',
              fontSize: '14px',
              minHeight: '40px',
              maxHeight: '100px',
              overflow: 'auto',
              wordBreak: 'break-all',
            }}
          >
            {displayLatex || '(空)'}
          </Box>
        </Box>

        {/* 预览区域 */}
        {showPreview && latex && (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              公式预览：
            </Typography>
            <Box
              sx={{
                p: 2,
                border: '1px solid #ddd',
                borderRadius: 1,
                backgroundColor: '#fff',
                textAlign: 'center',
                minHeight: '60px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MathLiveEditor
                value={latex}
                readOnly={true}
                height={50}
                virtualKeyboard={false}
                style={{ border: 'none' }}
              />
            </Box>
          </Box>
        )}

        {/* 使用说明 */}
        <Box>
          <Typography variant="caption" color="text.secondary">
            <strong>使用提示：</strong>
            <br />
            • 可以直接输入数学符号，如 x^2 会自动转换为上标
            <br />
            • 使用 / 输入分数，如 a/b 会转换为分数形式
            <br />
            • 使用 sqrt 输入根号，如 sqrt(x) 会转换为根号
            <br />
            • 点击上方模板可快速插入常用公式
            <br />
            • 支持虚拟键盘输入（移动设备）
          </Typography>
        </Box>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button onClick={handleCancel} variant="outlined">
          取消
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          disabled={!latex.trim()}
          startIcon={<FunctionsIcon />}
        >
          插入公式
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MathLiveDialog;
