package com.example.adminbackend.service;

import com.example.adminbackend.model.Video;

import java.util.List;

/**
 * 视频服务接口
 */
public interface VideoService {

    /**
     * 获取所有视频
     */
    List<Video> getAllVideos();

    /**
     * 根据ID获取视频
     */
    Video getVideoById(Long id);

    /**
     * 根据视频URL获取视频
     */
    Video getVideoByUrl(String videoUrl);

    /**
     * 根据标题获取视频
     */
    Video getVideoByTitle(String title);

    /**
     * 创建视频
     */
    Video createVideo(Video video);

    /**
     * 更新视频
     */
    Video updateVideo(Long id, Video video);

    /**
     * 删除视频
     */
    void deleteVideo(Long id);

    /**
     * 检查视频URL是否存在
     */
    boolean existsByVideoUrl(String videoUrl);

    /**
     * 检查视频标题是否存在
     */
    boolean existsByTitle(String title);

    /**
     * 根据关键词搜索视频
     */
    List<Video> searchVideos(String keyword);

    /**
     * 根据视频URL查找或创建视频
     * 用于迁移过程中处理知识点的video字段
     */
    Video findOrCreateVideoByUrl(String videoUrl);

    /**
     * 根据视频URL和标题查找或创建视频
     * 用于迁移过程中处理知识点的video字段，使用知识点名称作为视频标题
     */
    Video findOrCreateVideoByUrlAndTitle(String videoUrl, String title);
}
