package com.example.adminbackend.controller;

import com.example.adminbackend.model.PracticeConfig;
import com.example.adminbackend.service.PracticeConfigService;
import com.example.adminbackend.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.http.HttpStatus;

/**
 * 练习配置控制器
 * 权限：督学、管理员、超级管理员
 */
@RestController
@RequestMapping("/practice-configs")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasAnyRole('SUPERVISOR', 'ADMIN', 'SUPER_ADMIN')")
public class PracticeConfigController {

    private final PracticeConfigService practiceConfigService;

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建练习配置
     */
    @PostMapping
    public ResponseEntity<ApiResponse<PracticeConfig>> createConfig(
            @RequestBody PracticeConfig config,
            Principal principal) {
        try {
            log.info("创建练习配置请求: configType={}, scopeType={}, targetId={}, user={}", 
                    config.getConfigType(), config.getScopeType(), config.getTargetId(), principal.getName());

            PracticeConfig created = practiceConfigService.createConfig(config, principal.getName());
            
            log.info("练习配置创建成功: id={}", created.getId());
            return ResponseEntity.ok(ApiResponse.success(created, "配置创建成功"));

        } catch (Exception e) {
            log.error("创建练习配置失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "创建配置失败: " + e.getMessage()));
        }
    }

    /**
     * 更新练习配置
     */
    @PutMapping("/{configId}")
    public ResponseEntity<ApiResponse<PracticeConfig>> updateConfig(
            @PathVariable Long configId,
            @RequestBody PracticeConfig config,
            Principal principal) {
        try {
            log.info("更新练习配置请求: configId={}, user={}", configId, principal.getName());

            PracticeConfig updated = practiceConfigService.updateConfig(configId, config);
            
            log.info("练习配置更新成功: id={}", updated.getId());
            return ResponseEntity.ok(ApiResponse.success(updated, "配置更新成功"));

        } catch (Exception e) {
            log.error("更新练习配置失败: configId={}", configId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "更新配置失败: " + e.getMessage()));
        }
    }

    /**
     * 获取配置详情
     */
    @GetMapping("/{configId}")
    public ResponseEntity<ApiResponse<PracticeConfig>> getConfig(@PathVariable Long configId) {
        try {
            PracticeConfig config = practiceConfigService.getConfigById(configId);
            return ResponseEntity.ok(ApiResponse.success(config, "获取配置成功"));

        } catch (Exception e) {
            log.error("获取练习配置失败: configId={}", configId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取配置失败: " + e.getMessage()));
        }
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{configId}")
    public ResponseEntity<ApiResponse<Void>> deleteConfig(
            @PathVariable Long configId,
            Principal principal) {
        try {
            log.info("删除练习配置请求: configId={}, user={}", configId, principal.getName());

            practiceConfigService.deleteConfig(configId);

            log.info("练习配置删除成功: configId={}", configId);
            return ResponseEntity.ok(ApiResponse.success(null, "配置删除成功"));

        } catch (Exception e) {
            log.error("删除练习配置失败: configId={}", configId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "删除配置失败: " + e.getMessage()));
        }
    }

    /**
     * 启用/禁用配置
     */
    @PatchMapping("/{configId}/toggle")
    public ResponseEntity<ApiResponse<PracticeConfig>> toggleConfig(
            @PathVariable Long configId,
            @RequestParam boolean enabled,
            Principal principal) {
        try {
            log.info("切换练习配置状态请求: configId={}, enabled={}, user={}", configId, enabled, principal.getName());

            PracticeConfig updated = practiceConfigService.toggleConfig(configId, enabled);

            log.info("练习配置状态切换成功: configId={}, enabled={}", configId, enabled);
            return ResponseEntity.ok(ApiResponse.success(updated, "配置状态更新成功"));

        } catch (Exception e) {
            log.error("切换练习配置状态失败: configId={}, enabled={}", configId, enabled, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "更新配置状态失败: " + e.getMessage()));
        }
    }

    // ==================== 查询操作 ====================

    /**
     * 分页查询配置
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<PracticeConfig>>> getConfigs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String configType,
            @RequestParam(required = false) String scopeType) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<PracticeConfig> configs;

            if (configType != null && !configType.isEmpty()) {
                PracticeConfig.ConfigType type = PracticeConfig.ConfigType.valueOf(configType.toUpperCase());
                configs = practiceConfigService.getConfigsByType(type, pageable);
            } else if (scopeType != null && !scopeType.isEmpty()) {
                PracticeConfig.ScopeType scope = PracticeConfig.ScopeType.valueOf(scopeType.toUpperCase());
                configs = practiceConfigService.getConfigsByScope(scope, pageable);
            } else {
                configs = practiceConfigService.getConfigs(pageable);
            }

            return ResponseEntity.ok(ApiResponse.success(configs, "获取配置列表成功"));

        } catch (Exception e) {
            log.error("获取练习配置列表失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取配置列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有启用的配置
     */
    @GetMapping("/enabled")
    public ResponseEntity<ApiResponse<List<PracticeConfig>>> getAllEnabledConfigs(
            @RequestParam(required = false) String configType) {
        try {
            List<PracticeConfig> configs;

            if (configType != null && !configType.isEmpty()) {
                try {
                    PracticeConfig.ConfigType type = PracticeConfig.ConfigType.valueOf(configType.toUpperCase());
                    configs = practiceConfigService.getEnabledConfigsByType(type);
                } catch (IllegalArgumentException e) {
                    log.warn("无效的配置类型参数: {}", configType);
                    return ResponseEntity.badRequest().body(ApiResponse.error(400, "无效的配置类型: " + configType));
                }
            } else {
                configs = practiceConfigService.getAllEnabledConfigs();
            }

            // 确保返回的列表不为null
            if (configs == null) {
                configs = new ArrayList<>();
            }

            return ResponseEntity.ok(ApiResponse.success(configs, "获取启用配置成功"));

        } catch (Exception e) {
            log.error("获取启用配置失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(500, "获取启用配置失败: " + e.getMessage()));
        }
    }

    // ==================== 快捷配置操作 ====================

    /**
     * 创建或更新全局配置
     */
    @PostMapping("/global")
    public ResponseEntity<ApiResponse<PracticeConfig>> createOrUpdateGlobalConfig(
            @RequestParam String configType,
            @RequestParam Integer questionCount,
            @RequestParam(required = false, defaultValue = "RANDOM") String strategy,
            Principal principal) {
        try {
            log.info("创建或更新全局配置请求: configType={}, questionCount={}, strategy={}, user={}",
                    configType, questionCount, strategy, principal.getName());

            PracticeConfig.ConfigType type = PracticeConfig.ConfigType.valueOf(configType.toUpperCase());
            PracticeConfig.SelectionStrategy selectionStrategy = PracticeConfig.SelectionStrategy.valueOf(strategy.toUpperCase());

            PracticeConfig config = practiceConfigService.createOrUpdateGlobalConfig(
                    type, questionCount, selectionStrategy, principal.getName());

            log.info("全局配置创建或更新成功: id={}", config.getId());
            return ResponseEntity.ok(ApiResponse.success(config, "全局配置设置成功"));

        } catch (Exception e) {
            log.error("创建或更新全局配置失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "设置全局配置失败: " + e.getMessage()));
        }
    }

    /**
     * 创建或更新科目配置
     */
    @PostMapping("/subject/{subjectId}")
    public ResponseEntity<ApiResponse<PracticeConfig>> createOrUpdateSubjectConfig(
            @PathVariable Long subjectId,
            @RequestParam String configType,
            @RequestParam Integer questionCount,
            @RequestParam(required = false, defaultValue = "RANDOM") String strategy,
            Principal principal) {
        try {
            log.info("创建或更新科目配置请求: subjectId={}, configType={}, questionCount={}, user={}",
                    subjectId, configType, questionCount, principal.getName());

            PracticeConfig.ConfigType type = PracticeConfig.ConfigType.valueOf(configType.toUpperCase());
            PracticeConfig.SelectionStrategy selectionStrategy = PracticeConfig.SelectionStrategy.valueOf(strategy.toUpperCase());

            PracticeConfig config = practiceConfigService.createOrUpdateSubjectConfig(
                    subjectId, type, questionCount, selectionStrategy, principal.getName());

            log.info("科目配置创建或更新成功: id={}", config.getId());
            return ResponseEntity.ok(ApiResponse.success(config, "科目配置设置成功"));

        } catch (Exception e) {
            log.error("创建或更新科目配置失败: subjectId={}", subjectId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "设置科目配置失败: " + e.getMessage()));
        }
    }

    /**
     * 创建或更新章节配置
     */
    @PostMapping("/chapter/{chapterId}")
    public ResponseEntity<ApiResponse<PracticeConfig>> createOrUpdateChapterConfig(
            @PathVariable Long chapterId,
            @RequestParam String configType,
            @RequestParam Integer questionCount,
            @RequestParam(required = false, defaultValue = "RANDOM") String strategy,
            Principal principal) {
        try {
            log.info("创建或更新章节配置请求: chapterId={}, configType={}, questionCount={}, user={}",
                    chapterId, configType, questionCount, principal.getName());

            PracticeConfig.ConfigType type = PracticeConfig.ConfigType.valueOf(configType.toUpperCase());
            PracticeConfig.SelectionStrategy selectionStrategy = PracticeConfig.SelectionStrategy.valueOf(strategy.toUpperCase());

            PracticeConfig config = practiceConfigService.createOrUpdateChapterConfig(
                    chapterId, type, questionCount, selectionStrategy, principal.getName());

            log.info("章节配置创建或更新成功: id={}", config.getId());
            return ResponseEntity.ok(ApiResponse.success(config, "章节配置设置成功"));

        } catch (Exception e) {
            log.error("创建或更新章节配置失败: chapterId={}", chapterId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "设置章节配置失败: " + e.getMessage()));
        }
    }

    /**
     * 创建或更新知识点配置
     */
    @PostMapping("/knowledge-point/{knowledgePointId}")
    public ResponseEntity<ApiResponse<PracticeConfig>> createOrUpdateKnowledgePointConfig(
            @PathVariable Long knowledgePointId,
            @RequestParam String configType,
            @RequestParam Integer questionCount,
            @RequestParam(required = false, defaultValue = "RANDOM") String strategy,
            Principal principal) {
        try {
            log.info("创建或更新知识点配置请求: knowledgePointId={}, configType={}, questionCount={}, user={}",
                    knowledgePointId, configType, questionCount, principal.getName());

            PracticeConfig.ConfigType type = PracticeConfig.ConfigType.valueOf(configType.toUpperCase());
            PracticeConfig.SelectionStrategy selectionStrategy = PracticeConfig.SelectionStrategy.valueOf(strategy.toUpperCase());

            PracticeConfig config = practiceConfigService.createOrUpdateKnowledgePointConfig(
                    knowledgePointId, type, questionCount, selectionStrategy, principal.getName());

            log.info("知识点配置创建或更新成功: id={}", config.getId());
            return ResponseEntity.ok(ApiResponse.success(config, "知识点配置设置成功"));

        } catch (Exception e) {
            log.error("创建或更新知识点配置失败: knowledgePointId={}", knowledgePointId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "设置知识点配置失败: " + e.getMessage()));
        }
    }

    // ==================== 统计和分析 ====================

    /**
     * 获取配置统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getConfigStatistics() {
        try {
            Map<String, Object> statistics = practiceConfigService.getConfigStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics, "获取统计信息成功"));

        } catch (Exception e) {
            log.error("获取配置统计信息失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 根据目标ID获取配置
     */
    @GetMapping("/target/{targetId}")
    public ResponseEntity<ApiResponse<List<PracticeConfig>>> getConfigsByTargetId(@PathVariable Long targetId) {
        try {
            List<PracticeConfig> configs = practiceConfigService.getConfigsByTargetId(targetId);
            return ResponseEntity.ok(ApiResponse.success(configs, "获取目标配置成功"));

        } catch (Exception e) {
            log.error("根据目标ID获取配置失败: targetId={}", targetId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取目标配置失败: " + e.getMessage()));
        }
    }

    // ==================== 批量操作 ====================

    /**
     * 批量创建配置
     */
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<List<PracticeConfig>>> createConfigs(
            @RequestBody List<PracticeConfig> configs,
            Principal principal) {
        try {
            log.info("批量创建练习配置请求: count={}, user={}", configs.size(), principal.getName());

            List<PracticeConfig> created = practiceConfigService.createConfigs(configs, principal.getName());

            log.info("批量创建练习配置完成: 成功={}, 总数={}", created.size(), configs.size());
            return ResponseEntity.ok(ApiResponse.success(created, "批量创建配置完成"));

        } catch (Exception e) {
            log.error("批量创建练习配置失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "批量创建配置失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除配置
     */
    @DeleteMapping("/batch")
    public ResponseEntity<ApiResponse<Void>> deleteConfigs(
            @RequestBody List<Long> configIds,
            Principal principal) {
        try {
            log.info("批量删除练习配置请求: count={}, user={}", configIds.size(), principal.getName());

            practiceConfigService.deleteConfigs(configIds);

            log.info("批量删除练习配置完成: count={}", configIds.size());
            return ResponseEntity.ok(ApiResponse.success(null, "批量删除配置成功"));

        } catch (Exception e) {
            log.error("批量删除练习配置失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "批量删除配置失败: " + e.getMessage()));
        }
    }

    /**
     * 批量启用/禁用配置
     */
    @PatchMapping("/batch/toggle")
    public ResponseEntity<ApiResponse<Void>> toggleConfigs(
            @RequestBody List<Long> configIds,
            @RequestParam boolean enabled,
            Principal principal) {
        try {
            log.info("批量切换练习配置状态请求: count={}, enabled={}, user={}", configIds.size(), enabled, principal.getName());

            practiceConfigService.toggleConfigs(configIds, enabled);

            log.info("批量切换练习配置状态完成: count={}, enabled={}", configIds.size(), enabled);
            return ResponseEntity.ok(ApiResponse.success(null, "批量更新配置状态成功"));

        } catch (Exception e) {
            log.error("批量切换练习配置状态失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "批量更新配置状态失败: " + e.getMessage()));
        }
    }
}
