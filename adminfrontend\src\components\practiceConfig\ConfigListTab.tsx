import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  TextField,
  TablePagination,
  Checkbox,
  Toolbar,
  Tooltip,
} from '@mui/material';
import {
  Edit,
  Delete,
  Refresh,
  FilterList,
  ToggleOn,
  ToggleOff,
  DeleteSweep,
  Settings,
} from '@mui/icons-material';
import { PracticeConfigAPI, PracticeConfig, PageResponse } from '../../services/practiceConfigApi';

interface ConfigListTabProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const ConfigListTab: React.FC<ConfigListTabProps> = ({ onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [configs, setConfigs] = useState<PracticeConfig[]>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedConfigs, setSelectedConfigs] = useState<number[]>([]);
  const [filters, setFilters] = useState({
    configType: '',
    scopeType: '',
    search: '',
  });

  // 配置类型选项
  const configTypeOptions = [
    { value: '', label: '全部类型' },
    { value: 'KNOWLEDGE_POINT_PRACTICE', label: '知识点练习' },
    { value: 'CHAPTER_TEST', label: '章节测试' },
  ];

  // 适用范围选项
  const scopeTypeOptions = [
    { value: '', label: '全部范围' },
    { value: 'GLOBAL', label: '全局配置' },
    { value: 'SUBJECT', label: '科目配置' },
    { value: 'CHAPTER', label: '章节配置' },
    { value: 'KNOWLEDGE_POINT', label: '知识点配置' },
  ];

  // 策略选项
  const strategyOptions = [
    { value: 'RANDOM', label: '随机选择' },
    { value: 'DIFFICULTY_BALANCED', label: '难度均衡' },
    { value: 'ERROR_PRIORITY', label: '错题优先' },
    { value: 'TYPE_BALANCED', label: '题型均衡' },
  ];

  // 加载配置列表
  const loadConfigs = async () => {
    try {
      setLoading(true);
      const params = {
        page,
        size: rowsPerPage,
        ...(filters.configType && { configType: filters.configType }),
        ...(filters.scopeType && { scopeType: filters.scopeType }),
      };

      const response: PageResponse<PracticeConfig> = await PracticeConfigAPI.getConfigs(params);
      
      // 如果有搜索条件，在前端进行过滤
      let filteredConfigs = response.content;
      if (filters.search) {
        filteredConfigs = response.content.filter(config =>
          config.description?.toLowerCase().includes(filters.search.toLowerCase()) ||
          config.createdBy?.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      setConfigs(filteredConfigs);
      setTotalElements(response.totalElements);
    } catch (error: any) {
      console.error('加载配置列表失败:', error);
      onError('加载配置列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取配置类型标签
  const getConfigTypeLabel = (configType: string) => {
    const option = configTypeOptions.find(opt => opt.value === configType);
    return option?.label || configType;
  };

  // 获取适用范围标签
  const getScopeTypeLabel = (scopeType: string) => {
    const option = scopeTypeOptions.find(opt => opt.value === scopeType);
    return option?.label || scopeType;
  };

  // 获取策略标签
  const getStrategyLabel = (strategy: string) => {
    const option = strategyOptions.find(opt => opt.value === strategy);
    return option?.label || strategy;
  };

  // 获取目标显示名称
  const getTargetDisplayName = (config: PracticeConfig) => {
    if (config.scopeType === 'GLOBAL') {
      return '全局';
    }
    // 这里应该根据targetId查询具体的名称，暂时显示ID
    return `ID: ${config.targetId}`;
  };

  // 切换配置状态
  const handleToggleConfig = async (config: PracticeConfig) => {
    try {
      await PracticeConfigAPI.toggleConfig(config.id!, !config.enabled);
      onSuccess(`配置已${config.enabled ? '禁用' : '启用'}`);
      await loadConfigs();
    } catch (error: any) {
      console.error('切换配置状态失败:', error);
      onError('切换配置状态失败: ' + (error.message || '未知错误'));
    }
  };

  // 删除单个配置
  const handleDeleteConfig = async (config: PracticeConfig) => {
    if (!window.confirm('确定要删除这个配置吗？')) {
      return;
    }

    try {
      await PracticeConfigAPI.deleteConfig(config.id!);
      onSuccess('配置删除成功');
      await loadConfigs();
    } catch (error: any) {
      console.error('删除配置失败:', error);
      onError('删除配置失败: ' + (error.message || '未知错误'));
    }
  };

  // 批量删除配置
  const handleBatchDelete = async () => {
    if (selectedConfigs.length === 0) {
      onError('请选择要删除的配置');
      return;
    }

    if (!window.confirm(`确定要删除选中的 ${selectedConfigs.length} 个配置吗？`)) {
      return;
    }

    try {
      await PracticeConfigAPI.deleteConfigs(selectedConfigs);
      onSuccess(`成功删除 ${selectedConfigs.length} 个配置`);
      setSelectedConfigs([]);
      await loadConfigs();
    } catch (error: any) {
      console.error('批量删除配置失败:', error);
      onError('批量删除配置失败: ' + (error.message || '未知错误'));
    }
  };

  // 批量启用/禁用配置
  const handleBatchToggle = async (enabled: boolean) => {
    if (selectedConfigs.length === 0) {
      onError('请选择要操作的配置');
      return;
    }

    try {
      await PracticeConfigAPI.toggleConfigs(selectedConfigs, enabled);
      onSuccess(`成功${enabled ? '启用' : '禁用'} ${selectedConfigs.length} 个配置`);
      setSelectedConfigs([]);
      await loadConfigs();
    } catch (error: any) {
      console.error('批量操作配置失败:', error);
      onError('批量操作配置失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理选择
  const handleSelectConfig = (configId: number) => {
    setSelectedConfigs(prev => {
      if (prev.includes(configId)) {
        return prev.filter(id => id !== configId);
      } else {
        return [...prev, configId];
      }
    });
  };

  // 处理全选
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedConfigs(configs.map(config => config.id!));
    } else {
      setSelectedConfigs([]);
    }
  };

  // 处理分页
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理过滤器变化
  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
    }));
    setPage(0);
  };

  // 重置过滤器
  const handleResetFilters = () => {
    setFilters({
      configType: '',
      scopeType: '',
      search: '',
    });
    setPage(0);
  };

  useEffect(() => {
    loadConfigs();
  }, [page, rowsPerPage, filters.configType, filters.scopeType]);

  // 搜索防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      if (filters.search !== undefined) {
        loadConfigs();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [filters.search]);

  return (
    <Box>
      {/* 页面说明 */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          这里显示所有的练习配置，支持筛选、搜索、批量操作等功能。
          可以查看配置的详细信息，并进行启用/禁用、编辑、删除等操作。
        </Typography>
      </Alert>

      {/* 过滤器 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <FilterList sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="bold">
              筛选条件
            </Typography>
          </Box>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>配置类型</InputLabel>
                <Select
                  value={filters.configType}
                  label="配置类型"
                  onChange={(e) => handleFilterChange('configType', e.target.value)}
                >
                  {configTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>适用范围</InputLabel>
                <Select
                  value={filters.scopeType}
                  label="适用范围"
                  onChange={(e) => handleFilterChange('scopeType', e.target.value)}
                >
                  {scopeTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <TextField
                fullWidth
                size="small"
                label="搜索"
                placeholder="搜索描述或创建者"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <Button
                fullWidth
                variant="outlined"
                onClick={handleResetFilters}
                sx={{ height: '40px' }}
              >
                重置
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 操作栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Settings sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" fontWeight="bold">
            配置列表 ({totalElements})
          </Typography>
        </Box>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={loadConfigs}
            disabled={loading}
            sx={{ mr: 1 }}
          >
            刷新
          </Button>
        </Box>
      </Box>

      {/* 批量操作工具栏 */}
      {selectedConfigs.length > 0 && (
        <Toolbar
          sx={{
            pl: { sm: 2 },
            pr: { xs: 1, sm: 1 },
            bgcolor: 'primary.light',
            color: 'primary.contrastText',
            borderRadius: 1,
            mb: 2,
          }}
        >
          <Typography sx={{ flex: '1 1 100%' }} variant="subtitle1">
            已选择 {selectedConfigs.length} 项
          </Typography>
          <Tooltip title="批量启用">
            <IconButton color="inherit" onClick={() => handleBatchToggle(true)}>
              <ToggleOn />
            </IconButton>
          </Tooltip>
          <Tooltip title="批量禁用">
            <IconButton color="inherit" onClick={() => handleBatchToggle(false)}>
              <ToggleOff />
            </IconButton>
          </Tooltip>
          <Tooltip title="批量删除">
            <IconButton color="inherit" onClick={handleBatchDelete}>
              <DeleteSweep />
            </IconButton>
          </Tooltip>
        </Toolbar>
      )}

      {/* 配置列表 */}
      <Card>
        <CardContent sx={{ p: 0 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">
                        <Checkbox
                          indeterminate={selectedConfigs.length > 0 && selectedConfigs.length < configs.length}
                          checked={configs.length > 0 && selectedConfigs.length === configs.length}
                          onChange={handleSelectAll}
                        />
                      </TableCell>
                      <TableCell>配置类型</TableCell>
                      <TableCell>适用范围</TableCell>
                      <TableCell>目标</TableCell>
                      <TableCell>题目数量</TableCell>
                      <TableCell>抽题策略</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>创建者</TableCell>
                      <TableCell>创建时间</TableCell>
                      <TableCell align="center">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {configs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={10} align="center">
                          <Typography color="text.secondary">
                            暂无配置数据
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      configs.map((config) => (
                        <TableRow key={config.id} hover>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedConfigs.includes(config.id!)}
                              onChange={() => handleSelectConfig(config.id!)}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getConfigTypeLabel(config.configType)}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getScopeTypeLabel(config.scopeType)}
                              color="secondary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {getTargetDisplayName(config)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography fontWeight="medium">
                              {config.questionCount}道
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {getStrategyLabel(config.selectionStrategy)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={config.enabled ? '已启用' : '已禁用'}
                              color={config.enabled ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {config.createdBy || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {config.createdAt ? new Date(config.createdAt).toLocaleDateString() : '-'}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Tooltip title={config.enabled ? '禁用' : '启用'}>
                              <IconButton
                                size="small"
                                onClick={() => handleToggleConfig(config)}
                                color={config.enabled ? 'warning' : 'success'}
                              >
                                {config.enabled ? <ToggleOff /> : <ToggleOn />}
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="删除">
                              <IconButton
                                size="small"
                                onClick={() => handleDeleteConfig(config)}
                                color="error"
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              
              {/* 分页 */}
              <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={totalElements}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="每页行数:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
              />
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default ConfigListTab;
