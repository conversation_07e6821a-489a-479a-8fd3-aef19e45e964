import { UserRole } from '../types';

/**
 * 获取角色的中文标签
 * @param role 用户角色
 * @returns 角色的中文标签
 */
export const getRoleLabel = (role: UserRole): string => {
  const roleLabels: Record<UserRole, string> = {
    SUPER_ADMIN: '超级管理员',
    ADMIN: '管理员',
    DEALER: '经销商',
    AGENT: '代理商',
    SUPERVISOR: '督学',
  };
  
  return roleLabels[role] || '未知角色';
};

/**
 * 获取角色对应的颜色主题
 * @param role 用户角色
 * @returns Material-UI 颜色主题
 */
export const getRoleColor = (role: UserRole): 'primary' | 'success' | 'warning' | 'info' | 'error' => {
  const roleColors: Record<UserRole, 'primary' | 'success' | 'warning' | 'info' | 'error'> = {
    SUPER_ADMIN: 'primary',    // 蓝色 - 最高权限
    ADMIN: 'success',          // 绿色 - 管理员
    DEALER: 'info',            // 青色 - 经销商
    AGENT: 'warning',          // 橙色 - 代理商
    SUPERVISOR: 'error',       // 红色 - 督学
  };
  
  return roleColors[role] || 'primary';
};

/**
 * 获取角色对应的头像背景色
 * @param role 用户角色
 * @returns Material-UI 主题色名称
 */
export const getRoleAvatarColor = (role: UserRole): string => {
  const avatarColors: Record<UserRole, string> = {
    SUPER_ADMIN: 'primary.main',
    ADMIN: 'success.main',
    DEALER: 'info.main',
    AGENT: 'warning.main',
    SUPERVISOR: 'error.main',
  };
  
  return avatarColors[role] || 'primary.main';
};

/**
 * 根据当前用户角色获取可管理的角色列表
 * @param currentUserRole 当前用户角色
 * @returns 可管理的角色列表
 */
export const getManageableRoles = (currentUserRole: UserRole): UserRole[] => {
  switch (currentUserRole) {
    case 'SUPER_ADMIN':
      // 超级管理员可以管理所有角色
      return ['SUPER_ADMIN', 'ADMIN', 'DEALER', 'AGENT', 'SUPERVISOR'];
    case 'ADMIN':
      // 管理员只能管理督学
      return ['SUPERVISOR'];
    default:
      // 其他角色没有管理权限
      return [];
  }
};

/**
 * 检查当前用户是否有权限管理目标角色
 * @param currentUserRole 当前用户角色
 * @param targetRole 目标角色
 * @returns 是否有权限
 */
export const canManageRole = (currentUserRole: UserRole, targetRole: UserRole): boolean => {
  const manageableRoles = getManageableRoles(currentUserRole);
  return manageableRoles.includes(targetRole);
};
