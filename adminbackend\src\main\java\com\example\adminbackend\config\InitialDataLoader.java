package com.example.adminbackend.config;

import com.example.adminbackend.repository.UserRepository;
import com.example.adminbackend.model.Role;
import com.example.adminbackend.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class InitialDataLoader implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // 检查是否存在超级管理员
        if (!userRepository.existsByUsername("superadmin")) {
            // 创建超级管理员账户
            User superAdmin = User.builder()
                    .username("superadmin")
                    .password(passwordEncoder.encode("admin123"))
                    .fullName("超级管理员")
                    .role(Role.SUPER_ADMIN)
                    .accountType("系统账户")
                    .enabled(true)
                    .validFrom(new Date())
                    .creator("system")
                    .build();
            userRepository.save(superAdmin);
            System.out.println("超级管理员账户已创建");
        }

        // 检查是否存在管理员
        if (!userRepository.existsByUsername("admin")) {
            // 创建管理员账户
            User admin = User.builder()
                    .username("admin")
                    .password(passwordEncoder.encode("admin123"))
                    .fullName("管理员")
                    .role(Role.ADMIN)
                    .accountType("系统账户")
                    .enabled(true)
                    .validFrom(new Date())
                    .creator("system")
                    .build();
            userRepository.save(admin);
            System.out.println("管理员账户已创建");
        }
    }
} 