package com.example.adminbackend.repository;

import com.example.adminbackend.model.StudentAnswer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 学生答题记录数据访问层
 */
@Repository
public interface StudentAnswerRepository extends JpaRepository<StudentAnswer, Long> {

    /**
     * 根据题目ID查找所有学生答题记录
     */
    List<StudentAnswer> findByQuestionId(Long questionId);

    /**
     * 根据学生ID查找所有答题记录
     */
    List<StudentAnswer> findByStudentId(Long studentId);

    /**
     * 根据知识点ID查找所有答题记录
     */
    List<StudentAnswer> findByKnowledgePointId(Long knowledgePointId);

    /**
     * 根据题目ID删除所有相关的学生答题记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM StudentAnswer sa WHERE sa.question.id = :questionId")
    void deleteByQuestionId(@Param("questionId") Long questionId);

    /**
     * 根据知识点ID删除所有相关的学生答题记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM StudentAnswer sa WHERE sa.knowledgePoint.id = :knowledgePointId")
    void deleteByKnowledgePointId(@Param("knowledgePointId") Long knowledgePointId);

    /**
     * 统计知识点的答题记录数量
     */
    @Query("SELECT COUNT(sa) FROM StudentAnswer sa WHERE sa.knowledgePoint.id = :knowledgePointId")
    long countByKnowledgePointId(@Param("knowledgePointId") Long knowledgePointId);

    /**
     * 根据题目ID列表批量删除学生答题记录
     */
    @Modifying
    @Query("DELETE FROM StudentAnswer sa WHERE sa.question.id IN :questionIds")
    void deleteByQuestionIds(@Param("questionIds") List<Long> questionIds);

    /**
     * 检查题目是否有相关的学生答题记录
     */
    boolean existsByQuestionId(Long questionId);

    /**
     * 统计题目的答题记录数量
     */
    @Query("SELECT COUNT(sa) FROM StudentAnswer sa WHERE sa.question.id = :questionId")
    long countByQuestionId(@Param("questionId") Long questionId);

    /**
     * 根据学生ID和题目ID查找答题记录
     */
    List<StudentAnswer> findByStudentIdAndQuestionId(Long studentId, Long questionId);

    /**
     * 获取学生的所有错题ID
     */
    @Query("SELECT sa.question.id FROM StudentAnswer sa WHERE sa.student.id = :studentId AND sa.isCorrect = false")
    List<Long> findWrongQuestionIdsByStudent(@Param("studentId") Long studentId);

    /**
     * 获取学生在特定知识点的错题ID
     */
    @Query("SELECT sa.question.id FROM StudentAnswer sa WHERE sa.student.id = :studentId AND sa.knowledgePoint.id = :knowledgePointId AND sa.isCorrect = false")
    List<Long> findWrongQuestionIdsByStudentAndKnowledgePoint(@Param("studentId") Long studentId, @Param("knowledgePointId") Long knowledgePointId);

    /**
     * 获取学生在特定章节的错题ID
     */
    @Query("SELECT sa.question.id FROM StudentAnswer sa WHERE sa.student.id = :studentId AND sa.knowledgePoint.chapter.id = :chapterId AND sa.isCorrect = false")
    List<Long> findWrongQuestionIdsByStudentAndChapter(@Param("studentId") Long studentId, @Param("chapterId") Long chapterId);

    /**
     * 统计学生的错题数量
     */
    @Query("SELECT COUNT(sa) FROM StudentAnswer sa WHERE sa.student.id = :studentId AND sa.isCorrect = false")
    long countWrongAnswersByStudent(@Param("studentId") Long studentId);

    /**
     * 统计学生在特定知识点的错题数量
     */
    @Query("SELECT COUNT(sa) FROM StudentAnswer sa WHERE sa.student.id = :studentId AND sa.knowledgePoint.id = :knowledgePointId AND sa.isCorrect = false")
    long countWrongAnswersByStudentAndKnowledgePoint(@Param("studentId") Long studentId, @Param("knowledgePointId") Long knowledgePointId);

    /**
     * 获取学生的答题统计信息
     */
    @Query("SELECT sa.isCorrect, COUNT(sa) FROM StudentAnswer sa WHERE sa.student.id = :studentId GROUP BY sa.isCorrect")
    List<Object[]> getAnswerStatisticsByStudent(@Param("studentId") Long studentId);

    /**
     * 获取学生在特定知识点的答题统计信息
     */
    @Query("SELECT sa.isCorrect, COUNT(sa) FROM StudentAnswer sa WHERE sa.student.id = :studentId AND sa.knowledgePoint.id = :knowledgePointId GROUP BY sa.isCorrect")
    List<Object[]> getAnswerStatisticsByStudentAndKnowledgePoint(@Param("studentId") Long studentId, @Param("knowledgePointId") Long knowledgePointId);
}
