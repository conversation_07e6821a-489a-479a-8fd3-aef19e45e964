import React, { useEffect, useRef } from 'react';
import UnifiedRenderer from './UnifiedRenderer';

interface BatchRendererProps {
  /** 要渲染的内容列表 */
  items: Array<{
    id: string | number;
    content: string;
    maxLength?: number;
    inline?: boolean;
    className?: string;
    style?: React.CSSProperties;
  }>;
  /** 渲染延迟（毫秒），用于优化性能 */
  delay?: number;
  /** 容器类名 */
  className?: string;
  /** 容器样式 */
  style?: React.CSSProperties;
}

/**
 * 批量渲染器 - 优化大量公式的渲染性能
 * 
 * 功能特点：
 * - 延迟批量渲染，避免阻塞UI
 * - 自动管理MathJax渲染队列
 * - 适用于题目列表、表格等大量内容场景
 * 
 * 使用场景：
 * - 题目列表页面
 * - 数据表格
 * - 搜索结果列表
 */
const BatchRenderer: React.FC<BatchRendererProps> = ({
  items,
  delay = 100,
  className = '',
  style = {}
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 延迟批量渲染，确保DOM更新完成
    timeoutRef.current = setTimeout(() => {
      const element = containerRef.current;

      // 使用全局Promise链进行批量渲染 - 官方文档推荐
      const globalSafeTypeset = (window as any).safeTypeset;
      if (globalSafeTypeset && element) {
        globalSafeTypeset(() => {
          // 验证DOM元素的有效性
          if (!element.isConnected || !document.contains(element)) {
            return [];
          }
          return [element];
        }).catch((error: any) => {
          console.warn('批量渲染MathJax失败:', error);
        });
      }
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [items, delay]);

  // 开发环境性能监控
  if (process.env.NODE_ENV === 'development') {
    console.log('BatchRenderer 渲染:', {
      项目数量: items.length,
      延迟时间: delay,
      容器ID: containerRef.current?.id
    });
  }

  return (
    <div
      ref={containerRef}
      className={`batch-renderer ${className}`}
      style={style}
    >
      {items.map((item) => (
        <UnifiedRenderer
          key={item.id}
          content={item.content}
          maxLength={item.maxLength}
          inline={item.inline}
          className={item.className}
          style={item.style}
        />
      ))}
    </div>
  );
};

export default BatchRenderer;
