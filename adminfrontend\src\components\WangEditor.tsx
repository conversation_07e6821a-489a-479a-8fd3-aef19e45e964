import '@wangeditor/editor/dist/css/style.css'
import React, { useState, useEffect, useRef } from 'react'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { Box, Button } from '@mui/material'
import { Functions as FunctionsIcon, AudioFile as AudioFileIcon } from '@mui/icons-material'
import MathLiveDialog from './MathLiveDialog'
import MathQuillKeyboard, { useMathQuillKeyboard } from './keyborad/MathQuillKeyboard'
import { MathJax } from 'better-react-mathjax'

// MathJax类型声明
declare global {
  interface Window {
    MathJax: {
      typesetPromise: (elements?: HTMLElement[]) => Promise<void>;
      startup?: {
        promise?: Promise<void>;
      };
    };
  }
}

// 清理hook
const useCleanup = (mathRenderTimeoutRef: React.MutableRefObject<NodeJS.Timeout | null>) => {
  useEffect(() => {
    return () => {
      if (mathRenderTimeoutRef.current) {
        clearTimeout(mathRenderTimeoutRef.current)
      }
    }
  }, [mathRenderTimeoutRef])
}

// 待上传文件接口
export interface PendingFile {
  id: string
  file: File
  tempUrl: string
  type: 'image' | 'audio'
}

interface WangEditorProps {
  value?: string
  onChange?: (html: string) => void
  onFilesChange?: (files: PendingFile[]) => void
  placeholder?: string
  height?: number | string
  disabled?: boolean
  showToolbar?: boolean
  mode?: 'default' | 'simple'
}

const WangEditor: React.FC<WangEditorProps> = ({
  value = '',
  onChange,
  onFilesChange,
  placeholder = '请输入内容...',
  height = 400,
  disabled = false,
  showToolbar = true,
  mode = 'default'
}) => {
  const [editor, setEditor] = useState<IDomEditor | null>(null)
  const [html, setHtml] = useState(value)
  const [mathDialogOpen, setMathDialogOpen] = useState(false)
  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([])
  const audioInputRef = useRef<HTMLInputElement>(null)
  const editorContainerRef = useRef<HTMLDivElement>(null)
  const mathRenderTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 组件挂载状态跟踪
  const isMountedRef = useRef(true)

  // 事件监听器引用管理
  const eventListenersRef = useRef<{
    selectionChangeHandler?: () => void
    editorEventHandlers?: Array<{
      element: HTMLElement
      event: string
      handler: EventListener
    }>
  }>({})

  // 自定义键盘状态管理
  const {
    keyboardVisible,
    showKeyboard,
    hideKeyboard,
    toggleKeyboard
  } = useMathQuillKeyboard()

  // 使用清理hook
  useCleanup(mathRenderTimeoutRef)

  // 生成唯一ID
  const generateFileId = () => `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

  // 基于官方文档的安全MathJax渲染函数
  const renderMathFormulas = () => {
    // 检查组件是否仍然挂载
    if (!isMountedRef.current) return Promise.resolve()

    // 检查必要的引用和全局对象
    if (!editorContainerRef.current || !window.MathJax) return Promise.resolve()

    // 基于官方文档的typeset函数模式
    return new Promise<void>((resolve) => {
      // 再次检查组件状态
      if (!isMountedRef.current) {
        resolve()
        return
      }

      // 查找编辑器内容区域
      const editorContent = editorContainerRef.current?.querySelector('.w-e-text-container') as HTMLElement
      if (!editorContent || !isMountedRef.current) {
        resolve()
        return
      }

      // 检查元素是否仍在DOM中
      if (!editorContent.isConnected || !document.contains(editorContent)) {
        resolve()
        return
      }

      // 使用MathJax的typesetPromise，它现在已经有了全局错误处理
      window.MathJax.typesetPromise([editorContent])
        .then(() => {
          if (isMountedRef.current) {
            resolve()
          }
        })
        .catch((error) => {
          // 错误已经在全局处理器中被捕获，这里只需要resolve
          if (isMountedRef.current) {
            console.warn('WangEditor MathJax渲染失败:', error)
          }
          resolve()
        })
    })
  }

  // 防抖的数学公式渲染 - 改进清理逻辑
  const debouncedRenderMath = () => {
    // 检查组件是否仍然挂载
    if (!isMountedRef.current) return

    if (mathRenderTimeoutRef.current) {
      clearTimeout(mathRenderTimeoutRef.current)
    }
    mathRenderTimeoutRef.current = setTimeout(() => {
      // 在执行前再次检查组件状态
      if (isMountedRef.current) {
        renderMathFormulas()
      }
    }, 500) // 500ms延迟，避免频繁渲染
  }

  // 处理自定义键盘按键输入
  const handleCustomKeyPress = (keyData: any) => {
    if (!editor) return

    try {
      // 获取当前选区
      const selection = editor.selection

      switch (keyData.type) {
        case 'number':
        case 'variable':
        case 'letter':
          // 直接插入文本
          editor.insertText(keyData.insert || keyData.label)
          break

        case 'operator':
          // 插入运算符
          if (keyData.insert === '\\times') {
            editor.insertText('×')
          } else if (keyData.insert === '\\div') {
            editor.insertText('÷')
          } else {
            editor.insertText(keyData.insert || keyData.label)
          }
          break

        case 'comparison':
          // 插入比较符号
          const comparisonMap: { [key: string]: string } = {
            '\\geq': '≥',
            '\\leq': '≤',
            '\\neq': '≠',
            '\\approx': '≈'
          }
          const comparisonSymbol = comparisonMap[keyData.insert] || keyData.insert || keyData.label
          editor.insertText(comparisonSymbol)
          break

        case 'special':
        case 'greek':
          // 插入特殊符号和希腊字母
          const specialMap: { [key: string]: string } = {
            '\\alpha': 'α',
            '\\beta': 'β',
            '\\gamma': 'γ',
            '\\delta': 'δ',
            '\\pi': 'π',
            '\\theta': 'θ',
            '\\lambda': 'λ',
            '\\mu': 'μ',
            '\\sigma': 'σ',
            '\\phi': 'φ',
            '\\omega': 'ω'
          }
          const specialSymbol = specialMap[keyData.insert] || keyData.insert || keyData.label
          editor.insertText(specialSymbol)
          break

        case 'power':
          // 处理幂次，插入上标
          if (keyData.insert === '^2') {
            editor.insertText('²')
          } else if (keyData.insert === '^3') {
            editor.insertText('³')
          } else {
            editor.insertText(keyData.insert || keyData.label)
          }
          break

        case 'function':
          // 插入函数名
          const functionText = keyData.insert || keyData.label
          if (functionText.startsWith('\\')) {
            // 去掉LaTeX命令的反斜杠
            editor.insertText(functionText.substring(1))
          } else {
            editor.insertText(functionText)
          }
          break

        case 'bracket':
          // 插入括号
          editor.insertText(keyData.latex || keyData.insert || keyData.label)
          break

        case 'navigation':
          // 导航命令处理
          const commandName = Array.isArray(keyData.command) ? keyData.command[0] : keyData.command
          switch (commandName) {
            case 'deleteBackward':
              // 删除前一个字符
              editor.deleteBackward('character')
              break
            default:
              // 其他导航命令暂不处理
              break
          }
          break

        case 'mode':
        case 'shift':
          // 模式切换和Shift不需要插入内容
          break

        default:
          // 默认插入文本
          const content = keyData.insert || keyData.latex || keyData.label
          if (content) {
            editor.insertText(content)
          }
          break
      }
    } catch (error) {
      console.error('处理自定义键盘输入时出错:', error)
    }
  }

  // 添加待上传文件
  const addPendingFile = (file: File, tempUrl: string, type: 'image' | 'audio'): string => {
    const fileId = generateFileId()
    const pendingFile: PendingFile = {
      id: fileId,
      file,
      tempUrl,
      type
    }

    setPendingFiles((prev: PendingFile[]) => {
      const newFiles = [...prev, pendingFile]
      onFilesChange?.(newFiles)
      return newFiles
    })

    return fileId
  }

  // 移除待上传文件
  const removePendingFile = (fileId: string) => {
    setPendingFiles((prev: PendingFile[]) => {
      const newFiles = prev.filter((f: PendingFile) => f.id !== fileId)
      onFilesChange?.(newFiles)
      return newFiles
    })
  }

  // 清理所有临时URL
  const cleanupTempUrls = () => {
    pendingFiles.forEach((file: PendingFile) => {
      URL.revokeObjectURL(file.tempUrl)
    })
  }

  // 清理所有事件监听器
  const cleanupEventListeners = () => {
    // 清理 document 级别的事件监听器
    if (eventListenersRef.current.selectionChangeHandler) {
      document.removeEventListener('selectionchange', eventListenersRef.current.selectionChangeHandler)
      eventListenersRef.current.selectionChangeHandler = undefined
    }

    // 清理编辑器相关的事件监听器
    if (eventListenersRef.current.editorEventHandlers) {
      eventListenersRef.current.editorEventHandlers.forEach(({ element, event, handler }) => {
        try {
          element.removeEventListener(event, handler)
        } catch (error) {
          console.warn('清理事件监听器失败:', error)
        }
      })
      eventListenersRef.current.editorEventHandlers = []
    }
  }

  // 组件挂载时设置状态
  useEffect(() => {
    isMountedRef.current = true
    return () => {
      // 组件卸载时标记状态并清理所有资源
      isMountedRef.current = false

      // 清理定时器
      if (mathRenderTimeoutRef.current) {
        clearTimeout(mathRenderTimeoutRef.current)
        mathRenderTimeoutRef.current = null
      }

      // 清理事件监听器
      cleanupEventListeners()

      // 清理临时URL
      cleanupTempUrls()
    }
  }, [])

  // 当pendingFiles变化时通知父组件
  useEffect(() => {
    onFilesChange?.(pendingFiles)
  }, [pendingFiles, onFilesChange])

  // 工具栏配置 - 移除原来的公式插件
  const toolbarConfig: Partial<IToolbarConfig> = {
    excludeKeys: mode === 'simple' ? [
      'group-video', 'fullScreen'
    ] : []
  }

  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder,
    readOnly: disabled,
    MENU_CONF: {},
    // 允许粘贴HTML
    customPaste: (_editor: IDomEditor, _event: ClipboardEvent) => {
      // 检查组件是否仍然挂载
      if (!disabled && isMountedRef.current) {
        setTimeout(() => {
          if (isMountedRef.current) {
            showKeyboard(_editor, handleCustomKeyPress)
          }
        }, 100)
      }
      // 返回false表示使用默认粘贴行为
      return false;
    },
    // 编辑器焦点事件
    onFocus: (_editor: IDomEditor) => {
      // 当编辑器获得焦点时显示自定义键盘
      if (!disabled && isMountedRef.current) {
        setTimeout(() => {
          if (isMountedRef.current) {
            showKeyboard(_editor, handleCustomKeyPress)
          }
        }, 100)
      }
    },
    // 编辑器内容变化事件 - 任何编辑操作都会触发
    onChange: (_editor: IDomEditor) => {
      // 检查组件是否仍然挂载
      if (!isMountedRef.current) return

      // 内容变化时显示键盘
      if (!disabled) {
        showKeyboard(_editor, handleCustomKeyPress)
      }
      // 内容变化时触发数学公式渲染
      debouncedRenderMath()
    },
    // 编辑器失焦事件
    onBlur: (_editor: IDomEditor) => {
      // 检查组件是否仍然挂载
      if (!isMountedRef.current) return

      // 延迟检查失焦，避免键盘按钮点击导致的瞬时失焦
      setTimeout(() => {
        if (!isMountedRef.current) return

        const activeElement = document.activeElement
        const isKeyboardFocused = activeElement && (
          activeElement.closest('.mathquill-keyboard') ||
          activeElement.closest('.keyboard-key') ||
          activeElement.closest('.keyboard-overlay')
        )

        // 只有当焦点不在键盘内时才隐藏键盘
        if (!isKeyboardFocused) {
          hideKeyboard()
        }
      }, 150)
    }
  }

  // 配置图片上传 - 改为本地预览
  editorConfig.MENU_CONF!['uploadImage'] = {
    async customUpload(file: File, insertFn: any) {
      try {
        console.log('暂存图片文件:', file.name)

        // 验证文件类型和大小
        if (!file.type.startsWith('image/')) {
          alert('请选择有效的图片文件')
          return
        }

        const maxSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxSize) {
          alert('图片文件大小不能超过10MB')
          return
        }

        // 创建本地预览URL
        const tempUrl = URL.createObjectURL(file)

        // 添加到待上传文件列表
        const fileId = addPendingFile(file, tempUrl, 'image')

        // 使用临时URL插入图片，并在alt中存储文件ID用于后续处理
        insertFn(tempUrl, `pending_image_${fileId}`, tempUrl)
        console.log('图片暂存成功，文件ID:', fileId)

      } catch (error) {
        console.error('图片暂存异常:', error)
        alert('图片处理失败: ' + error)
      }
    },
    
    // 单个文件的最大体积限制，默认为 2M
    maxFileSize: 5 * 1024 * 1024, // 5M
    
    // 最多可上传几个文件，默认为 100
    maxNumberOfFiles: 10,
    
    // 选择文件时的类型限制，默认为 ['image/*']
    allowedFileTypes: ['image/*'],
    
    // 上传进度的回调函数
    onProgress(progress: number) {
      console.log('上传进度:', progress)
    },
    
    // 单个文件上传成功之后
    onSuccess(file: File, res: any) {
      console.log(`${file.name} 上传成功`, res)
    },
    
    // 单个文件上传失败
    onFailed(file: File, res: any) {
      console.log(`${file.name} 上传失败`, res)
    },
    
    // 上传错误，或者触发 timeout 超时
    onError(file: File, err: any, res: any) {
      console.log(`${file.name} 上传出错`, err, res)
    }
  }

  // 处理数学公式插入
  const handleMathFormulaInsert = (latex: string) => {
    console.log('Inserting math formula:', latex);
    console.log('Editor instance:', editor);

    // 检查组件是否仍然挂载
    if (!isMountedRef.current) {
      console.warn('Component unmounted, cancelling formula insertion');
      return;
    }

    if (!editor) {
      console.error('Editor instance is null');
      return;
    }

    if (!latex.trim()) {
      console.warn('Empty LaTeX formula');
      setMathDialogOpen(false);
      return;
    }

    try {
      // 再次检查组件状态
      if (!isMountedRef.current) return;

      // 确保编辑器获得焦点
      editor.focus();

      // 方法1: 尝试插入简单的LaTeX文本
      const mathText = `$$${latex}$$`;
      console.log('Inserting math text:', mathText);

      // 先尝试插入文本
      editor.insertText(mathText);

      console.log('Formula inserted successfully');

      // 触发内容变化
      setTimeout(() => {
        if (!isMountedRef.current) return;

        const newHtml = editor.getHtml();
        console.log('New HTML content:', newHtml);
        setHtml(newHtml);
        onChange?.(newHtml);

        // 触发数学公式渲染
        debouncedRenderMath();
      }, 100);

    } catch (error) {
      console.error('Error inserting formula:', error);

      // 回退方案：直接修改HTML内容
      if (isMountedRef.current) {
        try {
          const currentHtml = editor.getHtml();
          const mathText = `$$${latex}$$`;
          const newHtml = currentHtml + `<p>${mathText}</p>`;
          editor.setHtml(newHtml);
          setHtml(newHtml);
          onChange?.(newHtml);
          console.log('Fallback insertion successful');
        } catch (fallbackError) {
          console.error('Fallback insertion failed:', fallbackError);
        }
      }
    }

    setMathDialogOpen(false);
  }

  // 打开数学公式编辑器
  const handleOpenMathDialog = () => {
    setMathDialogOpen(true)
  }

  // 处理音频上传
  const handleAudioUpload = () => {
    audioInputRef.current?.click()
  }

  // 处理音频文件选择
  const handleAudioFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型 - 添加更完整的MIME类型支持
    const allowedTypes = [
      'audio/mpeg',     // MP3
      'audio/mp3',      // MP3 (某些系统)
      'audio/wav',      // WAV
      'audio/wave',     // WAV (某些系统)
      'audio/x-wav',    // WAV (某些系统)
      'audio/ogg',      // OGG
      'audio/mp4',      // M4A
      'audio/m4a',      // M4A
      'audio/aac',      // AAC
      'audio/x-aac',    // AAC (某些系统)
      'audio/flac',     // FLAC
      'audio/webm'      // WebM Audio
    ]

    console.log('上传文件类型:', file.type, '文件名:', file.name)

    if (!allowedTypes.includes(file.type)) {
      // 如果MIME类型检测失败，尝试通过文件扩展名验证
      const fileName = file.name.toLowerCase()
      const allowedExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac', '.webm']
      const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))

      if (!hasValidExtension) {
        alert(`请选择有效的音频文件\n支持格式: MP3, WAV, OGG, M4A, AAC, FLAC, WebM\n当前文件类型: ${file.type}`)
        return
      } else {
        console.log('通过文件扩展名验证通过:', fileName)
      }
    }

    // 验证文件大小 (最大50MB)
    const maxSize = 50 * 1024 * 1024
    if (file.size > maxSize) {
      alert('音频文件大小不能超过50MB')
      return
    }

    try {
      console.log('暂存音频文件:', file.name)

      // 创建本地引用URL
      const tempUrl = URL.createObjectURL(file)

      // 添加到待上传文件列表
      const fileId = addPendingFile(file, tempUrl, 'audio')

      // 使用dangerouslyInsertHtml插入音频HTML
      if (editor) {
        // 创建音频的HTML结构，便于后续URL替换
        const audioHtml = `<p>[音频] 文件: <strong>${file.name}</strong> (待上传: pending_audio_${fileId})</p>`;

        editor.dangerouslyInsertHtml(audioHtml);
        console.log('音频暂存成功，文件ID:', fileId);
      }
    } catch (error) {
      console.error('音频暂存异常:', error)
      alert('音频处理失败: ' + error)
    }

    // 清空input值，允许重复选择同一文件
    event.target.value = ''
  }

  // 及时销毁 editor ，重要！
  useEffect(() => {
    return () => {
      if (editor == null) return

      // 标记组件即将卸载
      isMountedRef.current = false

      // 清理MutationObserver
      const observer = (editor as any)._customObserver
      if (observer) {
        observer.disconnect()
        ;(editor as any)._customObserver = null
      }

      // 清理编辑器相关的事件监听器
      cleanupEventListeners()

      // 销毁编辑器
      try {
        editor.destroy()
      } catch (error) {
        console.warn('编辑器销毁失败:', error)
      }

      setEditor(null)
    }
  }, [editor])

  // 同步外部value到内部state
  useEffect(() => {
    if (value !== html) {
      setHtml(value)
    }
  }, [value])

  // 处理编辑器内容变化
  const handleChange = (editor: IDomEditor) => {
    // 检查组件是否仍然挂载
    if (!isMountedRef.current) return

    const newHtml = editor.getHtml()
    setHtml(newHtml)
    onChange?.(newHtml)

    // 内容变化时显示键盘（如果编辑器有焦点）
    if (!disabled && editor.isFocused() && isMountedRef.current) {
      showKeyboard(editor, handleCustomKeyPress)
    }

    // 触发数学公式渲染
    debouncedRenderMath()
  }

  // 处理编辑器创建
  const handleCreated = (editor: IDomEditor) => {
    // 检查组件是否仍然挂载
    if (!isMountedRef.current) return

    setEditor(editor)
    console.log('WangEditor 创建成功')

    // 编辑器创建后进行初始数学公式渲染
    setTimeout(() => {
      if (isMountedRef.current) {
        renderMathFormulas()
      }
    }, 100)

    // 添加额外的事件监听，确保任何编辑操作都显示键盘
    if (!disabled && isMountedRef.current) {
      // 监听编辑器内容区域的点击事件
      const editorContainer = editor.getEditableContainer()
      if (editorContainer) {
        // 初始化事件监听器数组
        if (!eventListenersRef.current.editorEventHandlers) {
          eventListenersRef.current.editorEventHandlers = []
        }

        // 创建事件处理器函数
        const clickHandler = () => {
          setTimeout(() => {
            if (isMountedRef.current) {
              showKeyboard(editor, handleCustomKeyPress)
            }
          }, 50)
        }

        const keydownHandler = (event: Event) => {
          if (!isMountedRef.current) return
          const keyboardEvent = event as KeyboardEvent
          showKeyboard(editor, handleCustomKeyPress)
          if (keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete') {
            console.log('删除操作检测到:', keyboardEvent.key)
          }
        }

        const inputHandler = () => {
          if (isMountedRef.current) {
            showKeyboard(editor, handleCustomKeyPress)
          }
        }

        const pasteHandler = () => {
          setTimeout(() => {
            if (isMountedRef.current) {
              showKeyboard(editor, handleCustomKeyPress)
            }
          }, 100)
        }

        const cutHandler = () => {
          if (isMountedRef.current) {
            showKeyboard(editor, handleCustomKeyPress)
          }
        }

        const dropHandler = () => {
          setTimeout(() => {
            if (isMountedRef.current) {
              showKeyboard(editor, handleCustomKeyPress)
            }
          }, 100)
        }

        const contextmenuHandler = () => {
          setTimeout(() => {
            if (isMountedRef.current && editor.isFocused()) {
              showKeyboard(editor, handleCustomKeyPress)
            }
          }, 200)
        }

        // 添加事件监听器并保存引用
        const eventHandlers = [
          { element: editorContainer, event: 'click', handler: clickHandler },
          { element: editorContainer, event: 'keydown', handler: keydownHandler },
          { element: editorContainer, event: 'input', handler: inputHandler },
          { element: editorContainer, event: 'paste', handler: pasteHandler },
          { element: editorContainer, event: 'cut', handler: cutHandler },
          { element: editorContainer, event: 'drop', handler: dropHandler },
          { element: editorContainer, event: 'contextmenu', handler: contextmenuHandler }
        ]

        eventHandlers.forEach(({ element, event, handler }) => {
          element.addEventListener(event, handler)
          eventListenersRef.current.editorEventHandlers!.push({ element: element as HTMLElement, event, handler })
        })

        // 监听文本选择变化 - document 级别
        const selectionChangeHandler = () => {
          if (isMountedRef.current && editor.isFocused()) {
            showKeyboard(editor, handleCustomKeyPress)
          }
        }
        document.addEventListener('selectionchange', selectionChangeHandler)
        eventListenersRef.current.selectionChangeHandler = selectionChangeHandler

        // 使用MutationObserver监听DOM变化（捕获所有可能的编辑操作）
        const observer = new MutationObserver(() => {
          if (isMountedRef.current && editor.isFocused()) {
            showKeyboard(editor, handleCustomKeyPress)
          }
        })

        // 开始观察编辑器内容变化
        observer.observe(editorContainer, {
          childList: true,
          subtree: true,
          characterData: true,
          attributes: false
        })

        // 保存observer引用以便清理
        ;(editor as any)._customObserver = observer
      }
    }
  }

  return (
    <>
      <Box
        ref={editorContainerRef}
        sx={{
          border: '1px solid #ccc',
          zIndex: 100,
          height: typeof height === 'number' ? `${height}px` : height,
          // 添加图片样式，设置固定宽高为10px
          '& .w-e-text-container img': {
            width: '60px',
            height: '60px',
            objectFit: 'contain',
            display: 'block',
            margin: '10px auto',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            cursor: 'pointer'
          },
          // 图片悬停效果
          '& .w-e-text-container img:hover': {
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            transform: 'scale(1.02)',
            transition: 'all 0.2s ease'
          },
          // 音频链接样式
          '& .w-e-text-container .pending-audio': {
            color: '#1976d2',
            fontStyle: 'italic'
          },
          '& .w-e-text-container .audio-link': {
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 'bold',
            '&:hover': {
              textDecoration: 'underline'
            }
          },
          // 数学公式样式
          '& .w-e-text-container .MathJax': {
            display: 'inline-block !important',
            margin: '2px 4px',
            verticalAlign: 'middle'
          },
          '& .w-e-text-container .MathJax_Display': {
            display: 'block !important',
            margin: '8px 0',
            textAlign: 'center'
          },
          // 数学公式容器样式
          '& .w-e-text-container mjx-container': {
            display: 'inline-block',
            margin: '2px 4px',
            verticalAlign: 'middle'
          },
          '& .w-e-text-container mjx-container[display="block"]': {
            display: 'block !important',
            margin: '8px 0',
            textAlign: 'center'
          }
        }}
      >
        {showToolbar && (
          <Box sx={{ display: 'flex', borderBottom: '1px solid #ccc' }}>
            <Toolbar
              editor={editor}
              defaultConfig={toolbarConfig}
              mode={mode}
              style={{ flex: 1, border: 'none' }}
            />
            {/* 自定义数学公式按钮 */}
            <Button
              size="small"
              startIcon={<FunctionsIcon />}
              onClick={handleOpenMathDialog}
              sx={{
                minWidth: 'auto',
                px: 1,
                borderRadius: 0,
                borderLeft: '1px solid #ccc',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                },
              }}
              title="插入数学公式"
            >
              公式
            </Button>

            {/* 自定义音频上传按钮 */}
            <Button
              size="small"
              startIcon={<AudioFileIcon />}
              onClick={handleAudioUpload}
              sx={{
                minWidth: 'auto',
                px: 1,
                borderRadius: 0,
                borderLeft: '1px solid #ccc',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                },
              }}
              title="插入音频"
            >
              音频
            </Button>
          </Box>
        )}
        <Editor
          defaultConfig={editorConfig}
          value={html}
          onCreated={handleCreated}
          onChange={handleChange}
          mode={mode}
          style={{
            height: showToolbar ? 'calc(100% - 40px)' : '100%',
            overflowY: 'hidden'
          }}
        />
      </Box>

      {/* MathLive数学公式编辑对话框 */}
      <MathLiveDialog
        open={mathDialogOpen}
        onClose={() => setMathDialogOpen(false)}
        onConfirm={handleMathFormulaInsert}
        title="数学公式编辑器"
        maxWidth="lg"
      />

      {/* 隐藏的音频文件输入 */}
      <input
        ref={audioInputRef}
        type="file"
        accept="audio/*"
        style={{ display: 'none' }}
        onChange={handleAudioFileChange}
      />

      {/* 注意：自定义键盘现在由GlobalKeyboard组件统一管理 */}
    </>
  )
}

// 添加清理函数
WangEditor.displayName = 'WangEditor'

export default WangEditor
