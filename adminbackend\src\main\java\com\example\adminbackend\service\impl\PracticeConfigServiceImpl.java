package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.PracticeConfig;
import com.example.adminbackend.model.KnowledgePoint;
import com.example.adminbackend.model.Chapter;
import com.example.adminbackend.model.Subject;
import com.example.adminbackend.repository.PracticeConfigRepository;
import com.example.adminbackend.repository.KnowledgePointRepository;
import com.example.adminbackend.service.PracticeConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 练习配置服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PracticeConfigServiceImpl implements PracticeConfigService {

    private final PracticeConfigRepository practiceConfigRepository;
    private final KnowledgePointRepository knowledgePointRepository;

    // 默认配置常量
    private static final int DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT = 10;
    private static final int DEFAULT_CHAPTER_TEST_DIVISOR = 10;

    // ==================== 基础CRUD操作 ====================

    @Override
    @Transactional
    public PracticeConfig createConfig(PracticeConfig config, String createdBy) {
        log.info("创建练习配置: configType={}, scopeType={}, targetId={}, questionCount={}", 
                config.getConfigType(), config.getScopeType(), config.getTargetId(), config.getQuestionCount());

        // 验证配置
        if (!validateConfig(config)) {
            throw new RuntimeException("配置验证失败");
        }

        // 检查是否存在冲突
        if (hasConflictingConfig(config)) {
            throw new RuntimeException("存在冲突的配置，请先删除或禁用现有配置");
        }

        config.setCreatedBy(createdBy);
        config.setEnabled(true);
        
        PracticeConfig saved = practiceConfigRepository.save(config);
        log.info("练习配置创建成功: id={}", saved.getId());
        
        return saved;
    }

    @Override
    @Transactional
    public PracticeConfig updateConfig(Long configId, PracticeConfig config) {
        log.info("更新练习配置: configId={}", configId);

        PracticeConfig existing = getConfigById(configId);
        
        // 更新字段
        if (config.getQuestionCount() != null) {
            existing.setQuestionCount(config.getQuestionCount());
        }
        if (config.getSelectionStrategy() != null) {
            existing.setSelectionStrategy(config.getSelectionStrategy());
        }
        if (config.getDescription() != null) {
            existing.setDescription(config.getDescription());
        }
        if (config.getEnabled() != null) {
            existing.setEnabled(config.getEnabled());
        }

        PracticeConfig updated = practiceConfigRepository.save(existing);
        log.info("练习配置更新成功: id={}", updated.getId());
        
        return updated;
    }

    @Override
    public PracticeConfig getConfigById(Long configId) {
        return practiceConfigRepository.findById(configId)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + configId));
    }

    @Override
    @Transactional
    public void deleteConfig(Long configId) {
        log.info("删除练习配置: configId={}", configId);
        
        if (!practiceConfigRepository.existsById(configId)) {
            throw new RuntimeException("配置不存在: " + configId);
        }
        
        practiceConfigRepository.deleteById(configId);
        log.info("练习配置删除成功: configId={}", configId);
    }

    @Override
    @Transactional
    public PracticeConfig toggleConfig(Long configId, boolean enabled) {
        log.info("切换练习配置状态: configId={}, enabled={}", configId, enabled);
        
        PracticeConfig config = getConfigById(configId);
        config.setEnabled(enabled);
        
        PracticeConfig updated = practiceConfigRepository.save(config);
        log.info("练习配置状态切换成功: configId={}, enabled={}", configId, enabled);
        
        return updated;
    }

    // ==================== 查询操作 ====================

    @Override
    public Page<PracticeConfig> getConfigs(Pageable pageable) {
        return practiceConfigRepository.findByEnabledTrueOrderByCreatedAtDesc(pageable);
    }

    @Override
    public Page<PracticeConfig> getConfigsByType(PracticeConfig.ConfigType configType, Pageable pageable) {
        return practiceConfigRepository.findByConfigTypeAndEnabledTrueOrderByCreatedAtDesc(configType, pageable);
    }

    @Override
    public Page<PracticeConfig> getConfigsByScope(PracticeConfig.ScopeType scopeType, Pageable pageable) {
        return practiceConfigRepository.findByScopeTypeAndEnabledTrueOrderByCreatedAtDesc(scopeType, pageable);
    }

    @Override
    public List<PracticeConfig> getAllEnabledConfigs() {
        return practiceConfigRepository.findByEnabledTrueOrderByCreatedAtDesc();
    }

    @Override
    public List<PracticeConfig> getEnabledConfigsByType(PracticeConfig.ConfigType configType) {
        return practiceConfigRepository.findByConfigTypeAndEnabledTrueOrderByCreatedAtDesc(configType);
    }

    // ==================== 核心业务方法 ====================

    @Override
    public Optional<PracticeConfig> getEffectiveConfigForKnowledgePoint(Long knowledgePointId, PracticeConfig.ConfigType configType) {
        log.debug("获取知识点有效配置: knowledgePointId={}, configType={}", knowledgePointId, configType);

        try {
            // 1. 检查知识点自定义配置
            KnowledgePoint knowledgePoint = knowledgePointRepository.findById(knowledgePointId).orElse(null);
            if (knowledgePoint != null && knowledgePoint.getEffectivePracticeCount() != null) {
                log.debug("使用知识点自定义配置: count={}", knowledgePoint.getEffectivePracticeCount());
                // 创建虚拟配置对象返回
                return Optional.of(PracticeConfig.builder()
                        .configType(configType)
                        .scopeType(PracticeConfig.ScopeType.KNOWLEDGE_POINT)
                        .targetId(knowledgePointId)
                        .questionCount(knowledgePoint.getEffectivePracticeCount())
                        .selectionStrategy(PracticeConfig.SelectionStrategy.RANDOM)
                        .enabled(true)
                        .build());
            }

            // 2. 查找系统配置（按优先级）
            if (knowledgePoint != null) {
                Chapter chapter = knowledgePoint.getChapter();
                if (chapter != null && chapter.getSubjectVersion() != null) {
                    Long chapterId = chapter.getId();
                    Subject subject = chapter.getSubjectVersion().getSubject();
                    Long subjectId = subject != null ? subject.getId() : null;

                    // 按优先级查找：知识点 > 章节 > 科目 > 全局
                    List<PracticeConfig> configs = practiceConfigRepository.findEffectiveConfigsForKnowledgePoint(
                            configType, knowledgePointId, chapterId, subjectId);

                    if (!configs.isEmpty()) {
                        log.debug("找到系统配置: scopeType={}, count={}", 
                                configs.get(0).getScopeType(), configs.get(0).getQuestionCount());
                        return Optional.of(configs.get(0));
                    }
                }
            }

            log.debug("未找到有效配置，将使用默认值");
            return Optional.empty();

        } catch (Exception e) {
            log.error("获取知识点有效配置时发生错误: knowledgePointId={}, configType={}", knowledgePointId, configType, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<PracticeConfig> getGlobalConfig(PracticeConfig.ConfigType configType) {
        return practiceConfigRepository.findByConfigTypeAndScopeTypeAndEnabledTrue(
                configType, PracticeConfig.ScopeType.GLOBAL);
    }

    @Override
    @Transactional
    public PracticeConfig createOrUpdateGlobalConfig(PracticeConfig.ConfigType configType, Integer questionCount, 
                                                    PracticeConfig.SelectionStrategy strategy, String createdBy) {
        log.info("创建或更新全局配置: configType={}, questionCount={}, strategy={}", configType, questionCount, strategy);

        Optional<PracticeConfig> existing = getGlobalConfig(configType);
        
        if (existing.isPresent()) {
            // 更新现有配置
            PracticeConfig config = existing.get();
            config.setQuestionCount(questionCount);
            config.setSelectionStrategy(strategy);
            return practiceConfigRepository.save(config);
        } else {
            // 创建新配置
            PracticeConfig config = PracticeConfig.builder()
                    .configType(configType)
                    .scopeType(PracticeConfig.ScopeType.GLOBAL)
                    .targetId(null)
                    .questionCount(questionCount)
                    .selectionStrategy(strategy)
                    .enabled(true)
                    .description(getDefaultDescription(configType, PracticeConfig.ScopeType.GLOBAL))
                    .createdBy(createdBy)
                    .build();
            return practiceConfigRepository.save(config);
        }
    }

    // ==================== 辅助方法 ====================

    @Override
    public boolean validateConfig(PracticeConfig config) {
        if (config == null) {
            return false;
        }
        
        // 验证必需字段
        if (config.getConfigType() == null || config.getScopeType() == null || config.getQuestionCount() == null) {
            return false;
        }
        
        // 验证题目数量范围
        if (config.getQuestionCount() < 1 || config.getQuestionCount() > 100) {
            return false;
        }
        
        // 验证目标ID
        if (config.getScopeType() != PracticeConfig.ScopeType.GLOBAL && config.getTargetId() == null) {
            return false;
        }
        
        if (config.getScopeType() == PracticeConfig.ScopeType.GLOBAL && config.getTargetId() != null) {
            return false;
        }
        
        return true;
    }

    @Override
    public boolean hasConflictingConfig(PracticeConfig config) {
        return practiceConfigRepository.existsByConfigTypeAndScopeTypeAndTargetId(
                config.getConfigType(), config.getScopeType(), config.getTargetId());
    }

    private String getDefaultDescription(PracticeConfig.ConfigType configType, PracticeConfig.ScopeType scopeType) {
        String typeDesc = configType == PracticeConfig.ConfigType.KNOWLEDGE_POINT_PRACTICE ? "知识点练习" : "章节测试";
        String scopeDesc = switch (scopeType) {
            case GLOBAL -> "全局";
            case SUBJECT -> "科目";
            case CHAPTER -> "章节";
            case KNOWLEDGE_POINT -> "知识点";
        };
        return typeDesc + scopeDesc + "配置";
    }

    @Override
    @Transactional
    public PracticeConfig createOrUpdateSubjectConfig(Long subjectId, PracticeConfig.ConfigType configType,
                                                     Integer questionCount, PracticeConfig.SelectionStrategy strategy, String createdBy) {
        log.info("创建或更新科目配置: subjectId={}, configType={}, questionCount={}", subjectId, configType, questionCount);

        Optional<PracticeConfig> existing = practiceConfigRepository.findByConfigTypeAndScopeTypeAndTargetIdAndEnabledTrue(
                configType, PracticeConfig.ScopeType.SUBJECT, subjectId);

        if (existing.isPresent()) {
            PracticeConfig config = existing.get();
            config.setQuestionCount(questionCount);
            config.setSelectionStrategy(strategy);
            return practiceConfigRepository.save(config);
        } else {
            PracticeConfig config = PracticeConfig.builder()
                    .configType(configType)
                    .scopeType(PracticeConfig.ScopeType.SUBJECT)
                    .targetId(subjectId)
                    .questionCount(questionCount)
                    .selectionStrategy(strategy)
                    .enabled(true)
                    .description(getDefaultDescription(configType, PracticeConfig.ScopeType.SUBJECT))
                    .createdBy(createdBy)
                    .build();
            return practiceConfigRepository.save(config);
        }
    }

    @Override
    @Transactional
    public PracticeConfig createOrUpdateChapterConfig(Long chapterId, PracticeConfig.ConfigType configType,
                                                     Integer questionCount, PracticeConfig.SelectionStrategy strategy, String createdBy) {
        log.info("创建或更新章节配置: chapterId={}, configType={}, questionCount={}", chapterId, configType, questionCount);

        Optional<PracticeConfig> existing = practiceConfigRepository.findByConfigTypeAndScopeTypeAndTargetIdAndEnabledTrue(
                configType, PracticeConfig.ScopeType.CHAPTER, chapterId);

        if (existing.isPresent()) {
            PracticeConfig config = existing.get();
            config.setQuestionCount(questionCount);
            config.setSelectionStrategy(strategy);
            return practiceConfigRepository.save(config);
        } else {
            PracticeConfig config = PracticeConfig.builder()
                    .configType(configType)
                    .scopeType(PracticeConfig.ScopeType.CHAPTER)
                    .targetId(chapterId)
                    .questionCount(questionCount)
                    .selectionStrategy(strategy)
                    .enabled(true)
                    .description(getDefaultDescription(configType, PracticeConfig.ScopeType.CHAPTER))
                    .createdBy(createdBy)
                    .build();
            return practiceConfigRepository.save(config);
        }
    }

    @Override
    @Transactional
    public PracticeConfig createOrUpdateKnowledgePointConfig(Long knowledgePointId, PracticeConfig.ConfigType configType,
                                                           Integer questionCount, PracticeConfig.SelectionStrategy strategy, String createdBy) {
        log.info("创建或更新知识点配置: knowledgePointId={}, configType={}, questionCount={}", knowledgePointId, configType, questionCount);

        Optional<PracticeConfig> existing = practiceConfigRepository.findByConfigTypeAndScopeTypeAndTargetIdAndEnabledTrue(
                configType, PracticeConfig.ScopeType.KNOWLEDGE_POINT, knowledgePointId);

        if (existing.isPresent()) {
            PracticeConfig config = existing.get();
            config.setQuestionCount(questionCount);
            config.setSelectionStrategy(strategy);
            return practiceConfigRepository.save(config);
        } else {
            PracticeConfig config = PracticeConfig.builder()
                    .configType(configType)
                    .scopeType(PracticeConfig.ScopeType.KNOWLEDGE_POINT)
                    .targetId(knowledgePointId)
                    .questionCount(questionCount)
                    .selectionStrategy(strategy)
                    .enabled(true)
                    .description(getDefaultDescription(configType, PracticeConfig.ScopeType.KNOWLEDGE_POINT))
                    .createdBy(createdBy)
                    .build();
            return practiceConfigRepository.save(config);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getConfigStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 按配置类型统计
            List<Object[]> typeStats = practiceConfigRepository.countByConfigType();
            Map<String, Long> typeCount = new HashMap<>();
            if (typeStats != null) {
                for (Object[] stat : typeStats) {
                    if (stat != null && stat.length >= 2 && stat[0] != null && stat[1] != null) {
                        typeCount.put(stat[0].toString(), (Long) stat[1]);
                    }
                }
            }
            stats.put("byType", typeCount);

            // 按适用范围统计
            List<Object[]> scopeStats = practiceConfigRepository.countByScopeType();
            Map<String, Long> scopeCount = new HashMap<>();
            if (scopeStats != null) {
                for (Object[] stat : scopeStats) {
                    if (stat != null && stat.length >= 2 && stat[0] != null && stat[1] != null) {
                        scopeCount.put(stat[0].toString(), (Long) stat[1]);
                    }
                }
            }
            stats.put("byScope", scopeCount);

            // 总数统计
            long totalCount = practiceConfigRepository.count();
            stats.put("total", totalCount);

            log.debug("配置统计信息: 总数={}, 按类型={}, 按范围={}", totalCount, typeCount, scopeCount);
            return stats;

        } catch (Exception e) {
            log.error("获取配置统计信息时发生错误", e);
            // 返回默认的空统计信息，而不是抛出异常
            stats.put("byType", new HashMap<String, Long>());
            stats.put("byScope", new HashMap<String, Long>());
            stats.put("total", 0L);
            stats.put("error", "统计信息获取失败: " + e.getMessage());
            return stats;
        }
    }

    @Override
    public List<PracticeConfig> getConfigsByTargetId(Long targetId) {
        return practiceConfigRepository.findByTargetIdAndEnabledTrueOrderByCreatedAtDesc(targetId);
    }

    @Override
    @Transactional
    public List<PracticeConfig> createConfigs(List<PracticeConfig> configs, String createdBy) {
        log.info("批量创建练习配置: count={}", configs.size());

        List<PracticeConfig> savedConfigs = new ArrayList<>();
        for (PracticeConfig config : configs) {
            try {
                PracticeConfig saved = createConfig(config, createdBy);
                savedConfigs.add(saved);
            } catch (Exception e) {
                log.error("批量创建配置时发生错误: config={}", config, e);
                // 继续处理其他配置
            }
        }

        log.info("批量创建练习配置完成: 成功={}, 总数={}", savedConfigs.size(), configs.size());
        return savedConfigs;
    }

    @Override
    @Transactional
    public void deleteConfigs(List<Long> configIds) {
        log.info("批量删除练习配置: count={}", configIds.size());

        for (Long configId : configIds) {
            try {
                deleteConfig(configId);
            } catch (Exception e) {
                log.error("批量删除配置时发生错误: configId={}", configId, e);
                // 继续处理其他配置
            }
        }

        log.info("批量删除练习配置完成");
    }

    @Override
    @Transactional
    public void toggleConfigs(List<Long> configIds, boolean enabled) {
        log.info("批量切换练习配置状态: count={}, enabled={}", configIds.size(), enabled);

        for (Long configId : configIds) {
            try {
                toggleConfig(configId, enabled);
            } catch (Exception e) {
                log.error("批量切换配置状态时发生错误: configId={}", configId, e);
                // 继续处理其他配置
            }
        }

        log.info("批量切换练习配置状态完成");
    }
}
