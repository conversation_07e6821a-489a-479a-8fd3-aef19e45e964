package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.Subject;
import com.example.adminbackend.model.SubjectVersion;
import com.example.adminbackend.repository.SubjectRepository;
import com.example.adminbackend.repository.SubjectVersionRepository;
import com.example.adminbackend.service.SubjectService;
import com.example.adminbackend.service.SubjectVersionService;
import com.example.adminbackend.dto.SubjectDTO;
import com.example.adminbackend.dto.SubjectVersionDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 科目服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SubjectServiceImpl implements SubjectService {

    private final SubjectRepository subjectRepository;
    private final SubjectVersionRepository subjectVersionRepository;
    private final SubjectVersionService subjectVersionService;

    @Override
    @Transactional(readOnly = true)
    public List<Subject> getAllSubjects() {
        log.info("获取所有科目");
        return subjectRepository.findAllByOrderByNameAsc();
    }

    @Override
    @Transactional(readOnly = true)
    public Subject getSubjectById(Long id) {
        log.info("根据ID获取科目: {}", id);
        return subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("科目不存在: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public Subject getSubjectByName(String name) {
        log.info("根据名称获取科目: {}", name);
        return subjectRepository.findByName(name)
                .orElseThrow(() -> new RuntimeException("科目不存在: " + name));
    }

    @Override
    @Transactional
    public Subject createSubject(Subject subject) {
        log.info("创建科目: {}", subject.getName());
        
        // 检查名称是否已存在
        if (subjectRepository.existsByName(subject.getName())) {
            throw new RuntimeException("科目名称已存在: " + subject.getName());
        }
        
        return subjectRepository.save(subject);
    }

    @Override
    @Transactional
    public Subject updateSubject(Long id, Subject subject) {
        log.info("更新科目: {}", id);
        
        Subject existingSubject = getSubjectById(id);
        
        // 检查名称是否与其他科目冲突
        if (!existingSubject.getName().equals(subject.getName()) && 
            subjectRepository.existsByName(subject.getName())) {
            throw new RuntimeException("科目名称已存在: " + subject.getName());
        }
        
        existingSubject.setName(subject.getName());
        existingSubject.setDescription(subject.getDescription());
        
        return subjectRepository.save(existingSubject);
    }

    @Override
    @Transactional
    public void deleteSubject(Long id) {
        log.info("删除科目: {}", id);

        Subject subject = getSubjectById(id);

        // 检查是否有关联的版本
        if (subject.getVersions() != null && !subject.getVersions().isEmpty()) {
            throw new RuntimeException("无法删除科目，存在关联的版本");
        }

        subjectRepository.delete(subject);
    }

    @Override
    @Transactional
    public int deleteSubjectCascade(Long id) {
        log.info("级联删除科目: {}", id);

        Subject subject = getSubjectById(id);

        // 获取科目下的所有版本
        List<SubjectVersion> versions = subjectVersionRepository.findBySubjectIdOrderByCreatedAtAsc(id);
        int totalDeletedFiles = 0;

        if (!versions.isEmpty()) {
            log.info("科目 {} 存在 {} 个版本，开始级联删除", subject.getName(), versions.size());

            // 删除每个版本及其关联的章节和知识点
            for (SubjectVersion version : versions) {
                // 使用SubjectVersionService的级联删除方法
                int deletedFiles = subjectVersionService.deleteSubjectVersion(version.getId());
                totalDeletedFiles += deletedFiles;
            }
        }

        // 删除科目
        subjectRepository.delete(subject);
        log.info("科目级联删除完成: {} (ID: {})", subject.getName(), id);

        return totalDeletedFiles;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return subjectRepository.existsByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Subject> searchSubjects(String keyword) {
        log.info("搜索科目: {}", keyword);
        return subjectRepository.findByKeyword(keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubjectDTO> getSubjectsWithVersions() {
        log.info("获取有版本的科目列表");
        List<Subject> subjects = subjectRepository.findSubjectsWithVersions();

        return subjects.stream().map(subject -> {
            SubjectDTO subjectDTO = new SubjectDTO();
            subjectDTO.setId(subject.getId());
            subjectDTO.setName(subject.getName());
            subjectDTO.setDescription(subject.getDescription());
            subjectDTO.setCreatedAt(subject.getCreatedAt());
            subjectDTO.setUpdatedAt(subject.getUpdatedAt());

            // 转换关联的SubjectVersion
            if (subject.getVersions() != null) {
                List<SubjectVersionDTO> versionDTOs = subject.getVersions().stream().map(version -> {
                    SubjectVersionDTO versionDTO = new SubjectVersionDTO();
                    versionDTO.setId(version.getId());
                    versionDTO.setName(version.getName());
                    versionDTO.setDescription(version.getDescription());
                    versionDTO.setSchoolLevel(version.getSchoolLevel());
                    versionDTO.setCreatedAt(version.getCreatedAt());
                    versionDTO.setUpdatedAt(version.getUpdatedAt());
                    versionDTO.setSubjectId(version.getSubject().getId()); // 设置关联的subjectId
                    return versionDTO;
                }).collect(java.util.stream.Collectors.toList());
                subjectDTO.setVersions(versionDTOs);
            }
            return subjectDTO;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubjectDTO> getAllSubjectsAsDTO() {
        log.info("获取所有科目（DTO格式）");
        List<Subject> subjects = subjectRepository.findAllByOrderByNameAsc();

        return subjects.stream().map(subject -> {
            SubjectDTO subjectDTO = new SubjectDTO();
            subjectDTO.setId(subject.getId());
            subjectDTO.setName(subject.getName());
            subjectDTO.setDescription(subject.getDescription());
            subjectDTO.setCreatedAt(subject.getCreatedAt());
            subjectDTO.setUpdatedAt(subject.getUpdatedAt());

            // 通过Repository安全获取版本信息，避免懒加载问题
            List<SubjectVersion> versions = subjectVersionRepository.findBySubjectIdOrderByCreatedAtAsc(subject.getId());
            if (!versions.isEmpty()) {
                List<SubjectVersionDTO> versionDTOs = versions.stream().map(version -> {
                    SubjectVersionDTO versionDTO = new SubjectVersionDTO();
                    versionDTO.setId(version.getId());
                    versionDTO.setName(version.getName());
                    versionDTO.setDescription(version.getDescription());
                    versionDTO.setSchoolLevel(version.getSchoolLevel());
                    versionDTO.setCreatedAt(version.getCreatedAt());
                    versionDTO.setUpdatedAt(version.getUpdatedAt());
                    versionDTO.setSubjectId(subject.getId());
                    return versionDTO;
                }).collect(java.util.stream.Collectors.toList());
                subjectDTO.setVersions(versionDTOs);
            }

            return subjectDTO;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public SubjectDTO getSubjectByIdAsDTO(Long id) {
        log.info("根据ID获取科目（DTO格式）: {}", id);
        Subject subject = subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("科目不存在: " + id));

        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setId(subject.getId());
        subjectDTO.setName(subject.getName());
        subjectDTO.setDescription(subject.getDescription());
        subjectDTO.setCreatedAt(subject.getCreatedAt());
        subjectDTO.setUpdatedAt(subject.getUpdatedAt());
        // 不包含versions，避免懒加载问题
        return subjectDTO;
    }
}
