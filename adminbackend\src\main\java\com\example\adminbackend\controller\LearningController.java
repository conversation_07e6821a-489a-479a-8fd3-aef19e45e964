package com.example.adminbackend.controller;

import com.example.adminbackend.dto.ApiResponse;
import com.example.adminbackend.model.Question;
import com.example.adminbackend.service.QuestionSelectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 学习端控制器
 * 提供题目选择和学习相关的API
 */
@RestController
@RequestMapping("/learning")
@RequiredArgsConstructor
@Slf4j
public class LearningController {

    private final QuestionSelectionService questionSelectionService;

    // ==================== 知识点练习相关 ====================

    /**
     * 获取知识点练习题目
     * 根据配置智能选择题目
     */
    @GetMapping("/knowledge-points/{knowledgePointId}/questions")
    public ResponseEntity<ApiResponse<List<Question>>> getKnowledgePointQuestions(
            @PathVariable Long knowledgePointId,
            @RequestParam(required = false) Long studentId) {
        try {
            log.info("获取知识点练习题目请求: knowledgePointId={}, studentId={}", knowledgePointId, studentId);

            List<Question> questions = questionSelectionService.selectQuestionsForKnowledgePointPractice(
                    knowledgePointId, studentId);

            log.info("知识点练习题目获取成功: knowledgePointId={}, 题目数量={}", knowledgePointId, questions.size());
            return ResponseEntity.ok(ApiResponse.success(questions, "获取练习题目成功"));

        } catch (Exception e) {
            log.error("获取知识点练习题目失败: knowledgePointId={}", knowledgePointId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取练习题目失败: " + e.getMessage()));
        }
    }

    /**
     * 获取知识点的有效练习题目数量配置
     */
    @GetMapping("/knowledge-points/{knowledgePointId}/practice-count")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEffectivePracticeCount(
            @PathVariable Long knowledgePointId) {
        try {
            log.debug("获取知识点练习题目数量配置: knowledgePointId={}", knowledgePointId);

            Integer practiceCount = questionSelectionService.getEffectivePracticeCount(knowledgePointId);
            
            Map<String, Object> result = Map.of(
                "knowledgePointId", knowledgePointId,
                "questionCount", practiceCount,
                "message", "本次练习共 " + practiceCount + " 道题目"
            );

            return ResponseEntity.ok(ApiResponse.success(result, "获取配置成功"));

        } catch (Exception e) {
            log.error("获取知识点练习题目数量配置失败: knowledgePointId={}", knowledgePointId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取配置失败: " + e.getMessage()));
        }
    }

    /**
     * 获取知识点练习统计信息
     */
    @GetMapping("/knowledge-points/{knowledgePointId}/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getKnowledgePointStatistics(
            @PathVariable Long knowledgePointId) {
        try {
            log.debug("获取知识点练习统计信息: knowledgePointId={}", knowledgePointId);

            Map<String, Object> statistics = questionSelectionService.getSelectionStatistics(knowledgePointId);

            return ResponseEntity.ok(ApiResponse.success(statistics, "获取统计信息成功"));

        } catch (Exception e) {
            log.error("获取知识点练习统计信息失败: knowledgePointId={}", knowledgePointId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取统计信息失败: " + e.getMessage()));
        }
    }

    // ==================== 章节测试相关 ====================

    /**
     * 获取章节测试题目
     * 从章节下每个知识点按规则抽题
     */
    @GetMapping("/chapters/{chapterId}/questions")
    public ResponseEntity<ApiResponse<List<Question>>> getChapterQuestions(
            @PathVariable Long chapterId,
            @RequestParam(required = false) Long studentId) {
        try {
            log.info("获取章节测试题目请求: chapterId={}, studentId={}", chapterId, studentId);

            List<Question> questions = questionSelectionService.selectQuestionsForChapterTest(chapterId, studentId);

            log.info("章节测试题目获取成功: chapterId={}, 题目数量={}", chapterId, questions.size());
            return ResponseEntity.ok(ApiResponse.success(questions, "获取测试题目成功"));

        } catch (Exception e) {
            log.error("获取章节测试题目失败: chapterId={}", chapterId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取测试题目失败: " + e.getMessage()));
        }
    }

    /**
     * 获取章节测试题目分配方案
     * 显示每个知识点分配的题目数量
     */
    @GetMapping("/chapters/{chapterId}/question-allocation")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getChapterTestQuestionAllocation(
            @PathVariable Long chapterId) {
        try {
            log.debug("获取章节测试题目分配方案: chapterId={}", chapterId);

            Map<Long, Integer> allocation = questionSelectionService.getChapterTestQuestionAllocation(chapterId);
            Integer totalCount = questionSelectionService.calculateChapterTestTotalCount(chapterId);

            Map<String, Object> result = Map.of(
                "chapterId", chapterId,
                "allocation", allocation,
                "totalQuestions", totalCount,
                "message", "本次测试共 " + totalCount + " 道题目，从各知识点中智能抽取"
            );

            return ResponseEntity.ok(ApiResponse.success(result, "获取分配方案成功"));

        } catch (Exception e) {
            log.error("获取章节测试题目分配方案失败: chapterId={}", chapterId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取分配方案失败: " + e.getMessage()));
        }
    }

    // ==================== 通用查询接口 ====================

    /**
     * 根据策略选择题目（通用接口）
     */
    @PostMapping("/questions/select")
    public ResponseEntity<ApiResponse<List<Question>>> selectQuestionsByStrategy(
            @RequestBody Map<String, Object> request) {
        try {
            // 解析请求参数
            @SuppressWarnings("unchecked")
            List<Long> questionIds = (List<Long>) request.get("questionIds");
            Integer count = (Integer) request.get("count");
            String strategy = (String) request.get("strategy");
            Long studentId = request.get("studentId") != null ? 
                    Long.valueOf(request.get("studentId").toString()) : null;

            if (questionIds == null || questionIds.isEmpty() || count == null || count <= 0) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "请求参数无效"));
            }

            log.info("通用题目选择请求: questionIds.size={}, count={}, strategy={}, studentId={}", 
                    questionIds.size(), count, strategy, studentId);

            // 这里需要根据questionIds获取Question对象，然后调用选择服务
            // 为了简化，这个接口可以后续完善
            return ResponseEntity.ok(ApiResponse.success(List.of(), "功能开发中"));

        } catch (Exception e) {
            log.error("通用题目选择失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "题目选择失败: " + e.getMessage()));
        }
    }

    // ==================== 配置查询接口 ====================

    /**
     * 获取有效的抽题策略
     */
    @GetMapping("/knowledge-points/{knowledgePointId}/strategy")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEffectiveSelectionStrategy(
            @PathVariable Long knowledgePointId) {
        try {
            log.debug("获取知识点抽题策略: knowledgePointId={}", knowledgePointId);

            var strategy = questionSelectionService.getEffectiveSelectionStrategy(knowledgePointId);

            Map<String, Object> result = Map.of(
                "knowledgePointId", knowledgePointId,
                "strategy", strategy.name(),
                "strategyDescription", getStrategyDescription(strategy)
            );

            return ResponseEntity.ok(ApiResponse.success(result, "获取抽题策略成功"));

        } catch (Exception e) {
            log.error("获取知识点抽题策略失败: knowledgePointId={}", knowledgePointId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "获取抽题策略失败: " + e.getMessage()));
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取策略描述
     */
    private String getStrategyDescription(com.example.adminbackend.model.PracticeConfig.SelectionStrategy strategy) {
        return switch (strategy) {
            case RANDOM -> "随机选择";
            case DIFFICULTY_BALANCED -> "难度均衡";
            case ERROR_PRIORITY -> "错题优先";
            case TYPE_BALANCED -> "题型均衡";
        };
    }
}
