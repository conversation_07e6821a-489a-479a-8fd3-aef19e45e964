/*
SQLyog Community v12.4.0 (64 bit)
MySQL - 8.0.12 : Database - aistrusys
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`aistrusys` /*!40100 DEFAULT CHARACTER SET utf8 */;

USE `aistrusys`;

/*Table structure for table `agent_card_subject_versions` */

DROP TABLE IF EXISTS `agent_card_subject_versions`;

CREATE TABLE `agent_card_subject_versions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `agent_card_subject_id` bigint(20) NOT NULL,
  `subject_version_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_subject_version` (`agent_card_subject_id`,`subject_version_id`),
  KEY `FKkkrmomvqpa8e7jw3e52ers9ep` (`subject_version_id`),
  CONSTRAINT `FKb2o1gg5tj5ry3at8ndi54rv8r` FOREIGN KEY (`agent_card_subject_id`) REFERENCES `agent_card_subjects` (`id`),
  CONSTRAINT `FKkkrmomvqpa8e7jw3e52ers9ep` FOREIGN KEY (`subject_version_id`) REFERENCES `subject_versions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8;

/*Data for the table `agent_card_subject_versions` */

insert  into `agent_card_subject_versions`(`id`,`created_at`,`updated_at`,`agent_card_subject_id`,`subject_version_id`) values 

(2,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',2,1),

(3,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',2,2),

(4,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',3,28),

(5,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',3,31),

(6,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',4,47),

(7,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',5,50),

(8,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,1),

(9,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,2),

(10,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,3),

(11,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',7,28),

(12,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',7,31),

(13,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',8,47),

(14,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',8,49),

(15,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',9,50),

(16,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',9,52),

(17,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',10,1),

(18,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',11,28),

(19,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',12,1),

(20,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',12,23),

(21,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',12,24),

(22,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',13,28),

(23,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',13,40),

(24,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',13,41),

(25,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',14,47),

(26,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',14,49),

(27,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',15,1),

(28,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',15,2),

(29,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',15,3),

(30,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',15,19),

(31,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',16,28),

(32,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',16,31),

(33,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',16,40),

(34,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',17,47),

(35,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',17,49),

(36,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',18,50),

(37,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',18,52);

/*Table structure for table `agent_card_subjects` */

DROP TABLE IF EXISTS `agent_card_subjects`;

CREATE TABLE `agent_card_subjects` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `agent_card_id` bigint(20) NOT NULL,
  `subject_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_subject` (`agent_card_id`,`subject_id`),
  KEY `FKae4bgty5vm58d8mjbf34vsqhy` (`subject_id`),
  CONSTRAINT `FK36wshv7r1augyuvld3k6rtxi0` FOREIGN KEY (`agent_card_id`) REFERENCES `agent_cards` (`id`),
  CONSTRAINT `FKae4bgty5vm58d8mjbf34vsqhy` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;

/*Data for the table `agent_card_subjects` */

insert  into `agent_card_subjects`(`id`,`created_at`,`updated_at`,`agent_card_id`,`subject_id`) values 

(2,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',2,1),

(3,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',2,2),

(4,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',2,3),

(5,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',2,4),

(6,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',3,1),

(7,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',3,2),

(8,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',3,3),

(9,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',3,4),

(10,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',4,1),

(11,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',4,2),

(12,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',5,1),

(13,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',5,2),

(14,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',5,3),

(15,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,1),

(16,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,2),

(17,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,3),

(18,'2025-06-10 05:24:15.000000','2025-06-10 05:24:15.000000',6,4);

/*Table structure for table `agent_cards` */

DROP TABLE IF EXISTS `agent_cards`;

CREATE TABLE `agent_cards` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `card_type` varchar(255) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `formal_or_trial` varchar(255) NOT NULL,
  `remaining` int(11) NOT NULL,
  `total` int(11) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `valid_days` int(11) DEFAULT NULL,
  `agent_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKmro3s6atdo06h6tas1uexrt3r` (`agent_id`),
  CONSTRAINT `FKmro3s6atdo06h6tas1uexrt3r` FOREIGN KEY (`agent_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

/*Data for the table `agent_cards` */

insert  into `agent_cards`(`id`,`card_type`,`created_at`,`formal_or_trial`,`remaining`,`total`,`updated_at`,`valid_days`,`agent_id`) values 

(2,'试用卡','2025-06-10 05:24:15.000000','试用',184,200,'2025-06-24 01:14:23.948000',7,5),

(3,'年卡','2025-06-10 05:24:15.000000','正式',94,200,'2025-06-24 01:14:32.661000',365,5),

(4,'试用卡','2025-06-10 05:24:15.000000','试用',95,100,'2025-06-10 05:24:15.000000',15,6),

(5,'高级卡','2025-06-10 05:24:15.000000','正式',20,30,'2025-06-10 05:24:15.000000',365,6),

(6,'企业卡','2025-06-10 05:24:15.000000','正式',10,10,'2025-06-10 05:24:15.000000',730,6);

/*Table structure for table `agent_hierarchies` */

DROP TABLE IF EXISTS `agent_hierarchies`;

CREATE TABLE `agent_hierarchies` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agent_id` bigint(20) NOT NULL,
  `parent_id` bigint(20) DEFAULT NULL,
  `level` int(11) NOT NULL DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `agent_id_unique` (`agent_id`),
  KEY `parent_id_index` (`parent_id`),
  CONSTRAINT `agent_hierarchies_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `agent_hierarchies_ibfk_2` FOREIGN KEY (`parent_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `agent_hierarchies` */

/*Table structure for table `chapters` */

DROP TABLE IF EXISTS `chapters`;

CREATE TABLE `chapters` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `order_index` int(11) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `subject_version_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK8s1tww8q6qy48ui4op0vk9x9i` (`subject_version_id`),
  CONSTRAINT `FK8s1tww8q6qy48ui4op0vk9x9i` FOREIGN KEY (`subject_version_id`) REFERENCES `subject_versions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

/*Data for the table `chapters` */

insert  into `chapters`(`id`,`created_at`,`name`,`order_index`,`updated_at`,`subject_version_id`) values 

(1,'2025-06-24 06:37:35.633000','第一单元 走进化学世界',0,'2025-06-24 06:37:35.633000',1);

/*Table structure for table `collection_videos` */

DROP TABLE IF EXISTS `collection_videos`;

CREATE TABLE `collection_videos` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `collection_id` bigint(20) NOT NULL,
  `video_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_collection_video` (`collection_id`,`video_id`),
  KEY `FK23or1l886xio6itd3px7ejsig` (`video_id`),
  CONSTRAINT `FK23or1l886xio6itd3px7ejsig` FOREIGN KEY (`video_id`) REFERENCES `videos` (`id`),
  CONSTRAINT `FK27kr6s9m7rmhrtonn34eglw0c` FOREIGN KEY (`collection_id`) REFERENCES `video_collections` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

/*Data for the table `collection_videos` */

insert  into `collection_videos`(`id`,`created_at`,`collection_id`,`video_id`) values 

(1,'2025-06-26 18:54:18.000000',5,5),

(2,'2025-06-26 14:53:43.689000',5,6),

(3,'2025-06-26 15:54:20.828000',6,7),

(4,'2025-06-26 16:10:20.066000',7,8);

/*Table structure for table `consumption_records` */

DROP TABLE IF EXISTS `consumption_records`;

CREATE TABLE `consumption_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `student_id` bigint(20) NOT NULL,
  `type` enum('CARD_USE','SUBSCRIPTION','UPGRADE') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `operator_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `student_id_index` (`student_id`),
  KEY `operator_id_index` (`operator_id`),
  CONSTRAINT `consumption_records_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `consumption_records_ibfk_2` FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `consumption_records` */

/*Table structure for table `knowledge_points` */

DROP TABLE IF EXISTS `knowledge_points`;

CREATE TABLE `knowledge_points` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cover_image_url` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `order_index` int(11) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `chapter_id` bigint(20) NOT NULL,
  `description` text,
  `enabled` bit(1) NOT NULL,
  `video_collection_id` bigint(20) DEFAULT NULL,
  `custom_practice_count` int(11) DEFAULT NULL,
  `use_custom_config` bit(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKq84n79xjqi4nnk96rf0r342hk` (`chapter_id`),
  KEY `FKoirghem35wj2vkmeftt1gbebf` (`video_collection_id`),
  CONSTRAINT `FKoirghem35wj2vkmeftt1gbebf` FOREIGN KEY (`video_collection_id`) REFERENCES `video_collections` (`id`),
  CONSTRAINT `FKq84n79xjqi4nnk96rf0r342hk` FOREIGN KEY (`chapter_id`) REFERENCES `chapters` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

/*Data for the table `knowledge_points` */

insert  into `knowledge_points`(`id`,`cover_image_url`,`created_at`,`name`,`order_index`,`updated_at`,`chapter_id`,`description`,`enabled`,`video_collection_id`,`custom_practice_count`,`use_custom_config`) values 

(1,'https://stuvideo.studya.top/image/2025/06/26/1750951340665_036191c8bf964967ac8d9259a5f9180a.jpg','2025-06-24 06:37:35.662000','化学变化和物理变化',0,'2025-06-26 15:22:24.895000',1,'从源数据导入','',5,5,'\0');

/*Table structure for table `orders` */

DROP TABLE IF EXISTS `orders`;

CREATE TABLE `orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `operation_type` enum('ADD_REGULAR','ADD_TRIAL','RENEWAL','TRIAL_RESET','TRIAL_TO_REGULAR') NOT NULL,
  `order_number` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `operator_id` bigint(20) NOT NULL,
  `student_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKnthkiu7pgmnqnu86i2jyoe2v7` (`order_number`),
  KEY `FK5lchxpqhw6n3w58pcla09mpsh` (`operator_id`),
  KEY `FKeudclkhb1qymuoqf76gg10i3t` (`student_id`),
  CONSTRAINT `FK5lchxpqhw6n3w58pcla09mpsh` FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FKeudclkhb1qymuoqf76gg10i3t` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8;

/*Data for the table `orders` */

/*Table structure for table `practice_configs` */

DROP TABLE IF EXISTS `practice_configs`;

CREATE TABLE `practice_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `config_type` enum('CHAPTER_TEST','KNOWLEDGE_POINT_PRACTICE') NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `enabled` bit(1) NOT NULL,
  `question_count` int(11) NOT NULL,
  `scope_type` enum('CHAPTER','GLOBAL','KNOWLEDGE_POINT','SUBJECT') NOT NULL,
  `selection_strategy` enum('DIFFICULTY_BALANCED','ERROR_PRIORITY','RANDOM','TYPE_BALANCED') NOT NULL,
  `target_id` bigint(20) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

/*Data for the table `practice_configs` */

insert  into `practice_configs`(`id`,`config_type`,`created_at`,`created_by`,`description`,`enabled`,`question_count`,`scope_type`,`selection_strategy`,`target_id`,`updated_at`) values 

(1,'KNOWLEDGE_POINT_PRACTICE','2025-06-26 08:09:32.919000','***********','知识点练习全局配置','',5,'GLOBAL','RANDOM',NULL,'2025-06-26 08:09:32.919000'),

(2,'KNOWLEDGE_POINT_PRACTICE','2025-06-26 08:10:02.594000','***********','知识点练习科目配置','',5,'SUBJECT','RANDOM',4,'2025-06-26 08:10:02.594000'),

(3,'KNOWLEDGE_POINT_PRACTICE','2025-06-26 08:10:18.812000','***********','知识点练习章节配置','',5,'CHAPTER','RANDOM',1,'2025-06-26 08:10:18.812000'),

(4,'KNOWLEDGE_POINT_PRACTICE','2025-06-26 08:10:36.512000','***********','知识点练习知识点配置','',5,'KNOWLEDGE_POINT','RANDOM',1,'2025-06-26 08:10:36.512000');

/*Table structure for table `questions` */

DROP TABLE IF EXISTS `questions`;

CREATE TABLE `questions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `body` json NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `enabled` bit(1) NOT NULL DEFAULT b'1',
  `knowledge_point_id` bigint(20) DEFAULT NULL,
  `question_type` enum('CLOZE_TEST','FILL_IN_BLANK','LISTENING','MATCHING','MULTIPLE_CHOICE','READING_COMPREHENSION','SINGLE_CHOICE','TRUE_FALSE') NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8;

/*Data for the table `questions` */

insert  into `questions`(`id`,`body`,`created_at`,`enabled`,`knowledge_point_id`,`question_type`,`updated_at`) values 

(91,'{\"id\": \"1750983274134\", \"tags\": [], \"type\": \"SINGLE_CHOICE\", \"answer\": \"A\", \"content\": \"<p><img src=\\\"http://stuvideo.studya.top/image/2025/06/27/1750983273796_b76a620250ae42f1b4126a5459f0e836.jpg\\\" alt=\\\"pending_image_file_1750983167546_bb9u6ynrf\\\" data-href=\\\"http://stuvideo.studya.top/image/2025/06/27/1750983273796_b76a620250ae42f1b4126a5459f0e836.jpg\\\" style=\\\"\\\"/></p>\", \"options\": [\"hh\", \"hh\", \"hh\", \"hh\"], \"difficulty\": \"EASY\", \"explanation\": \"hh\"}','2025-06-27 00:14:34','',1,'SINGLE_CHOICE','2025-06-27 00:14:34'),

(95,'{\"id\": \"1750984679705\", \"tags\": [], \"type\": \"SINGLE_CHOICE\", \"answer\": \"A\", \"content\": \"<p>[音频] 文件: <strong>relaxing.mp3</strong> <a href=\\\"http://stuvideo.studya.top/audio/2025/06/27/1750984679270_07b6a62ae6364add820d89f72345289f.mp3\\\" target=\\\"_blank\\\" class=\\\"audio-link\\\">(点击播放)</a>,hhhhh</p>\", \"options\": [\"aa\", \"aa\", \"aa\", \"aa\"], \"difficulty\": \"EASY\", \"explanation\": \"aaaa\"}','2025-06-27 00:38:00','',1,'SINGLE_CHOICE','2025-06-27 00:38:00'),

(96,'{\"id\": \"1750986809623\", \"tags\": [], \"type\": \"READING_COMPREHENSION\", \"answer\": \"\", \"content\": \"aa\", \"subject\": \"CHEMISTRY\", \"material\": \"aa\", \"difficulty\": \"EASY\", \"explanation\": \"aa\", \"subQuestions\": [{\"id\": \"sub_1750986786113\", \"type\": \"SINGLE_CHOICE\", \"answer\": \"A\", \"content\": \"aa\", \"options\": [\"aa\", \"aa\", \"aa\", \"aa\"], \"explanation\": \"aa\"}]}','2025-06-27 01:13:30','',1,'READING_COMPREHENSION','2025-06-27 01:13:30'),

(97,'{\"id\": \"1750986876602\", \"tags\": [], \"type\": \"LISTENING\", \"answer\": \"\", \"content\": \"aa\", \"subject\": \"CHEMISTRY\", \"difficulty\": \"EASY\", \"explanation\": \"aaa\", \"subQuestions\": [{\"id\": \"sub_1750986841212\", \"type\": \"SINGLE_CHOICE\", \"answer\": \"A\", \"content\": \"aa\", \"options\": [\"aa\", \"aa\", \"aa\", \"aa\"], \"explanation\": \"aaa\"}]}','2025-06-27 01:14:37','',1,'LISTENING','2025-06-27 01:14:37');

/*Table structure for table `regions` */

DROP TABLE IF EXISTS `regions`;

CREATE TABLE `regions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `level` int(11) NOT NULL,
  `parent_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code_unique` (`code`),
  KEY `parent_id_index` (`parent_id`),
  CONSTRAINT `regions_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `regions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `regions` */

insert  into `regions`(`id`,`name`,`code`,`level`,`parent_id`,`created_at`,`updated_at`) values 

(1,'四川省','SC',1,NULL,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(2,'重庆市','CQ',1,NULL,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(3,'云南省','YN',1,NULL,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(4,'北京市','BJ',1,NULL,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(5,'上海市','SH',1,NULL,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(6,'广安市','SC_GA',2,1,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(7,'成都市','SC_CD',2,1,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(8,'自贡市','SC_ZG',2,1,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(9,'渝中区','CQ_YZ',2,2,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(10,'江北区','CQ_JB',2,2,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(11,'沙坪坝区','CQ_SPB',2,2,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(12,'昆明市','YN_KM',2,3,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(13,'大理州','YN_DL',2,3,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(14,'广安区','SC_GA_GA',3,6,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(15,'前锋区','SC_GA_QF',3,6,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(16,'武胜县','SC_GA_WS',3,6,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(17,'岳池县','SC_GA_YC',3,6,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(18,'邻水县','SC_GA_LS',3,6,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(19,'华蓥市','SC_GA_HY',3,6,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(20,'武侯区','SC_CD_WH',3,7,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(21,'锦江区','SC_CD_JJ',3,7,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(22,'青羊区','SC_CD_QY',3,7,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(23,'金牛区','SC_CD_JN',3,7,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(24,'盘龙区','YN_KM_PL',3,12,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(25,'五华区','YN_KM_WH',3,12,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(26,'官渡区','YN_KM_GD',3,12,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(27,'西山区','YN_KM_XS',3,12,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(28,'大理市','YN_DL_DL',3,13,'2025-06-08 03:29:38','2025-06-08 03:29:38'),

(29,'祥云县','YN_DL_XY',3,13,'2025-06-08 03:29:38','2025-06-08 03:29:38');

/*Table structure for table `student_answers` */

DROP TABLE IF EXISTS `student_answers`;

CREATE TABLE `student_answers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `attempt_timestamp` datetime(6) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `is_correct` bit(1) NOT NULL,
  `submitted_answer` json NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `knowledge_point_id` bigint(20) NOT NULL,
  `question_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKojp8vroqtx1igyt1ma6vwmf3` (`knowledge_point_id`),
  KEY `FK8nyksamccim8emu803uhf2da` (`question_id`),
  KEY `FK7ine3irxhlo98sfub0vu686r7` (`student_id`),
  CONSTRAINT `FK7ine3irxhlo98sfub0vu686r7` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`),
  CONSTRAINT `FK8nyksamccim8emu803uhf2da` FOREIGN KEY (`question_id`) REFERENCES `questions` (`id`),
  CONSTRAINT `FKojp8vroqtx1igyt1ma6vwmf3` FOREIGN KEY (`knowledge_point_id`) REFERENCES `knowledge_points` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=569 DEFAULT CHARSET=utf8;

/*Data for the table `student_answers` */

insert  into `student_answers`(`id`,`attempt_timestamp`,`created_at`,`is_correct`,`submitted_answer`,`updated_at`,`knowledge_point_id`,`question_id`,`student_id`) values 

(559,'2025-06-27 00:29:49.285000','2025-06-27 00:29:49.294000','','\"A\"','2025-06-27 00:29:49.294000',1,91,39),

(561,'2025-06-27 01:07:34.003000','2025-06-27 01:07:34.003000','','\"A\"','2025-06-27 01:07:34.003000',1,91,39),

(562,'2025-06-27 01:14:46.280000','2025-06-27 01:14:46.280000','','\"A\"','2025-06-27 01:14:46.280000',1,95,39),

(563,'2025-06-27 01:14:53.396000','2025-06-27 01:14:53.396000','','\"A\"','2025-06-27 01:14:53.396000',1,91,39),

(564,'2025-06-27 01:14:56.605000','2025-06-27 01:14:56.605000','','\"A\"','2025-06-27 01:14:56.605000',1,95,39),

(565,'2025-06-27 01:15:05.328000','2025-06-27 01:15:05.328000','','\"A\"','2025-06-27 01:15:05.328000',1,91,39),

(566,'2025-06-27 01:15:08.357000','2025-06-27 01:15:08.357000','','\"A\"','2025-06-27 01:15:08.357000',1,95,39),

(567,'2025-06-27 01:15:13.980000','2025-06-27 01:15:13.980000','','{\"sub_1750986786113\": \"A\"}','2025-06-27 01:15:13.980000',1,96,39),

(568,'2025-06-27 01:15:21.735000','2025-06-27 01:15:21.735000','','{\"sub_1750986841212\": \"A\"}','2025-06-27 01:15:21.735000',1,97,39);

/*Table structure for table `student_course_contents` */

DROP TABLE IF EXISTS `student_course_contents`;

CREATE TABLE `student_course_contents` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `grade` varchar(255) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `subject_version_id` bigint(20) NOT NULL,
  `trial_id` bigint(20) NOT NULL,
  `student_id` bigint(20) DEFAULT NULL,
  `subject_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK3vkx25otrbxygi8gmlok9b8xf` (`subject_version_id`),
  KEY `FK5o8juxcaw6ybojlbidab4cujk` (`trial_id`),
  KEY `FKous8qm95m27rpw9jdn2g6pn7j` (`student_id`),
  KEY `FKe5jsve7g96q9ihw4r1f7r4j3g` (`subject_id`),
  CONSTRAINT `FK3vkx25otrbxygi8gmlok9b8xf` FOREIGN KEY (`subject_version_id`) REFERENCES `subject_versions` (`id`),
  CONSTRAINT `FK5o8juxcaw6ybojlbidab4cujk` FOREIGN KEY (`trial_id`) REFERENCES `student_subject_trials` (`id`),
  CONSTRAINT `FKe5jsve7g96q9ihw4r1f7r4j3g` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`),
  CONSTRAINT `FKous8qm95m27rpw9jdn2g6pn7j` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=125 DEFAULT CHARSET=utf8;

/*Data for the table `student_course_contents` */

insert  into `student_course_contents`(`id`,`created_at`,`grade`,`updated_at`,`subject_version_id`,`trial_id`,`student_id`,`subject_id`) values 

(124,'2025-06-25 02:46:00.024000','九年级上','2025-06-25 02:46:00.024000',1,88,39,4);

/*Table structure for table `student_courses` */

DROP TABLE IF EXISTS `student_courses`;

CREATE TABLE `student_courses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `course_name` varchar(100) NOT NULL,
  `course_type` varchar(50) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `end_date` datetime(6) DEFAULT NULL,
  `start_date` datetime(6) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Data for the table `student_courses` */

/*Table structure for table `student_progress` */

DROP TABLE IF EXISTS `student_progress`;

CREATE TABLE `student_progress` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `last_accessed_at` datetime(6) DEFAULT NULL,
  `progress` int(11) NOT NULL,
  `status` enum('COMPLETED','IN_PROGRESS','NOT_STARTED') NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `knowledge_point_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKi76wv3n7bdwol6k4ev1qjhoqy` (`knowledge_point_id`),
  KEY `FKo0ptk24xm7njb9nbhsjldncrl` (`student_id`),
  CONSTRAINT `FKi76wv3n7bdwol6k4ev1qjhoqy` FOREIGN KEY (`knowledge_point_id`) REFERENCES `knowledge_points` (`id`),
  CONSTRAINT `FKo0ptk24xm7njb9nbhsjldncrl` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8;

/*Data for the table `student_progress` */

insert  into `student_progress`(`id`,`created_at`,`last_accessed_at`,`progress`,`status`,`updated_at`,`knowledge_point_id`,`student_id`) values 

(17,'2025-06-25 10:18:46.463000','2025-06-27 01:15:21.737000',100,'COMPLETED','2025-06-27 01:15:21.737000',1,39);

/*Table structure for table `student_region_mappings` */

DROP TABLE IF EXISTS `student_region_mappings`;

CREATE TABLE `student_region_mappings` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `is_primary` bit(1) NOT NULL,
  `relation_type` enum('RESIDENCE','SCHOOL_LOCATION','STUDY_REGION') NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `region_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_region_type_primary_unique` (`student_id`,`relation_type`,`is_primary`),
  KEY `FKjsqx8c506kxs78ki3e0tp3uw0` (`region_id`),
  CONSTRAINT `FK8edbf47e14nauctbtjr1eendw` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`),
  CONSTRAINT `FKjsqx8c506kxs78ki3e0tp3uw0` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Data for the table `student_region_mappings` */

/*Table structure for table `student_subject_trials` */

DROP TABLE IF EXISTS `student_subject_trials`;

CREATE TABLE `student_subject_trials` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `days_remaining` int(11) DEFAULT NULL,
  `end_date` datetime(6) DEFAULT NULL,
  `start_date` datetime(6) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `trial_card_id` bigint(20) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `student_id` bigint(20) NOT NULL,
  `subject_id` bigint(20) NOT NULL,
  `subject_version_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKglntcnlbc4nns2f1jt4ithb81` (`subject_id`),
  KEY `FK2gokptbpvoerepmback7jdq71` (`subject_version_id`),
  KEY `FK_student_subject_trials_student_v2` (`student_id`),
  CONSTRAINT `FK2gokptbpvoerepmback7jdq71` FOREIGN KEY (`subject_version_id`) REFERENCES `subject_versions` (`id`),
  CONSTRAINT `FK_student_subject_trials_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_student_subject_trials_student_v2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FKglntcnlbc4nns2f1jt4ithb81` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`),
  CONSTRAINT `FKt6aunuh9vvxmbjmbnvdpb4iur` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8;

/*Data for the table `student_subject_trials` */

insert  into `student_subject_trials`(`id`,`created_at`,`days_remaining`,`end_date`,`start_date`,`status`,`trial_card_id`,`updated_at`,`student_id`,`subject_id`,`subject_version_id`) values 

(30,'2025-06-12 10:12:01.293000',7,'2025-06-17 17:22:30.360000','2025-06-17 17:02:52.334000','CONVERTED',2,'2025-06-17 17:22:30.426000',2,1,NULL),

(31,'2025-06-12 10:12:01.293000',7,'2025-06-17 17:22:30.366000','2025-06-17 17:02:52.334000','CONVERTED',2,'2025-06-17 17:22:30.426000',2,2,NULL),

(48,'2025-06-12 22:55:42.973000',352,'2026-06-12 22:55:42.973000','2025-06-12 22:55:42.973000','ACTIVE',3,'2025-06-26 17:00:00.348000',24,1,1),

(49,'2025-06-12 22:55:42.979000',352,'2026-06-12 22:55:42.979000','2025-06-12 22:55:42.979000','ACTIVE',3,'2025-06-26 17:00:00.349000',24,3,47),

(50,'2025-06-15 17:10:54.399000',355,'2026-06-15 17:10:54.399000','2025-06-15 17:10:54.399000','ACTIVE',3,'2025-06-26 17:00:00.350000',25,3,47),

(51,'2025-06-15 17:10:54.405000',355,'2026-06-15 17:10:54.405000','2025-06-15 17:10:54.405000','ACTIVE',3,'2025-06-26 17:00:00.350000',25,4,50),

(52,'2025-06-17 04:42:52.502000',364,'2025-06-17 04:43:09.081000','2025-06-17 04:42:52.502000','CONVERTED',2,'2025-06-17 04:43:09.081000',26,1,NULL),

(53,'2025-06-17 04:42:52.502000',364,'2025-06-17 04:43:09.081000','2025-06-17 04:42:52.502000','CONVERTED',2,'2025-06-17 04:43:09.081000',26,2,NULL),

(54,'2025-06-17 04:42:52.508000',364,'2025-06-17 04:43:09.081000','2025-06-17 04:42:52.508000','CONVERTED',2,'2025-06-17 04:43:09.081000',26,3,NULL),

(55,'2025-06-17 04:42:52.508000',364,'2025-06-17 04:43:09.081000','2025-06-17 04:42:52.508000','CONVERTED',2,'2025-06-17 04:43:09.081000',26,4,NULL),

(57,'2025-06-17 14:11:41.694000',1,'2025-06-24 01:00:46.223000','2025-06-17 14:11:41.694000','CONVERTED',2,'2025-06-24 01:00:46.307000',31,3,NULL),

(58,'2025-06-17 14:11:41.694000',1,'2025-06-24 01:00:46.229000','2025-06-17 14:11:41.694000','CONVERTED',2,'2025-06-24 01:00:46.307000',31,2,NULL),

(59,'2025-06-17 14:11:41.700000',1,'2025-06-24 01:00:46.229000','2025-06-17 14:11:41.700000','CONVERTED',2,'2025-06-24 01:00:46.307000',31,1,NULL),

(60,'2025-06-17 14:11:41.700000',1,'2025-06-24 01:00:46.231000','2025-06-17 14:11:41.700000','CONVERTED',2,'2025-06-24 01:00:46.307000',31,4,NULL),

(61,'2025-06-17 14:40:19.343000',356,'2026-06-17 14:40:19.343000','2025-06-17 14:40:19.343000','ACTIVE',3,'2025-06-26 17:00:00.350000',32,1,1),

(62,'2025-06-17 14:40:19.359000',356,'2026-06-17 14:40:19.359000','2025-06-17 14:40:19.359000','ACTIVE',3,'2025-06-26 17:00:00.350000',32,2,28),

(63,'2025-06-17 14:40:19.367000',356,'2026-06-17 14:40:19.367000','2025-06-17 14:40:19.367000','ACTIVE',3,'2025-06-26 17:00:00.350000',32,3,47),

(64,'2025-06-17 14:40:19.380000',356,'2026-06-17 14:40:19.379000','2025-06-17 14:40:19.379000','ACTIVE',3,'2025-06-26 17:00:00.350000',32,4,50),

(65,'2025-06-17 15:01:39.896000',7,'2025-06-17 15:01:50.889000','2025-06-17 15:01:39.896000','CONVERTED',2,'2025-06-17 15:01:50.895000',33,3,NULL),

(66,'2025-06-17 15:01:39.900000',7,'2025-06-17 15:01:50.889000','2025-06-17 15:01:39.900000','CONVERTED',2,'2025-06-17 15:01:50.895000',33,1,NULL),

(67,'2025-06-17 15:01:39.900000',7,'2025-06-17 15:01:50.889000','2025-06-17 15:01:39.900000','CONVERTED',2,'2025-06-17 15:01:50.895000',33,2,NULL),

(68,'2025-06-17 15:01:39.900000',7,'2025-06-17 15:01:50.889000','2025-06-17 15:01:39.900000','CONVERTED',2,'2025-06-17 15:01:50.895000',33,4,NULL),

(69,'2025-06-23 03:53:10.145000',7,'2025-06-24 00:49:07.505000','2025-06-23 03:53:10.145000','CONVERTED',2,'2025-06-24 00:49:07.598000',34,2,NULL),

(70,'2025-06-23 03:53:10.151000',7,'2025-06-24 00:49:07.512000','2025-06-23 03:53:10.151000','CONVERTED',2,'2025-06-24 00:49:07.598000',34,3,NULL),

(71,'2025-06-23 03:53:10.153000',7,'2025-06-24 00:49:07.512000','2025-06-23 03:53:10.153000','CONVERTED',2,'2025-06-24 00:49:07.598000',34,4,NULL),

(72,'2025-06-23 03:53:10.156000',7,'2025-06-24 00:49:07.512000','2025-06-23 03:53:10.156000','CONVERTED',2,'2025-06-24 00:49:07.598000',34,1,NULL),

(73,'2025-06-23 23:43:17.289000',7,'2025-06-24 00:49:50.183000','2025-06-23 23:43:17.289000','CONVERTED',2,'2025-06-24 00:49:50.187000',35,2,NULL),

(74,'2025-06-23 23:43:17.297000',7,'2025-06-24 00:49:50.183000','2025-06-23 23:43:17.297000','CONVERTED',2,'2025-06-24 00:49:50.187000',35,3,NULL),

(75,'2025-06-23 23:43:17.304000',7,'2025-06-24 00:49:50.183000','2025-06-23 23:43:17.304000','CONVERTED',2,'2025-06-24 00:49:50.187000',35,4,NULL),

(76,'2025-06-23 23:43:17.310000',7,'2025-06-24 00:49:50.183000','2025-06-23 23:43:17.309000','CONVERTED',2,'2025-06-24 00:49:50.187000',35,1,NULL),

(77,'2025-06-24 01:00:46.233000',363,'2026-06-24 01:00:46.231000','2025-06-24 01:00:46.231000','ACTIVE',3,'2025-06-26 17:00:00.350000',31,3,NULL),

(78,'2025-06-24 01:00:46.288000',363,'2026-06-24 01:00:46.231000','2025-06-24 01:00:46.231000','ACTIVE',3,'2025-06-26 17:00:00.350000',31,2,NULL),

(79,'2025-06-24 01:00:46.291000',363,'2026-06-24 01:00:46.231000','2025-06-24 01:00:46.231000','ACTIVE',3,'2025-06-26 17:00:00.350000',31,1,NULL),

(80,'2025-06-24 01:00:46.293000',363,'2026-06-24 01:00:46.231000','2025-06-24 01:00:46.231000','ACTIVE',3,'2025-06-26 17:00:00.350000',31,4,NULL),

(81,'2025-06-24 01:14:23.930000',7,'2025-06-24 01:14:32.652000','2025-06-24 01:14:23.928000','CONVERTED',2,'2025-06-24 01:14:32.661000',39,2,NULL),

(82,'2025-06-24 01:14:23.932000',7,'2025-06-24 01:14:32.653000','2025-06-24 01:14:23.932000','CONVERTED',2,'2025-06-24 01:14:32.661000',39,1,NULL),

(83,'2025-06-24 01:14:23.934000',7,'2025-06-24 01:14:32.653000','2025-06-24 01:14:23.934000','CONVERTED',2,'2025-06-24 01:14:32.661000',39,3,NULL),

(84,'2025-06-24 01:14:23.936000',7,'2025-06-24 01:14:32.653000','2025-06-24 01:14:23.936000','CONVERTED',2,'2025-06-24 01:14:32.661000',39,4,NULL),

(85,'2025-06-24 01:14:32.653000',363,'2026-06-24 01:14:32.653000','2025-06-24 01:14:32.653000','ACTIVE',3,'2025-06-26 17:00:00.350000',39,2,NULL),

(86,'2025-06-24 01:14:32.655000',363,'2026-06-24 01:14:32.653000','2025-06-24 01:14:32.653000','ACTIVE',3,'2025-06-26 17:00:00.350000',39,1,NULL),

(87,'2025-06-24 01:14:32.656000',363,'2026-06-24 01:14:32.653000','2025-06-24 01:14:32.653000','ACTIVE',3,'2025-06-26 17:00:00.350000',39,3,NULL),

(88,'2025-06-24 01:14:32.657000',363,'2026-06-24 01:14:32.653000','2025-06-24 01:14:32.653000','ACTIVE',3,'2025-06-26 17:00:00.352000',39,4,NULL);

/*Table structure for table `student_supervisor_relations` */

DROP TABLE IF EXISTS `student_supervisor_relations`;

CREATE TABLE `student_supervisor_relations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `student_id` bigint(20) NOT NULL,
  `supervisor_id` bigint(20) NOT NULL,
  `start_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `end_date` datetime DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_supervisor` (`student_id`,`supervisor_id`,`is_active`),
  KEY `student_id_index` (`student_id`),
  KEY `supervisor_id_index` (`supervisor_id`),
  CONSTRAINT `FKda0yn5t7pqadhdtkgr6c8dr34` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`),
  CONSTRAINT `student_supervisor_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_supervisor_ibfk_2` FOREIGN KEY (`supervisor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `student_supervisor_relations` */

insert  into `student_supervisor_relations`(`id`,`student_id`,`supervisor_id`,`start_date`,`end_date`,`is_active`,`created_at`,`updated_at`) values 

(9,2,8,'2025-06-12 23:04:54',NULL,1,'2025-06-12 23:04:54','2025-06-12 23:04:54'),

(10,24,8,'2025-06-12 23:49:43',NULL,1,'2025-06-12 23:49:43','2025-06-12 23:49:43');

/*Table structure for table `student_trial_cards` */

DROP TABLE IF EXISTS `student_trial_cards`;

CREATE TABLE `student_trial_cards` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `activated_at` datetime(6) DEFAULT NULL,
  `activation_code` varchar(255) NOT NULL,
  `card_number` varchar(255) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `subject_ids` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `valid_days` int(11) DEFAULT NULL,
  `creator_id` bigint(20) DEFAULT NULL,
  `student_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKfe5cwsmgtgx1n0dee4yqs3i3j` (`card_number`),
  KEY `FK3ltjbd7ll66rnjt92bjc45rgv` (`creator_id`),
  KEY `FKouty0yl31d5wacirayw3vna58` (`student_id`),
  CONSTRAINT `FK3ltjbd7ll66rnjt92bjc45rgv` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FKouty0yl31d5wacirayw3vna58` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Data for the table `student_trial_cards` */

/*Table structure for table `students` */

DROP TABLE IF EXISTS `students`;

CREATE TABLE `students` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_type` varchar(255) NOT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `enabled` bit(1) NOT NULL,
  `grade` varchar(255) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `real_name` varchar(255) DEFAULT NULL,
  `remarks` varchar(500) DEFAULT NULL,
  `school` varchar(255) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `username` varchar(255) NOT NULL,
  `valid_from` datetime(6) DEFAULT NULL,
  `valid_to` datetime(6) DEFAULT NULL,
  `creator_id` bigint(20) DEFAULT NULL,
  `dealer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKakwqgcdnid3qo41cqpdu4ke01` (`username`),
  UNIQUE KEY `UK4j48kma5fa3dcya13gd0l3gi` (`phone`),
  KEY `FKeefqdta9bxf24am4qgtjo5ta6` (`creator_id`),
  KEY `FK72xqgijtn6vjsnix1qskgs4x8` (`dealer_id`),
  CONSTRAINT `FK72xqgijtn6vjsnix1qskgs4x8` FOREIGN KEY (`dealer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FKeefqdta9bxf24am4qgtjo5ta6` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8;

/*Data for the table `students` */

insert  into `students`(`id`,`account_type`,`create_time`,`created_at`,`enabled`,`grade`,`password`,`phone`,`real_name`,`remarks`,`school`,`updated_at`,`username`,`valid_from`,`valid_to`,`creator_id`,`dealer_id`) values 

(2,'REGULAR','2025-06-12 10:12:01.269000','2025-06-12 10:12:01.275000','','','$2a$10$zAPV8TTKSrr0bObDdn/PieWvLI9wHv6f931Y.aZL6FE3v9vxZqJAm','***********','zx1','aa','','2025-06-17 17:22:30.426000','zx1','2025-06-12 10:12:01.275000',NULL,5,5),

(24,'REGULAR','2025-06-12 22:55:42.967000','2025-06-12 22:55:42.967000','','','$2a$10$zAPV8TTKSrr0bObDdn/PieWvLI9wHv6f931Y.aZL6FE3v9vxZqJAm','***********','zx13','jj','','2025-06-12 23:50:47.088000','zx13','2025-06-12 22:55:42.967000',NULL,5,5),

(25,'REGULAR','2025-06-15 17:10:54.382000','2025-06-15 17:10:54.382000','','','$2a$10$OADPB1ciV6BCI9Kpwp6IGOa4bvSQYKyKwZPHnKvUEDFH63YRzj8pS','***********','zx133','5','','2025-06-15 17:10:54.382000','zx133','2025-06-15 17:10:54.382000',NULL,5,5),

(26,'REGULAR','2025-06-17 04:42:52.490000','2025-06-17 04:42:52.490000','','','$2a$10$QQl19E0SpoeiOc9mhbzOzOD4rLFdRQyer9wa8cc.Le.DmB5N1V8cK','19999999991','zx2','111','','2025-06-17 09:52:52.498000','zx2','2025-06-17 04:42:52.490000',NULL,5,5),

(31,'REGULAR','2025-06-17 14:11:41.688000','2025-06-17 14:11:41.688000','','','$2a$10$WXfB6B83a1.jsN7.qWbG9OrfcJFmHTgmrnL75hHdW3B9F7ItdNALW','19999999995','zx279','hhh','','2025-06-24 01:00:46.307000','zx279','2025-06-24 01:00:46.294000','2026-06-24 01:00:46.294000',5,5),

(32,'REGULAR','2025-06-17 14:40:19.270000','2025-06-17 14:40:19.277000','','','$2a$10$9j2BcpNGV7K/qSxis0IWeuUwbME9Cjv.mSFNTEQn6yDpSbTc8YmBG','17777777773','zx134','hh','','2025-06-17 14:40:19.277000','zx134','2025-06-17 14:40:19.277000',NULL,5,5),

(33,'REGULAR','2025-06-17 15:01:39.842000','2025-06-17 15:01:39.842000','','','$2a$10$1Mg8iRYJpMnASV61bsM04eqbmfSvrliDcumzZI6Qqkb7t0R76IjLm','19999599995','zx2799','kk','','2025-06-17 15:01:50.895000','zx2799','2025-06-17 15:01:39.842000',NULL,5,5),

(34,'REGULAR','2025-06-23 03:53:10.063000','2025-06-23 03:53:10.071000','','','$2a$10$OPN/arL3b2Bv3CFSIHD9reJPt70jyEpI2OoL593BRxr5hcDJA8qfW','18888888821','zx1356','1212','','2025-06-24 00:49:07.598000','zx1356','2025-06-24 00:49:07.513000','2026-06-24 00:49:07.513000',5,5),

(35,'REGULAR','2025-06-23 23:43:17.131000','2025-06-23 23:43:17.145000','','','$2a$10$Onx2r6zqn8hl4a.xDJUVeOsiU2lO9iQgg4Nx1kT3xMktTsNxkU6H2','18898888821','zx1356234','1','','2025-06-24 00:49:50.186000','zx1356234','2025-06-24 00:49:50.183000','2026-06-24 00:49:50.183000',5,5),

(39,'REGULAR','2025-06-24 01:14:23.904000','2025-06-24 01:14:23.905000','','','$2a$10$zAPV8TTKSrr0bObDdn/PieWvLI9wHv6f931Y.aZL6FE3v9vxZqJAm','18898788821','zx0624','we','','2025-06-24 01:14:32.661000','zx0624','2025-06-24 01:14:32.658000','2026-06-24 01:14:32.658000',5,5);

/*Table structure for table `subject_versions` */

DROP TABLE IF EXISTS `subject_versions`;

CREATE TABLE `subject_versions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `school_level` varchar(50) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `subject_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKf5v6sy0ui9ls2hf4js3txeqgp` (`subject_id`),
  CONSTRAINT `FKf5v6sy0ui9ls2hf4js3txeqgp` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

/*Data for the table `subject_versions` */

insert  into `subject_versions`(`id`,`created_at`,`description`,`name`,`school_level`,`updated_at`,`subject_id`) values 

(1,'2025-06-24 06:37:35.568000','从源数据导入','初中人教版','九年级上','2025-06-24 06:37:35.568000',4);

/*Table structure for table `subjects` */

DROP TABLE IF EXISTS `subjects`;

CREATE TABLE `subjects` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `name` varchar(50) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

/*Data for the table `subjects` */

insert  into `subjects`(`id`,`created_at`,`description`,`name`,`updated_at`) values 

(1,'2025-06-08 22:19:37.750000','英语学科','英语','2025-06-08 22:19:37.750000'),

(2,'2025-06-08 22:19:38.222000','数学学科','数学','2025-06-08 22:19:38.222000'),

(3,'2025-06-08 22:19:38.527000','物理学科','物理','2025-06-08 22:19:38.527000'),

(4,'2025-06-08 22:19:38.590000','化学学科','化学','2025-06-08 22:19:38.590000');

/*Table structure for table `user_region_mappings` */

DROP TABLE IF EXISTS `user_region_mappings`;

CREATE TABLE `user_region_mappings` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `region_id` bigint(20) NOT NULL,
  `relation_type` enum('MANAGEMENT','SUPERVISION','AGENCY') NOT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_region_type_primary_unique` (`user_id`,`relation_type`,`is_primary`),
  UNIQUE KEY `UK_user_region_relation_primary` (`user_id`,`relation_type`,`is_primary`),
  KEY `user_region_index` (`user_id`,`region_id`),
  KEY `region_id` (`region_id`),
  CONSTRAINT `user_region_mappings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_region_mappings_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `user_region_mappings` */

insert  into `user_region_mappings`(`id`,`user_id`,`region_id`,`relation_type`,`is_primary`,`created_at`,`updated_at`) values 

(15,6,18,'AGENCY',1,'2025-06-08 23:55:21','2025-06-08 23:55:21'),

(18,8,1,'AGENCY',1,'2025-06-09 12:46:55','2025-06-09 12:46:55'),

(19,9,18,'AGENCY',1,'2025-06-09 21:11:33','2025-06-09 21:11:33'),

(24,5,18,'AGENCY',1,'2025-06-15 17:10:28','2025-06-15 17:10:28'),

(25,23,18,'AGENCY',1,'2025-06-17 04:42:06','2025-06-17 04:42:06'),

(31,24,16,'MANAGEMENT',1,'2025-06-17 06:26:05','2025-06-17 06:26:05'),

(45,5,15,'MANAGEMENT',1,'2025-06-23 23:13:37','2025-06-23 23:13:37');

/*Table structure for table `users` */

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `role` enum('SUPER_ADMIN','DEALER','ADMIN','SUPERVISOR','AGENT','STUDENT') NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `valid_from` datetime DEFAULT NULL,
  `valid_to` datetime DEFAULT NULL,
  `creator_id` bigint(20) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creator` varchar(255) DEFAULT NULL,
  `supervisor_accounts` int(11) DEFAULT NULL,
  `account_type` varchar(255) DEFAULT NULL,
  `version` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_unique` (`username`),
  UNIQUE KEY `phone_unique` (`phone`),
  UNIQUE KEY `uk_user_phone_role` (`phone`,`role`),
  KEY `role_index` (`role`),
  KEY `creator_id` (`creator_id`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `users` */

insert  into `users`(`id`,`username`,`password`,`phone`,`full_name`,`role`,`enabled`,`valid_from`,`valid_to`,`creator_id`,`created_at`,`updated_at`,`creator`,`supervisor_accounts`,`account_type`,`version`) values 

(5,'***********k','$2a$10$zAPV8TTKSrr0bObDdn/PieWvLI9wHv6f931Y.aZL6FE3v9vxZqJAm','***********','dd2','DEALER',1,'2025-06-10 00:00:00','2025-07-31 00:00:00',NULL,'2025-06-08 21:41:07','2025-06-19 07:07:33','dealer001',NULL,NULL,1),

(6,'***********','$2a$10$54fiy/vVWjX.UumQfBl8aezQOcPZ8jQCR3VJUpkpmYHNW05PJbnH2','***********','aa','AGENT',1,'2025-06-11 00:00:00','2025-08-02 00:00:00',NULL,'2025-06-08 23:55:21','2025-06-19 14:11:00','dealer001',NULL,NULL,0),

(8,'yuu','$2a$10$LflRQYuhlL.7z0ZnpmJJ8eiUyNX54Gk4otO6J5xS6GfGaVgWrH46G','***********','dx','SUPERVISOR',0,'2025-06-10 00:00:00','2025-07-31 00:00:00',NULL,'2025-06-09 12:46:55','2025-06-21 23:46:58','dealer001',NULL,'督学账号',2),

(9,'***********','$2a$10$LFGPmHKlEZl10Vf2EQ2L9eShhOgbiHaqcj8IJ5MSzQqXlOVvEL.q6','***********','aa','SUPER_ADMIN',1,'2025-06-11 00:00:00','2025-07-31 00:00:00',NULL,'2025-06-09 21:11:33','2025-06-23 14:13:06','dealer001',NULL,NULL,0),

(23,'***********','$2a$10$zmYHLZci8tam3opsW6spQe8aJtnrcV0YlNYEiX3RgjFg17nIhbaTC','***********','dd3','AGENT',1,'2025-06-17 00:00:00','2025-07-31 00:00:00',NULL,'2025-06-17 04:42:06','2025-06-17 04:42:06','***********',NULL,NULL,0),

(24,'dd565073','$2a$10$.aNI6t93w0pfsEvg0rxxq.jNDEqk/J8y3ifVig.eCwvlBM.I3UY8K','19999999995','dd5','DEALER',1,'2025-06-13 00:00:00','2025-07-26 00:00:00',NULL,'2025-06-17 06:26:05','2025-06-17 06:26:05','dd2',5,NULL,0),

(25,'superadmin','$2a$10$.aUvyUthb.5Qhz9771rF2uIvaomITz8tyCRcU2gVH0WiBs35Hxty6',NULL,'超级管理员','SUPER_ADMIN',1,'2025-06-18 23:52:46',NULL,NULL,'2025-06-18 23:52:46','2025-06-18 23:52:46','system',NULL,'系统账户',0),

(27,'admin','$2a$10$JVCfIbS91FFA2ID8CsvpH.x0GAnokkVmM8eUqAHuIq36qPJU9Qun2',NULL,'管理员','ADMIN',1,'2025-06-19 00:54:10',NULL,NULL,'2025-06-19 00:54:10','2025-06-19 00:54:10','system',NULL,'系统账户',0),

(28,'dealer13800138000','$2a$10$6W0dZEB9YMFxZ9vog5Vqqek.5qJZdxPQRAqL47Hael2ZX6SI7LJAq','13800138000','测试经销商','DEALER',1,NULL,NULL,NULL,'2025-06-19 06:12:08','2025-06-19 06:12:08',NULL,NULL,NULL,0),

(29,'admin13900139000','$2a$10$3RYXNEDOuoIWku7wav30Iey8OzPoo5MZwvIt4wzGRDO37ehZMJ7mi','13900139000','系统管理员','ADMIN',1,NULL,NULL,NULL,'2025-06-19 06:12:08','2025-06-19 06:12:08',NULL,NULL,NULL,0),

(30,'aa','$2a$10$ZyMofq5VKjZ.zKxcjstQAeijOVRw155LQG1FoY6OOChl4dR5FmEES','13333333332','bb','SUPERVISOR',1,NULL,NULL,NULL,'2025-06-22 00:09:18','2025-06-22 00:09:18','***********',NULL,NULL,0);

/*Table structure for table `video_collections` */

DROP TABLE IF EXISTS `video_collections`;

CREATE TABLE `video_collections` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cover_image_url` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `description` text,
  `name` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;

/*Data for the table `video_collections` */

insert  into `video_collections`(`id`,`cover_image_url`,`created_at`,`description`,`name`,`updated_at`) values 

(5,NULL,'2025-06-24 06:37:35.657000','知识点视频合集: 化学变化和物理变化','化学变化和物理变化_20250624_143735','2025-06-24 06:37:35.657000'),

(6,NULL,'2025-06-26 15:54:20.821000','知识点视频合集: aa','aa_20250626_235420','2025-06-26 15:54:20.821000'),

(7,NULL,'2025-06-26 16:10:20.060000','知识点视频合集: aa','aa_20250627_001020','2025-06-26 16:10:20.060000');

/*Table structure for table `video_progress` */

DROP TABLE IF EXISTS `video_progress`;

CREATE TABLE `video_progress` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `is_completed` bit(1) NOT NULL,
  `last_position` int(11) DEFAULT NULL,
  `last_watched_at` datetime(6) DEFAULT NULL,
  `progress_seconds` int(11) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `student_id` bigint(20) NOT NULL,
  `video_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_video` (`student_id`,`video_id`),
  KEY `FKdaqxc66uvuhgmv78ohg6qeqvi` (`video_id`),
  CONSTRAINT `FK60b6r5ys8p9ge15un84pbg7ci` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`),
  CONSTRAINT `FKdaqxc66uvuhgmv78ohg6qeqvi` FOREIGN KEY (`video_id`) REFERENCES `videos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

/*Data for the table `video_progress` */

insert  into `video_progress`(`id`,`created_at`,`is_completed`,`last_position`,`last_watched_at`,`progress_seconds`,`updated_at`,`student_id`,`video_id`) values 

(1,'2025-06-26 10:55:16.690000','\0',98,'2025-06-26 17:29:10.505000',98,'2025-06-26 17:29:10.505000',39,5);

/*Table structure for table `videos` */

DROP TABLE IF EXISTS `videos`;

CREATE TABLE `videos` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cover_image_url` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `description` text,
  `duration_seconds` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `video_url` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8;

/*Data for the table `videos` */

insert  into `videos`(`id`,`cover_image_url`,`created_at`,`description`,`duration_seconds`,`title`,`updated_at`,`video_url`) values 

(5,NULL,'2025-06-24 06:37:35.648000','知识点视频: 化学变化和物理变化',NULL,'化学变化和物理变化_20250624_143735','2025-06-24 06:37:35.648000','http://stuvideo.studya.top/video/ebc764a959d760e177691d61d20b44ad'),

(6,NULL,'2025-06-26 14:53:43.648000','从知识点视频字段自动创建',NULL,'视频-1750949621625_8d16678e2b054ac699463ed8403b87dd','2025-06-26 14:53:43.648000','https://stuvideo.studya.top/video/2025/06/26/1750949621625_8d16678e2b054ac699463ed8403b87dd.mp4'),

(7,NULL,'2025-06-26 15:54:20.779000','知识点视频: aa',NULL,'aa_20250626_235420','2025-06-26 15:54:20.779000','http://stuvideo.studya.top/video/2025/06/26/1750953258717_09ce928e798442adb0fcbad6999ac446.mp4'),

(8,NULL,'2025-06-26 16:10:20.043000','知识点视频: aa',NULL,'aa_20250627_001020','2025-06-26 16:10:20.043000','http://stuvideo.studya.top/video/2025/06/27/1750954218427_fc06417c817144ec8336c7f33b17bbb9.mp4');

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
