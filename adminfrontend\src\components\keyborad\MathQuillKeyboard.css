/* MathQuill自定义键盘样式 */
/* 使用命名空间前缀避免与管理端全局样式冲突 */

/* 键盘覆盖层 */
.keyboard-overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: transparent;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

/* 主键盘容器 */
.mathquill-keyboard {
  position: relative !important;
  width: 100% !important; /* 确保占满全宽 */
  height: 290px !important; /* 适当增加键盘高度，配合更大的按键尺寸 */
  background: #fafafa !important; /* 更淡的灰色背景，提升视觉舒适度 */
  border-top: 1px solid #e0e0e0 !important;
  border-radius: 0 !important; /* 移除圆角，因为是全宽布局 */
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15) !important;
  padding: 16px !important; /* 减少内边距 */
  animation: slideUpFromBottom 0.3s ease-out;
  overflow: hidden !important; /* 防止内容溢出 */
  box-sizing: border-box !important; /* 确保padding不会导致溢出 */
}

/* 键盘动画 */
@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.mathquill-keyboard.animating {
  animation: slideUpFromBottom 0.3s ease-out;
}

/* 确保键盘容器有正确的布局 */
.mathquill-keyboard {
  display: flex !important;
  flex-direction: column !important;
}

/* 双面板容器 */
.keyboard-container {
  display: flex !important;
  gap: 16px !important; /* 减少面板间距 */
  width: 100% !important;
  height: 100% !important;
  /* 全宽布局，但在超大屏幕上限制内容区域 */
  max-width: none !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* 面板基础样式 */
.math-panel,
.numeric-panel {
  background: #f8f8f8 !important; /* 柔和的浅灰色面板，与按键颜色协调 */
  border-radius: 8px !important;
  padding: 12px !important; /* 减少内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  height: 100% !important; /* 确保面板占满容器高度 */
  box-sizing: border-box !important; /* 包含padding在内的高度计算 */
  /* 确保面板有明显的边框和背景 */
  border: 1px solid #e0e0e0 !important;
}

/* 右侧数字面板特殊样式 - 自然铺满容器 */
.numeric-panel {
  padding: 12px; /* 适中的内边距 */
  display: flex;
  flex-direction: column;
}

.numeric-panel .panel-grid {
  flex: 1; /* 占满剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为从顶部开始排列，避免行距过大 */
  gap: 8px; /* 增大行间距，配合更大的按键 */
}

.numeric-panel .keyboard-key {
  height: 42px; /* 增大数字键盘按键高度，更好地填满容器 */
  font-size: 16px; /* 增大字体，提升可读性 */
  padding: 8px 12px; /* 增大内边距，提升按键舒适度 */
  border-radius: 8px; /* 增大圆角，配合更大的按键 */
}

.numeric-panel .keyboard-row {
  gap: 8px; /* 增大数字键盘按键间距，与整体保持一致 */
  margin-bottom: 0; /* 移除固定行间距，使用flex布局 */
}

/* 左侧数学符号面板 */
.math-panel {
  flex: 0 0 45%; /* 固定45%宽度 */
  height: 100%; /* 占满整个容器高度 */
  display: flex;
  flex-direction: column;
}

/* 右侧数字运算面板 */
.numeric-panel {
  flex: 0 0 55%; /* 固定55%宽度 */
  height: 100%; /* 占满整个容器高度 */
}

/* 英语键盘面板 */
.english-keyboard-panel {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #f8f8f8; /* 柔和的浅灰色，与整体风格协调 */
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 英语键盘按键高度 */
.english-keyboard-panel .keyboard-key {
  height: 42px; /* 英语键盘按键高度设置为42px */
  font-size: 16px; /* 相应调整字体大小 */
  min-width: 32px; /* 基础最小宽度 */
  flex: 1; /* 响应式宽度 */
}

/* 英语键盘网格布局 */
.english-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
}

/* 英语键盘行布局 */
.english-grid .keyboard-row {
  display: flex;
  justify-content: center;
  gap: 6px;
  flex: 1;
}

/* 英语键盘第一行 - 10个按键 */
.english-grid .keyboard-row:nth-child(1) {
  justify-content: space-between;
}

/* 英语键盘第二行 - 9个按键，居中 */
.english-grid .keyboard-row:nth-child(2) {
  justify-content: center;
  padding: 0 20px;
}

/* 英语键盘第三行 - Shift + 7个字母 + Backspace */
.english-grid .keyboard-row:nth-child(3) {
  justify-content: space-between;
}

/* 英语键盘第四行 - 符号/123 + 空格 + = */
.english-grid .keyboard-row:nth-child(4) {
  display: flex;
  align-items: center;
  gap: 6px; /* 按键间距 */
}

/* 英语键盘第四行按键宽度分配 */
.english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(1) {
  flex: 1.5; /* 符号/123按键 - 30% */
  min-width: 100px;
}

.english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(2) {
  flex: 2.5; /* 空格键 - 50% */
  min-width: 160px;
}

.english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(3) {
  flex: 1; /* 等号按键 - 20% */
  min-width: 60px;
}

/* 通用宽按键样式（用于其他行） */
.keyboard-key.key-wide {
  flex: 0 0 auto;
  min-width: 80px;
  padding: 4px 12px;
}

/* Shift激活状态 */
.keyboard-key.shift-active {
  background: #d1d5db !important;
  color: #1f2937 !important;
}

/* 面板分隔符 */
.panel-separator {
  width: 2px;
  background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
  border-radius: 1px;
  flex-shrink: 0;
}

/* 符号滚动容器 */
.symbol-scroll-container {
  flex: 1; /* 占满剩余空间 */
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 0; /* 移除滚动条空间 */
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 滚动条 */
.symbol-scroll-container::-webkit-scrollbar {
  display: none;
}



/* 网格布局 */
.panel-grid {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 增大行间距，配合更大的按键 */
  height: 100%; /* 占满容器高度 */
}

/* 空占位符 */
.keyboard-key-placeholder {
  flex: 1;
  height: 32px; /* 与按键高度保持一致 */
  visibility: hidden;
}



/* 键盘行 */
.keyboard-row {
  display: flex;
  gap: 8px; /* 增大按键间距，提升视觉效果 */
  margin-bottom: 8px; /* 增大行间距，配合更大的按键 */
}

.keyboard-row:last-child {
  margin-bottom: 0;
}

/* 按键基础样式 - 增强封装性防止外部样式干扰 */
.keyboard-key {
  flex: 1;
  min-width: 0; /* 防止flex子元素溢出 */
  height: 38px; /* 增大按键高度，提升用户体验 */
  border: none;
  border-radius: 6px; /* 适中的圆角 */
  font-size: 13px; /* 适中的字体大小 */
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12); /* 适中的阴影 */
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  overflow: hidden;
  padding: 6px 12px; /* 增大内边距，提升按键舒适度 */
  box-sizing: border-box;
  background: #f0f0f0; /* 柔和的浅灰色，减少视觉疲劳 */
  color: #2d2d2d; /* 深灰色文字，在浅灰背景上有良好对比度 */
  outline: none;
  margin: 0;
}

/* 增强封装性 - 防止外部样式干扰 */
.mathquill-keyboard .keyboard-key {
  /* 防止Material-UI Button样式干扰 */
  text-transform: none !important;
  text-decoration: none !important;
  /* 重新应用我们的样式 - 移除all:revert避免过度重置 */
  flex: 1 !important;
  height: 38px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  margin: 0 !important;
  border: none !important;
  border-radius: 6px !important;
  background: #f0f0f0 !important; /* 柔和的浅灰色，减少视觉疲劳 */
  color: #2d2d2d !important; /* 深灰色文字，在浅灰背景上有良好对比度 */
  box-sizing: border-box !important;
}

/* 按键标签 */
.key-label {
  pointer-events: none;
  line-height: 1;
  z-index: 1;
  position: relative;
}

/* 按键标签 - 增强封装性 */
.mathquill-keyboard .key-label {
  /* 防止外部样式干扰 */
  margin: 0 !important;
  padding: 0 !important;
  text-align: center !important;
  display: inline-block !important;
}

/* 按键波纹效果 */
.keyboard-key::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  pointer-events: none;
}

.keyboard-key:active::before {
  width: 100%;
  height: 100%;
}

/* 按键按下状态 */
.keyboard-key:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* 白色数字键 - 调整为护眼的浅灰色 */
.keyboard-key.key-white {
  background: #f0f0f0; /* 柔和的浅灰色，减少刺眼感 */
  color: #2d2d2d; /* 深灰色文字，保持对比度 */
  border: 1px solid #d0d0d0;
}

.keyboard-key.key-white:hover {
  background: #e8e8e8; /* 悬停时稍微深一点 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.keyboard-key.key-white:active {
  background: #dcdcdc; /* 点击时更深一点 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

/* 灰色功能键 */
.keyboard-key.key-gray {
  background: #e5e7eb;
  color: #374151;
  border: 1px solid #d1d5db;
}

.keyboard-key.key-gray:hover {
  background: #d1d5db;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.keyboard-key.key-gray:active {
  background: #c4c8cc;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

/* 特殊按键样式 */
.keyboard-key[data-key-type="navigation"] {
  font-size: 18px;
}

.keyboard-key[data-key-type="power"] .key-label,
.keyboard-key[data-key-type="function"] .key-label {
  font-size: 14px;
}

.keyboard-key[data-key-type="mode"] {
  font-size: 14px;
  font-weight: 600;
}

/* 🔧 数学符号按键中的上下标对齐样式 */
.keyboard-key[data-key-type="power"] {
  position: relative;
}

.keyboard-key[data-key-type="power"] .key-label {
  position: relative;
  display: inline-block;
  min-width: 24px;
  min-height: 20px;
  line-height: 1.2;
}

/* 数学显示容器 */
.keyboard-key .math-display-container {
  position: relative;
  display: inline-flex; /* 改为inline-flex，提供更好的对齐控制 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: center; /* 水平居中对齐 */
  min-width: 16px; /* 减少最小宽度，使布局更紧凑 */
  min-height: 18px;
  vertical-align: middle;
}

/* 数学显示容器 - 增强封装性 */
.mathquill-keyboard .keyboard-key .math-display-container {
  /* 防止外部样式干扰 */
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

/* 上下标基础元素 */
.keyboard-key .math-base {
  display: inline-block;
  font-size: 14px;
  vertical-align: baseline;
  position: relative;
  margin-right: 10px; /* 再增加4px距离 */
}

/* 上标样式 */
.keyboard-key .math-sup {
  font-size: 9px !important;
  line-height: 1 !important;
  position: absolute !important;
  top: -0.3em !important; /* 调整上标位置，避免过高 */
  right: 4px !important; /* 再向右移动4px */
}

/* 下标样式 */
.keyboard-key .math-sub {
  font-size: 9px !important;
  line-height: 1 !important;
  position: absolute !important;
  bottom: -0.3em !important; /* 调整下标位置，避免过低 */
  right: 4px !important; /* 再向右移动4px */
}

/* 上标样式 - 增强封装性 */
.mathquill-keyboard .keyboard-key .math-sup {
  font-size: 9px !important;
  line-height: 1 !important;
  position: absolute !important;
  top: -0.3em !important; /* 调整上标位置，避免过高 */
  right: 4px !important; /* 再向右移动4px */
  margin: 0 !important;
  padding: 0 !important;
  vertical-align: baseline !important;
}

/* 下标样式 - 增强封装性 */
.mathquill-keyboard .keyboard-key .math-sub {
  font-size: 9px !important;
  line-height: 1 !important;
  position: absolute !important;
  bottom: -0.3em !important; /* 调整下标位置，避免过低 */
  right: 4px !important; /* 再向右移动4px */
  margin: 0 !important;
  padding: 0 !important;
  vertical-align: baseline !important;
}

/* 🔧 为不同情况提供更好的兼容性 */
/* 默认情况下为上下标留出充足空间 */
.keyboard-key .math-display-container .math-base {
  margin-right: 10px;
}

/* 🔧 微调上下标位置以确保完美对齐 */
.keyboard-key .math-sup {
  transform: translateX(0px) translateY(0px); /* 重置偏移，依赖position定位 */
}

.keyboard-key .math-sub {
  transform: translateX(0px) translateY(0px); /* 重置偏移，依赖position定位 */
}

/* 确保math-display类的按键正确显示 */
.keyboard-key.math-display {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: 'Times New Roman', serif !important;
}

/* 确保math-display类的按键正确显示 - 使用更具体的选择器避免冲突 */
.mathquill-keyboard .keyboard-key.math-display {
  /* 重置可能被全局样式影响的属性 */
  text-align: center !important;
  margin: 0 !important;
  padding: 6px 12px !important;
  box-sizing: border-box !important;
}



/* 关闭按钮 */
.keyboard-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.keyboard-close-btn:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
}

.keyboard-close-btn:active {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .keyboard-container {
    flex-direction: column;
    gap: 20px; /* 保持较大间距 */
  }

  .math-panel,
  .numeric-panel {
    flex: none;
    padding: 16px; /* 减少移动端内边距 */
  }

  .panel-separator {
    display: none;
  }

  .keyboard-key {
    height: 26px; /* 移动端进一步减少按键高度 */
    font-size: 11px;
  }

  .numeric-panel .keyboard-key {
    height: 30px; /* 移动端数字区按键高度 */
    font-size: 12px;
  }

  .mathquill-keyboard {
    padding: 12px; /* 减少内边距 */
    /* 保持键盘高度不变 */
  }

  /* 英语键盘移动端适配 */
  .english-keyboard-panel {
    padding: 12px;
  }

  .english-keyboard-panel .keyboard-key {
    height: 42px; /* 移动端英语键盘按键高度 */
    font-size: 14px;
    min-width: 28px; /* 移动端最小宽度 */
  }

  .english-grid .keyboard-row:nth-child(2) {
    padding: 0 10px;
  }

  /* 移动端英语键盘第四行按键宽度分配 */
  .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(1) {
    flex: 1.5; /* 符号/123按键 */
    min-width: 80px;
    font-size: 12px;
  }

  .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(2) {
    flex: 2.5; /* 空格键 */
    min-width: 120px;
  }

  .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(3) {
    flex: 1; /* 等号按键 */
    min-width: 45px;
  }

  .keyboard-key.key-wide {
    min-width: 60px;
    padding: 4px 8px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .keyboard-container {
    gap: 16px; /* 保持合理间距 */
  }

  .math-panel,
  .numeric-panel {
    padding: 12px; /* 小屏幕减少内边距 */
  }

  .keyboard-key {
    height: 24px; /* 小屏幕最小按键高度 */
    font-size: 10px;
  }

  .numeric-panel .keyboard-key {
    height: 26px; /* 小屏幕数字区按键高度 */
    font-size: 11px;
  }

  /* 英语键盘小屏幕适配 */
  .english-keyboard-panel .keyboard-key {
    height: 42px; /* 小屏幕英语键盘按键高度 */
    font-size: 12px;
    min-width: 24px; /* 小屏幕最小宽度 */
  }

  /* 小屏幕宽按键和空格键 */
  /* 小屏幕英语键盘第四行按键宽度分配 */
  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(1) {
    flex: 1.5; /* 符号/123按键 */
    min-width: 65px;
    font-size: 10px;
  }

  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(2) {
    flex: 2.5; /* 空格键 */
    min-width: 90px;
  }

  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(3) {
    flex: 1; /* 等号按键 */
    min-width: 35px;
  }

  .english-keyboard-panel .keyboard-key.key-wide {
    min-width: 45px;
    font-size: 10px;
  }

  .keyboard-row {
    gap: 3px; /* 小屏幕减少按键间距 */
  }

  .numeric-panel .keyboard-row {
    gap: 4px; /* 小屏幕数字区按键间距 */
  }

  .panel-grid {
    gap: 3px; /* 小屏幕减少行间距 */
  }

  .mathquill-keyboard {
    padding: 8px;
    /* 保持键盘高度不变 */
  }
}



/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .keyboard-key {
    height: 52px;
    font-size: 17px;
  }

  .english-keyboard-panel .keyboard-key {
    height: 42px; /* 平板端英语键盘按键高度 */
    font-size: 18px;
    min-width: 36px; /* 平板端最小宽度 */
  }

  /* 平板端英语键盘第四行按键宽度分配 */
  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(1) {
    flex: 1.5; /* 符号/123按键 */
    min-width: 110px;
  }

  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(2) {
    flex: 2.5; /* 空格键 */
    min-width: 180px;
  }

  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(3) {
    flex: 1; /* 等号按键 */
    min-width: 70px;
  }

  .english-keyboard-panel .keyboard-key.key-wide {
    min-width: 90px;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .keyboard-key {
    height: 56px;
    font-size: 18px;
  }

  .english-keyboard-panel .keyboard-key {
    height: 42px; /* 桌面端英语键盘按键高度 */
    font-size: 20px;
    min-width: 40px; /* 桌面端最小宽度 */
  }

  /* 桌面端英语键盘第四行按键宽度分配 */
  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(1) {
    flex: 1.5; /* 符号/123按键 */
    min-width: 120px;
  }

  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(2) {
    flex: 2.5; /* 空格键 */
    min-width: 200px;
  }

  .english-keyboard-panel .english-grid .keyboard-row:nth-child(4) .keyboard-key:nth-child(3) {
    flex: 1; /* 等号按键 */
    min-width: 80px;
  }

  .english-keyboard-panel .keyboard-key.key-wide {
    min-width: 100px;
  }
}

/* 超大屏幕优化 - 通过内边距控制内容区域 */
@media (min-width: 1200px) {
  .mathquill-keyboard {
    padding-left: calc(20px + (100vw - 1200px) / 8); /* 动态左内边距 */
    padding-right: calc(20px + (100vw - 1200px) / 8); /* 动态右内边距 */
  }
}

@media (min-width: 1600px) {
  .mathquill-keyboard {
    padding-left: calc(20px + 200px); /* 最大左内边距 */
    padding-right: calc(20px + 200px); /* 最大右内边距 */
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .mathquill-keyboard {
    background: #1f1f1f;
    border-top-color: #333;
  }
  
  .math-panel,
  .numeric-panel {
    background: #2a2a2a;
  }
  
  .panel-title {
    color: #ccc;
  }
  
  .panel-header {
    border-bottom-color: #444;
  }
  
  .panel-separator {
    background: linear-gradient(to bottom, transparent, #444, transparent);
  }
  
  .keyboard-key.key-white {
    background: #3a3a3a;
    color: #fff;
    border-color: #555;
  }
  
  .keyboard-key.key-white:hover {
    background: #4a4a4a;
  }
  
  .keyboard-key.key-gray {
    background: #555;
    color: #ccc;
    border-color: #666;
  }
  
  .keyboard-key.key-gray:hover {
    background: #666;
  }
  
  .keyboard-close-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
  }
  
  .keyboard-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .keyboard-key {
    border-width: 2px;
  }
  
  .keyboard-key.key-white {
    border-color: #000;
  }
  
  .keyboard-key.key-gray {
    border-color: #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .mathquill-keyboard {
    animation: none;
  }
  
  .keyboard-key {
    transition: none;
  }
  
  .keyboard-key:hover {
    transform: none;
  }
  
  .keyboard-close-btn {
    transition: none;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .keyboard-key:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .keyboard-key:active {
    transform: scale(0.95);
  }
}

/* 无障碍支持 */
.keyboard-key:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.keyboard-close-btn:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 键盘显示状态指示 - 已隐藏 */
.mathquill-keyboard::before {
  display: none;
}



/* 加载状态 */
.mathquill-keyboard.loading {
  opacity: 0.7;
  pointer-events: none;
}

.mathquill-keyboard.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  border: 2px solid #ccc;
  border-top-color: #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}


