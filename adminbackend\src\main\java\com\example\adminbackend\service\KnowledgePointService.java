package com.example.adminbackend.service;

import com.example.adminbackend.model.KnowledgePoint;
import com.example.adminbackend.dto.KnowledgePointDTO;
import com.example.adminbackend.dto.KnowledgePointHierarchyInfo;

import java.util.List;

/**
 * 知识点服务接口
 */
public interface KnowledgePointService {

    /**
     * 获取所有知识点
     */
    List<KnowledgePoint> getAllKnowledgePoints();

    /**
     * 根据ID获取知识点
     */
    KnowledgePoint getKnowledgePointById(Long id);

    /**
     * 根据章节ID获取所有知识点
     */
    List<KnowledgePoint> getKnowledgePointsByChapterId(Long chapterId);

    /**
     * 根据章节ID获取所有知识点（DTO格式）
     */
    List<KnowledgePointDTO> getKnowledgePointDTOsByChapterId(Long chapterId);

    /**
     * 根据知识点ID获取其层级信息（科目、版本、章节）
     */
    KnowledgePointHierarchyInfo getKnowledgePointHierarchy(Long knowledgePointId);

    /**
     * 根据章节ID和知识点名称获取知识点
     */
    KnowledgePoint getKnowledgePointByChapterIdAndName(Long chapterId, String name);

    /**
     * 根据科目版本ID获取所有知识点
     */
    List<KnowledgePoint> getKnowledgePointsBySubjectVersionId(Long subjectVersionId);

    /**
     * 根据科目ID获取所有知识点
     */
    List<KnowledgePoint> getKnowledgePointsBySubjectId(Long subjectId);

    /**
     * 创建知识点
     */
    KnowledgePoint createKnowledgePoint(KnowledgePoint knowledgePoint);

    /**
     * 更新知识点
     */
    KnowledgePoint updateKnowledgePoint(Long id, KnowledgePoint knowledgePoint);

    /**
     * 删除知识点（简单删除，不处理关联资源）
     */
    void deleteKnowledgePoint(Long id);

    /**
     * 级联删除知识点及其关联的视频和文件资源
     * @param id 知识点ID
     * @return 删除的文件数量
     */
    int deleteKnowledgePointCascade(Long id);

    /**
     * 检查章节下是否存在指定名称的知识点
     */
    boolean existsByChapterIdAndName(Long chapterId, String name);

    /**
     * 根据关键词搜索知识点
     */
    List<KnowledgePoint> searchKnowledgePoints(String keyword);

    /**
     * 获取章节下知识点的最大顺序索引
     */
    Integer getMaxOrderIndexByChapterId(Long chapterId);

    /**
     * 统计章节下的知识点数量
     */
    long countByChapterId(Long chapterId);

    /**
     * 统计科目版本下的知识点数量
     */
    long countBySubjectVersionId(Long subjectVersionId);

    /**
     * 统计科目下的知识点数量
     */
    long countBySubjectId(Long subjectId);

    /**
     * 获取所有知识点，返回DTO（避免懒加载问题）
     */
    List<KnowledgePointDTO> getAllKnowledgePointsAsDTO();
}
