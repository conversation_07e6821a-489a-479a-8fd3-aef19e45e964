import { AxiosError } from 'axios';

// 错误详情接口
export interface ErrorDetail {
  field?: string;
  rejectedValue?: any;
  message: string;
  code?: string;
  suggestion?: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  errors?: ErrorDetail[];
  timestamp: string;
  path?: string;
  requestId?: string;
}

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 处理后的错误信息
export interface ProcessedError {
  type: ErrorType;
  code: number;
  message: string;
  details?: ErrorDetail[];
  suggestion?: string;
  retryable: boolean;
  requestId?: string;
}

/**
 * 错误处理工具类
 */
export class ErrorHandler {
  
  /**
   * 处理API错误
   */
  static handleApiError(error: AxiosError): ProcessedError {
    // 网络错误
    if (!error.response) {
      return {
        type: ErrorType.NETWORK_ERROR,
        code: 0,
        message: '网络连接失败，请检查网络连接',
        suggestion: '请检查网络连接后重试',
        retryable: true
      };
    }

    const { status, data } = error.response;
    const apiResponse = data as ApiResponse;

    // 根据HTTP状态码分类错误
    switch (status) {
      case 400:
        return this.handleBadRequest(apiResponse);
      case 401:
        return this.handleUnauthorized(apiResponse);
      case 403:
        return this.handleForbidden(apiResponse);
      case 404:
        return this.handleNotFound(apiResponse);
      case 409:
        return this.handleConflict(apiResponse);
      case 413:
        return this.handlePayloadTooLarge(apiResponse);
      case 422:
        return this.handleUnprocessableEntity(apiResponse);
      case 429:
        return this.handleTooManyRequests(apiResponse);
      case 500:
      case 502:
      case 503:
      case 504:
        return this.handleServerError(apiResponse, status);
      default:
        return this.handleUnknownError(apiResponse, status);
    }
  }

  /**
   * 处理400错误
   */
  private static handleBadRequest(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.VALIDATION_ERROR,
      code: response.code || 400,
      message: response.message || '请求参数错误',
      details: response.errors,
      suggestion: this.getValidationSuggestion(response.errors),
      retryable: false,
      requestId: response.requestId
    };
  }

  /**
   * 处理401错误
   */
  private static handleUnauthorized(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.AUTHENTICATION_ERROR,
      code: response.code || 401,
      message: response.message || '身份验证失败',
      suggestion: '请重新登录',
      retryable: false,
      requestId: response.requestId
    };
  }

  /**
   * 处理403错误
   */
  private static handleForbidden(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.AUTHORIZATION_ERROR,
      code: response.code || 403,
      message: response.message || '没有权限访问此资源',
      suggestion: '请联系管理员获取相应权限',
      retryable: false,
      requestId: response.requestId
    };
  }

  /**
   * 处理404错误
   */
  private static handleNotFound(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.BUSINESS_ERROR,
      code: response.code || 404,
      message: response.message || '请求的资源不存在',
      suggestion: '请检查请求的资源是否正确',
      retryable: false,
      requestId: response.requestId
    };
  }

  /**
   * 处理409错误
   */
  private static handleConflict(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.BUSINESS_ERROR,
      code: response.code || 409,
      message: response.message || '数据冲突',
      suggestion: '请刷新页面后重试',
      retryable: true,
      requestId: response.requestId
    };
  }

  /**
   * 处理413错误
   */
  private static handlePayloadTooLarge(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.VALIDATION_ERROR,
      code: response.code || 413,
      message: response.message || '文件大小超出限制',
      suggestion: '请选择更小的文件',
      retryable: false,
      requestId: response.requestId
    };
  }

  /**
   * 处理422错误
   */
  private static handleUnprocessableEntity(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.VALIDATION_ERROR,
      code: response.code || 422,
      message: response.message || '数据验证失败',
      details: response.errors,
      suggestion: this.getValidationSuggestion(response.errors),
      retryable: false,
      requestId: response.requestId
    };
  }

  /**
   * 处理429错误
   */
  private static handleTooManyRequests(response: ApiResponse): ProcessedError {
    return {
      type: ErrorType.BUSINESS_ERROR,
      code: response.code || 429,
      message: response.message || '请求过于频繁',
      suggestion: '请稍后再试',
      retryable: true,
      requestId: response.requestId
    };
  }

  /**
   * 处理服务器错误
   */
  private static handleServerError(response: ApiResponse, status: number): ProcessedError {
    return {
      type: ErrorType.SERVER_ERROR,
      code: response.code || status,
      message: response.message || '服务器内部错误',
      suggestion: '请稍后重试，如果问题持续存在请联系技术支持',
      retryable: true,
      requestId: response.requestId
    };
  }

  /**
   * 处理未知错误
   */
  private static handleUnknownError(response: ApiResponse, status: number): ProcessedError {
    return {
      type: ErrorType.UNKNOWN_ERROR,
      code: response.code || status,
      message: response.message || '发生未知错误',
      suggestion: '请刷新页面重试',
      retryable: true,
      requestId: response.requestId
    };
  }

  /**
   * 获取验证错误建议
   */
  private static getValidationSuggestion(errors?: ErrorDetail[]): string {
    if (!errors || errors.length === 0) {
      return '请检查输入的数据格式';
    }

    const firstError = errors[0];
    if (firstError.suggestion) {
      return firstError.suggestion;
    }

    // 根据字段名提供通用建议
    if (firstError.field) {
      const field = firstError.field.toLowerCase();
      if (field.includes('phone')) {
        return '请输入正确的手机号格式';
      } else if (field.includes('email')) {
        return '请输入正确的邮箱格式';
      } else if (field.includes('password')) {
        return '请检查密码格式要求';
      }
    }

    return '请检查输入的数据格式';
  }

  /**
   * 格式化错误消息用于显示
   */
  static formatErrorMessage(error: ProcessedError): string {
    let message = error.message;
    
    if (error.details && error.details.length > 0) {
      const fieldErrors = error.details
        .filter(detail => detail.field)
        .map(detail => `${detail.field}: ${detail.message}`)
        .join('; ');
      
      if (fieldErrors) {
        message += ` (${fieldErrors})`;
      }
    }

    return message;
  }

  /**
   * 判断错误是否可重试
   */
  static isRetryable(error: ProcessedError): boolean {
    return error.retryable && [
      ErrorType.NETWORK_ERROR,
      ErrorType.SERVER_ERROR,
      ErrorType.BUSINESS_ERROR
    ].includes(error.type);
  }

  /**
   * 判断是否需要重新登录
   */
  static needsReauth(error: ProcessedError): boolean {
    return error.type === ErrorType.AUTHENTICATION_ERROR;
  }

  /**
   * 获取错误的严重程度
   */
  static getErrorSeverity(error: ProcessedError): 'error' | 'warning' | 'info' {
    switch (error.type) {
      case ErrorType.AUTHENTICATION_ERROR:
      case ErrorType.AUTHORIZATION_ERROR:
      case ErrorType.SERVER_ERROR:
        return 'error';
      case ErrorType.VALIDATION_ERROR:
      case ErrorType.BUSINESS_ERROR:
        return 'warning';
      case ErrorType.NETWORK_ERROR:
        return 'info';
      default:
        return 'error';
    }
  }
}
