package com.example.adminbackend.service.migration;

import com.example.adminbackend.dto.migration.MigrationResult;
import com.example.adminbackend.dto.migration.SourceQuestionData;

import java.util.List;

/**
 * 题目数据迁移服务接口
 */
public interface QuestionMigrationService {
    
    /**
     * 迁移题目数据
     * 
     * @param sourceData 源数据列表
     * @return 迁移结果
     */
    MigrationResult migrateQuestions(List<SourceQuestionData> sourceData);
    
    /**
     * 从JSON字符串迁移单个题目
     *
     * @param jsonData JSON格式的单个题目数据
     * @return 迁移结果
     */
    MigrationResult migrateQuestionFromJsonString(String jsonData);

    /**
     * 从题目对象迁移单个题目
     *
     * @param questionData 题目对象
     * @return 迁移结果
     */
    MigrationResult migrateQuestionFromObject(SourceQuestionData questionData);
    

    
    /**
     * 验证源数据格式
     * 
     * @param sourceData 源数据列表
     * @return 验证结果统计
     */
    MigrationResult validateSourceData(List<SourceQuestionData> sourceData);
    
    /**
     * 获取迁移进度
     * 
     * @return 当前迁移进度信息
     */
    MigrationResult getCurrentMigrationProgress();
    
    /**
     * 停止当前迁移任务
     */
    void stopMigration();
    
    /**
     * 清理迁移缓存
     */
    void clearMigrationCache();
}
