package com.example.adminbackend.repository;

import com.example.adminbackend.model.KnowledgePoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 知识点数据访问层
 */
@Repository
public interface KnowledgePointRepository extends JpaRepository<KnowledgePoint, Long> {

    /**
     * 根据章节ID查找所有知识点，按顺序排序
     */
    List<KnowledgePoint> findByChapterIdOrderByOrderIndexAsc(Long chapterId);

    /**
     * 根据章节ID查找所有启用的知识点，按顺序排序
     */
    List<KnowledgePoint> findByChapterIdAndEnabledTrueOrderByOrderIndexAsc(Long chapterId);

    /**
     * 根据章节ID查找所有知识点，预加载视频合集信息，按顺序排序
     */
    @Query("SELECT kp FROM KnowledgePoint kp LEFT JOIN FETCH kp.videoCollection WHERE kp.chapter.id = :chapterId ORDER BY kp.orderIndex ASC")
    List<KnowledgePoint> findByChapterIdWithVideoCollectionOrderByOrderIndexAsc(@Param("chapterId") Long chapterId);

    /**
     * 根据ID查找知识点，预加载章节、科目版本、科目信息
     */
    @Query("SELECT kp FROM KnowledgePoint kp " +
           "JOIN FETCH kp.chapter c " +
           "JOIN FETCH c.subjectVersion sv " +
           "JOIN FETCH sv.subject s " +
           "WHERE kp.id = :id")
    Optional<KnowledgePoint> findByIdWithHierarchy(@Param("id") Long id);

    /**
     * 根据章节ID和知识点名称查找知识点
     */
    Optional<KnowledgePoint> findByChapterIdAndName(Long chapterId, String name);

    /**
     * 根据章节ID和知识点名称查找所有匹配的知识点（用于处理重复数据）
     */
    List<KnowledgePoint> findAllByChapterIdAndName(Long chapterId, String name);

    /**
     * 根据章节ID和顺序索引查找知识点
     */
    Optional<KnowledgePoint> findByChapterIdAndOrderIndex(Long chapterId, Integer orderIndex);

    /**
     * 检查章节下是否存在指定名称的知识点
     */
    boolean existsByChapterIdAndName(Long chapterId, String name);

    /**
     * 检查章节下是否存在指定顺序的知识点
     */
    boolean existsByChapterIdAndOrderIndex(Long chapterId, Integer orderIndex);

    /**
     * 获取章节下知识点的最大顺序索引
     */
    @Query("SELECT MAX(kp.orderIndex) FROM KnowledgePoint kp WHERE kp.chapter.id = :chapterId")
    Integer findMaxOrderIndexByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 根据科目版本ID查找所有知识点
     */
    @Query("SELECT kp FROM KnowledgePoint kp JOIN kp.chapter c WHERE c.subjectVersion.id = :subjectVersionId ORDER BY c.orderIndex, kp.orderIndex")
    List<KnowledgePoint> findBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 根据科目ID查找所有知识点
     */
    @Query("SELECT kp FROM KnowledgePoint kp JOIN kp.chapter c JOIN c.subjectVersion sv WHERE sv.subject.id = :subjectId ORDER BY sv.id, c.orderIndex, kp.orderIndex")
    List<KnowledgePoint> findBySubjectId(@Param("subjectId") Long subjectId);

    /**
     * 根据知识点名称模糊查询
     */
    @Query("SELECT kp FROM KnowledgePoint kp WHERE kp.name LIKE %:keyword% ORDER BY kp.chapter.id, kp.orderIndex")
    List<KnowledgePoint> findByNameContaining(@Param("keyword") String keyword);

    /**
     * 统计章节下的知识点数量
     */
    long countByChapterId(Long chapterId);

    /**
     * 统计科目版本下的知识点数量
     */
    @Query("SELECT COUNT(kp) FROM KnowledgePoint kp JOIN kp.chapter c WHERE c.subjectVersion.id = :subjectVersionId")
    long countBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 统计科目下的知识点数量
     */
    @Query("SELECT COUNT(kp) FROM KnowledgePoint kp JOIN kp.chapter c JOIN c.subjectVersion sv WHERE sv.subject.id = :subjectId")
    long countBySubjectId(@Param("subjectId") Long subjectId);
}
