/**
 * MathLive输入管理器
 * 统一管理MathLive实例，提供数学公式输入功能
 * 
 * 设计原则：
 * 1. 实例管理：统一创建和销毁MathLive实例
 * 2. 配置管理：为不同科目提供预定义配置
 * 3. 事件处理：标准化的事件处理机制
 * 4. 生命周期：正确处理实例的创建、使用和销毁
 * 5. 键盘集成：与现有的MathQuillKeyboard组件集成
 */

import 'mathlive';

// 配置MathLive字体路径
if (typeof window !== 'undefined' && window.MathfieldElement) {
  // 设置字体路径为public目录中的路径
  window.MathfieldElement.fontsDirectory = '/fonts/';
}

/**
 * MathLive配置管理器
 * 为不同科目和使用场景提供预定义配置
 */
class ConfigManager {
  constructor() {
    this.configs = {
      // 数学科目配置
      math: {
        mathVirtualKeyboardPolicy: 'manual', // 禁用内置键盘
        smartMode: false, // 禁用智能模式
        smartFence: true,
        smartSuperscript: true,
        menuItems: [], // 禁用菜单
        placeholderSymbol: '▢', // 🔧 设置占位符符号
        inlineShortcuts: {
          'pi': '\\pi',
          'theta': '\\theta',
          'alpha': '\\alpha',
          'beta': '\\beta',
          'gamma': '\\gamma',
          'sqrt': '\\sqrt{#?}',
          'sum': '\\sum_{#?}^{#?}',
          'int': '\\int_{#?}^{#?}',
          'frac': '\\frac{#?}{#?}',
          'sin': '\\sin',
          'cos': '\\cos',
          'tan': '\\tan',
          'log': '\\log',
          'ln': '\\ln'
        }
      },

      // 物理科目配置
      physics: {
        mathVirtualKeyboardPolicy: 'manual',
        smartMode: false,
        smartFence: true,
        smartSuperscript: true,
        menuItems: [], // 禁用菜单
        inlineShortcuts: {
          'pi': '\\pi',
          'theta': '\\theta',
          'alpha': '\\alpha',
          'beta': '\\beta',
          'gamma': '\\gamma',
          'omega': '\\omega',
          'mu': '\\mu',
          'rho': '\\rho',
          'sigma': '\\sigma',
          'sqrt': '\\sqrt{#?}',
          'frac': '\\frac{#?}{#?}',
          'sin': '\\sin',
          'cos': '\\cos',
          'tan': '\\tan',
          'log': '\\log',
          'ln': '\\ln'
        }
      },

      // 化学科目配置
      chemistry: {
        mathVirtualKeyboardPolicy: 'manual',
        smartMode: false,
        smartFence: true,
        smartSuperscript: true,
        menuItems: [], // 禁用菜单
        inlineShortcuts: {
          'sqrt': '\\sqrt{#?}',
          'frac': '\\frac{#?}{#?}',
          'sum': '\\sum',
          'Delta': '\\Delta',
          'delta': '\\delta',
          'rightarrow': '\\rightarrow',
          'leftarrow': '\\leftarrow',
          'leftrightarrow': '\\leftrightarrow'
        }
      },

      // 文本科目配置
      text: {
        mathVirtualKeyboardPolicy: 'manual',
        smartMode: false,
        smartFence: false,
        smartSuperscript: false,
        menuItems: [], // 禁用菜单
        inlineShortcuts: {}
      }
    };
  }

  /**
   * 获取配置 - 统一使用数学配置
   * @param {string} subject - 科目类型
   * @param {boolean} isEmbedded - 是否为嵌入模式
   * @returns {Object} MathLive配置
   */
  getConfig(subject = 'math', isEmbedded = false) {
    // 获取基础配置
    const baseConfig = this.configs[subject] || this.configs.math;

    // 嵌入模式的特殊配置
    if (isEmbedded) {
      return {
        ...baseConfig,
        // 嵌入模式可能需要的特殊配置
      };
    }

    return { ...baseConfig };
  }
}

/**
 * MathLive输入管理器
 * 统一管理MathLive实例的创建、配置和销毁
 */
class MathLiveInputManager {
  constructor(options = {}) {
    this.instances = new Map(); // 存储MathLive实例
    this.configManager = new ConfigManager();
    this.options = {
      enableDebug: false,
      defaultSubject: 'math',
      ...options
    };

    // 防抖函数缓存
    this.debounceFunctions = new Map();
  }

  /**
   * 创建防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖后的函数
   */
  createDebounce(func, delay = 300) {
    const key = func.toString();
    if (this.debounceFunctions.has(key)) {
      return this.debounceFunctions.get(key);
    }

    let timeoutId;
    const debouncedFunc = (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };

    this.debounceFunctions.set(key, debouncedFunc);
    return debouncedFunc;
  }

  /**
   * 创建MathLive输入框
   * @param {HTMLElement} element - DOM元素
   * @param {Object} options - 选项
   * @returns {Object} MathLive实例和管理信息
   */
  createInput(element, options = {}) {
    const blankId = element.getAttribute('data-blank-id');
    console.log(`创建MathLive实例:`, { blankId, element, hasExisting: this.instances.has(element) });

    // 检查是否已存在实例，避免重复创建
    if (this.instances.has(element)) {
      const existingInstance = this.instances.get(element);
      console.log('MathLive实例已存在，复用现有实例:', {
        blankId,
        element: element.tagName,
        instancesCount: this.instances.size
      });
      return existingInstance;
    }

    const {
      subject = this.options.defaultSubject,
      isEmbedded = false,
      value = '',
      disabled = false,
      onChange = null,
      onFocus = null,
      onBlur = null
    } = options;

    // 获取配置
    const config = this.configManager.getConfig(subject, isEmbedded);

    if (this.options.enableDebug) {
      console.log('MathLive配置:', {
        subject,
        isEmbedded,
        config
      });
    }

    // 创建防抖的onChange处理器
    const debouncedOnChange = onChange ? this.createDebounce((latex, sourceElement) => {
      onChange(latex, sourceElement);
    }, 300) : null;

    // 确保元素是math-field
    if (element.tagName.toLowerCase() !== 'math-field') {
      console.error('MathLive只能应用于math-field元素');
      return null;
    }

    // 配置MathLive实例
    try {
      // 应用配置
      Object.assign(element, config);

      // 设置禁用状态
      if (disabled) {
        element.readOnly = true;
        element.style.pointerEvents = 'none';
        element.style.opacity = '0.6';
      } else {
        element.readOnly = false;
        element.style.pointerEvents = 'auto';
        element.style.opacity = '1';
      }

      // 设置初始值
      if (value) {
        element.value = value;
      }

      // 添加事件监听器
      const handleInput = (e) => {
        // 如果禁用，不处理输入事件
        if (disabled) return;

        // 🔧 使用 getValue('latex-expanded') 获得统一且健壮的LaTeX输出
        const rawLatex = e.target.getValue ?
          e.target.getValue('latex-expanded') :
          e.target.value;

        // 🔧 对LaTeX进行后处理
        const latex = this.postProcessLatex(rawLatex);
        const blankId = e.target.getAttribute('data-blank-id');
        console.log(`MathLive input事件:`, { blankId, rawLatex, latex, element: e.target });

        if (debouncedOnChange) {
          debouncedOnChange(latex, e.target); // 传递源元素
        }
      };

      const handleFocus = (e) => {
        // 如果禁用，不处理焦点事件
        if (disabled) {
          e.target.blur(); // 立即失去焦点
          return;
        }

        console.log('MathLive输入框获得焦点');

        // 当输入框获得焦点时，自动显示键盘
        const keyboardEvent = new CustomEvent('showMathKeyboard', {
          detail: { mathField: e.target, element }
        });
        console.log('发送showMathKeyboard事件:', keyboardEvent.detail);
        document.dispatchEvent(keyboardEvent);

        if (onFocus) {
          onFocus(e.target); // 传递元素而不是事件对象
        }
      };

      const handleBlur = (e) => {
        if (this.options.enableDebug) {
          console.log('MathLive输入框失去焦点');
        }
        if (onBlur) {
          onBlur(e);
        }
      };

      const handleClick = (e) => {
        // 如果禁用，不处理点击事件
        if (disabled) {
          e.preventDefault();
          e.stopPropagation();
          return;
        }

        // 确保输入框获得焦点
        try {
          e.target.focus();
        } catch (error) {
          console.error('MathLive focus()调用失败:', error);
        }

        // 触发自定义键盘显示事件
        const keyboardEvent = new CustomEvent('showMathKeyboard', {
          detail: { mathField: e.target, element }
        });
        document.dispatchEvent(keyboardEvent);
      };

      // 添加事件监听器
      element.addEventListener('input', handleInput);
      element.addEventListener('focus', handleFocus);
      element.addEventListener('blur', handleBlur);
      element.addEventListener('click', handleClick);

      // 保存实例信息，包含事件处理函数引用以便清理
      const instanceInfo = {
        instance: element,
        element: element,
        subject: subject,
        isEmbedded: isEmbedded,
        config: config,
        createdAt: Date.now(),
        handleInput: handleInput,
        handleFocus: handleFocus,
        handleBlur: handleBlur,
        handleClick: handleClick
      };

      this.instances.set(element, instanceInfo);

      if (this.options.enableDebug) {
        console.log('MathLive实例创建成功:', {
          subject,
          isEmbedded,
          element: element.tagName,
          instancesCount: this.instances.size
        });
      }

      return instanceInfo;

    } catch (error) {
      console.error('MathLive实例创建失败:', error);
      return null;
    }
  }

  /**
   * 后处理LaTeX代码，确保正确的括号格式
   * @param {string} latex - 原始LaTeX字符串
   * @returns {string} 处理后的LaTeX字符串
   */
  postProcessLatex(latex) {
    if (!latex || typeof latex !== 'string') return latex;

    let processed = latex;

    // 修复分数格式：\frac43 -> \frac{4}{3}
    // 处理两个连续的非括号字符
    processed = processed.replace(/\\frac([^{\\])([^{\\])/g, '\\frac{$1}{$2}');

    // 处理单个字符后跟数字的情况
    processed = processed.replace(/\\frac([^{\\])(\d+)/g, '\\frac{$1}{$2}');
    processed = processed.replace(/\\frac(\d+)([^{\\])/g, '\\frac{$1}{$2}');

    // 修复上标格式：确保多字符上标有括号
    processed = processed.replace(/\^([a-zA-Z0-9]{2,})/g, '^{$1}');
    processed = processed.replace(/\^([^{\\])([a-zA-Z0-9]+)/g, '^{$1$2}');

    // 修复下标格式：确保多字符下标有括号
    processed = processed.replace(/_([a-zA-Z0-9]{2,})/g, '_{$1}');
    processed = processed.replace(/_([^{\\])([a-zA-Z0-9]+)/g, '_{$1$2}');

    // 修复根号格式：\sqrt2 -> \sqrt{2}
    processed = processed.replace(/\\sqrt([^{\\][^\\]*?)(\s|$|\\)/g, '\\sqrt{$1}$2');

    // 修复其他常见函数的参数格式
    const functions = ['sin', 'cos', 'tan', 'log', 'ln', 'exp', 'lim', 'max', 'min'];
    functions.forEach(func => {
      const regex = new RegExp(`\\\\${func}([^{\\s\\\\][\\w\\d]*)`, 'g');
      processed = processed.replace(regex, `\\${func}{$1}`);
    });

    return processed;
  }

  /**
   * 获取MathLive实例的LaTeX值
   * @param {HTMLElement} element - DOM元素
   * @returns {string} LaTeX字符串
   */
  getValue(element) {
    const instanceInfo = this.instances.get(element);
    if (instanceInfo && instanceInfo.instance) {
      // 🔧 使用 getValue('latex-expanded') 获得统一且健壮的LaTeX输出
      const rawLatex = instanceInfo.instance.getValue ?
        instanceInfo.instance.getValue('latex-expanded') :
        (instanceInfo.instance.value || '');

      // 🔧 对获取的LaTeX进行后处理
      return this.postProcessLatex(rawLatex);
    }
    return '';
  }

  /**
   * 设置MathLive实例的LaTeX值
   * @param {HTMLElement} element - DOM元素
   * @param {string} value - LaTeX字符串
   */
  setValue(element, value) {
    const instanceInfo = this.instances.get(element);
    if (instanceInfo && instanceInfo.instance) {
      instanceInfo.instance.value = value || '';
    }
  }

  /**
   * 销毁MathLive实例
   * @param {HTMLElement} element - DOM元素
   */
  destroyInput(element) {
    const instanceInfo = this.instances.get(element);
    if (!instanceInfo) {
      return;
    }

    try {
      // 移除事件监听器
      const { handleInput, handleFocus, handleBlur, handleClick } = instanceInfo;
      element.removeEventListener('input', handleInput);
      element.removeEventListener('focus', handleFocus);
      element.removeEventListener('blur', handleBlur);
      element.removeEventListener('click', handleClick);

      // 从实例映射中移除
      this.instances.delete(element);

      if (this.options.enableDebug) {
        console.log('MathLive实例已销毁:', {
          element: element.tagName,
          instancesCount: this.instances.size
        });
      }
    } catch (error) {
      console.error('销毁MathLive实例时出错:', error);
    }
  }

  /**
   * 销毁所有实例
   */
  destroyAll() {
    const elements = Array.from(this.instances.keys());
    elements.forEach(element => this.destroyInput(element));
    
    // 清理防抖函数缓存
    this.debounceFunctions.clear();

    if (this.options.enableDebug) {
      console.log('所有MathLive实例已销毁');
    }
  }

  /**
   * 获取实例统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      totalInstances: this.instances.size,
      instances: Array.from(this.instances.values()).map(info => ({
        subject: info.subject,
        isEmbedded: info.isEmbedded,
        createdAt: info.createdAt,
        element: info.element.tagName
      }))
    };
  }
}

// 创建全局实例
export const mathLiveInputManager = new MathLiveInputManager({
  enableDebug: process.env.NODE_ENV === 'development',
  defaultSubject: 'math'
});

// 导出类供其他地方使用
export { MathLiveInputManager, ConfigManager };
