import React from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Container,
  Avatar,
  Menu,
  MenuItem,
  IconButton,
  Divider,
} from '@mui/material';
import {
  AccountCircle,
  ExitToApp,
  Dashboard,
  People,
  SupervisorAccount,
  Quiz,
  CloudSync,
  Settings,
  School,
  LibraryBooks,
  MenuBook,
  Psychology,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { getRoleLabel } from '../utils/roleUtils';
import { useNavigate, useLocation } from 'react-router-dom';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout, isSuperAdmin, isAdmin, isSupervisor } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    logout();
  };

  // 根据用户角色显示不同的菜单
  const getMenuItems = () => {
    const items = [
      { path: '/dashboard', label: '仪表板', icon: <Dashboard /> },
    ];

    if (isSuperAdmin) {
      // 超级管理员可以看到所有用户管理
      items.push({ path: '/users', label: '用户管理', icon: <People /> });
    } else if (isAdmin) {
      // 普通管理员只能管理督学
      items.push({ path: '/supervisors', label: '督学管理', icon: <SupervisorAccount /> });
    }

    // 管理员及以上可以管理题库
    if (isAdmin || isSuperAdmin) {
      items.push({ path: '/questions', label: '题库管理', icon: <Quiz /> });
    }

    // 管理员及以上可以管理课程体系
    if (isAdmin || isSuperAdmin) {
      items.push({ path: '/subjects', label: '学科管理', icon: <School /> });
      items.push({ path: '/subject-versions', label: '学科版本管理', icon: <LibraryBooks /> });
      items.push({ path: '/chapters', label: '章节管理', icon: <MenuBook /> });
      items.push({ path: '/knowledge-points', label: '知识点管理', icon: <Psychology /> });
    }

    // 督学、管理员及以上可以管理题目数量配置
    if (isSupervisor || isAdmin || isSuperAdmin) {
      items.push({ path: '/practice-config', label: '题目数量管理', icon: <Settings /> });
    }

    // 超级管理员可以进行数据迁移
    if (isSuperAdmin) {
      items.push({ path: '/migration', label: '数据迁移', icon: <CloudSync /> });
    }

    return items;
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <AppBar position="static" elevation={0} sx={{ bgcolor: 'primary.main' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            智能学习系统管理端
          </Typography>
          
          {/* 导航菜单 */}
          <Box sx={{ display: 'flex', gap: 2, mr: 2 }}>
            {getMenuItems().map((item) => (
              <Button
                key={item.path}
                color="inherit"
                startIcon={item.icon}
                onClick={() => navigate(item.path)}
                sx={{
                  bgcolor: location.pathname === item.path ? 'rgba(255,255,255,0.1)' : 'transparent',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                {item.label}
              </Button>
            ))}
          </Box>

          {/* 用户菜单 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ color: 'inherit' }}>
              {user?.fullName || user?.username}
            </Typography>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="inherit"
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'rgba(255,255,255,0.2)' }}>
                <AccountCircle />
              </Avatar>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem disabled>
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    {user?.fullName || user?.username}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {user?.role ? getRoleLabel(user.role) : '未知角色'}
                  </Typography>
                </Box>
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ExitToApp sx={{ mr: 1 }} />
                退出登录
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      {/* 主要内容区域 */}
      <Container maxWidth="xl" sx={{ mt: 3, mb: 3, flexGrow: 1 }}>
        {children}
      </Container>
    </Box>
  );
};

export default Layout;
