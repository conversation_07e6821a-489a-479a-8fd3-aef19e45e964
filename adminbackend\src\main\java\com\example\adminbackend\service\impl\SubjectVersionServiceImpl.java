package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.SubjectVersion;
import com.example.adminbackend.model.Chapter;
import com.example.adminbackend.dto.SubjectVersionDTO;
import com.example.adminbackend.repository.SubjectVersionRepository;
import com.example.adminbackend.repository.ChapterRepository;
import com.example.adminbackend.repository.AgentCardSubjectVersionRepository;
import com.example.adminbackend.repository.StudentSubjectTrialRepository;
import com.example.adminbackend.repository.StudentCourseContentRepository;
import com.example.adminbackend.service.SubjectVersionService;
import com.example.adminbackend.service.ChapterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 科目版本服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SubjectVersionServiceImpl implements SubjectVersionService {

    private final SubjectVersionRepository subjectVersionRepository;
    private final ChapterRepository chapterRepository;
    private final ChapterService chapterService;
    private final AgentCardSubjectVersionRepository agentCardSubjectVersionRepository;
    private final StudentSubjectTrialRepository studentSubjectTrialRepository;
    private final StudentCourseContentRepository studentCourseContentRepository;

    @Override
    public List<SubjectVersion> getAllSubjectVersions() {
        log.info("获取所有科目版本");
        return subjectVersionRepository.findAll();
    }

    @Override
    public SubjectVersion getSubjectVersionById(Long id) {
        log.info("根据ID获取科目版本: {}", id);
        return subjectVersionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("科目版本不存在: " + id));
    }

    @Override
    public List<SubjectVersion> getVersionsBySubjectId(Long subjectId) {
        log.info("根据科目ID获取版本: {}", subjectId);
        return subjectVersionRepository.findBySubjectIdOrderByCreatedAtAsc(subjectId);
    }

    @Override
    public SubjectVersion getVersionBySubjectIdAndName(Long subjectId, String name) {
        log.info("根据科目ID和名称获取版本: subjectId={}, name={}", subjectId, name);
        // 使用findAll方法避免重复数据异常
        List<SubjectVersion> results = subjectVersionRepository.findAll().stream()
            .filter(v -> v.getSubject().getId().equals(subjectId) && v.getName().equals(name))
            .collect(java.util.stream.Collectors.toList());
        if (results.isEmpty()) {
            throw new RuntimeException("科目版本不存在: " + name);
        }
        if (results.size() > 1) {
            log.warn("发现重复的科目版本记录: subjectId={}, name={}, 数量={}, 使用第一个", subjectId, name, results.size());
        }
        return results.get(0);
    }

    @Override
    public List<SubjectVersion> getVersionsBySchoolLevel(String schoolLevel) {
        log.info("根据学校级别获取版本: {}", schoolLevel);
        return subjectVersionRepository.findBySchoolLevel(schoolLevel);
    }

    @Override
    public List<SubjectVersion> getVersionsBySubjectIdAndSchoolLevel(Long subjectId, String schoolLevel) {
        log.info("根据科目ID和学校级别获取版本: subjectId={}, schoolLevel={}", subjectId, schoolLevel);
        return subjectVersionRepository.findBySubjectIdAndSchoolLevel(subjectId, schoolLevel);
    }

    @Override
    @Transactional
    public SubjectVersion createSubjectVersion(SubjectVersion subjectVersion) {
        log.info("创建科目版本: {}", subjectVersion.getName());
        
        // 检查同一科目下名称是否已存在
        if (subjectVersionRepository.existsBySubjectIdAndName(
                subjectVersion.getSubject().getId(), subjectVersion.getName())) {
            throw new RuntimeException("科目版本名称已存在: " + subjectVersion.getName());
        }
        
        return subjectVersionRepository.save(subjectVersion);
    }

    @Override
    @Transactional
    public SubjectVersion updateSubjectVersion(Long id, SubjectVersion subjectVersion) {
        log.info("更新科目版本: {}", id);
        
        SubjectVersion existingVersion = getSubjectVersionById(id);
        
        // 检查名称是否与同一科目下其他版本冲突
        if (!existingVersion.getName().equals(subjectVersion.getName()) && 
            subjectVersionRepository.existsBySubjectIdAndName(
                    existingVersion.getSubject().getId(), subjectVersion.getName())) {
            throw new RuntimeException("科目版本名称已存在: " + subjectVersion.getName());
        }
        
        existingVersion.setName(subjectVersion.getName());
        existingVersion.setDescription(subjectVersion.getDescription());
        existingVersion.setSchoolLevel(subjectVersion.getSchoolLevel());
        
        return subjectVersionRepository.save(existingVersion);
    }

    @Override
    @Transactional
    public int deleteSubjectVersion(Long id) {
        log.info("开始删除科目版本: {}", id);

        SubjectVersion subjectVersion = getSubjectVersionById(id);
        int totalDeletedFiles = 0;

        // 1. 先删除外键关联记录
        log.info("开始清理科目版本 {} 的外键关联记录", id);

        // 删除代理卡学科版本关联记录
        long agentCardCount = agentCardSubjectVersionRepository.countBySubjectVersionId(id);
        if (agentCardCount > 0) {
            log.info("删除 {} 条代理卡学科版本关联记录", agentCardCount);
            agentCardSubjectVersionRepository.deleteBySubjectVersionId(id);
        }

        // 删除学生学科试用记录
        long trialCount = studentSubjectTrialRepository.countBySubjectVersionId(id);
        if (trialCount > 0) {
            log.info("删除 {} 条学生学科试用记录", trialCount);
            studentSubjectTrialRepository.deleteBySubjectVersionId(id);
        }

        // 删除学生课程内容记录
        long courseContentCount = studentCourseContentRepository.countBySubjectVersionId(id);
        if (courseContentCount > 0) {
            log.info("删除 {} 条学生课程内容记录", courseContentCount);
            studentCourseContentRepository.deleteBySubjectVersionId(id);
        }

        // 2. 检查是否有关联的章节
        List<Chapter> chapters = chapterRepository.findBySubjectVersionIdOrderByOrderIndexAsc(id);
        if (!chapters.isEmpty()) {
            log.info("科目版本 {} 存在 {} 个关联章节，开始级联删除", id, chapters.size());

            // 级联删除所有章节及其知识点
            for (Chapter chapter : chapters) {
                int deletedFiles = chapterService.deleteChapterCascade(chapter.getId());
                totalDeletedFiles += deletedFiles;
            }
        }

        // 3. 删除科目版本
        subjectVersionRepository.delete(subjectVersion);
        log.info("科目版本删除完成: {}, 删除文件数: {}", id, totalDeletedFiles);

        return totalDeletedFiles;
    }





    @Override
    public boolean existsBySubjectIdAndName(Long subjectId, String name) {
        return subjectVersionRepository.existsBySubjectIdAndName(subjectId, name);
    }

    @Override
    public List<SubjectVersion> getSubjectVersionsWithChapters() {
        log.info("获取有章节的科目版本列表");
        return subjectVersionRepository.findSubjectVersionsWithChapters();
    }

    @Override
    public List<SubjectVersion> getVersionsBySubjectName(String subjectName) {
        log.info("根据科目名称获取版本: {}", subjectName);
        return subjectVersionRepository.findBySubjectName(subjectName);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubjectVersionDTO> getSubjectVersionsWithChaptersAsDTO() {
        log.info("获取有章节的科目版本列表（DTO格式）");
        List<SubjectVersion> versions = subjectVersionRepository.findSubjectVersionsWithChapters();

        return versions.stream().map(version -> {
            SubjectVersionDTO dto = new SubjectVersionDTO();
            dto.setId(version.getId());
            dto.setName(version.getName());
            dto.setDescription(version.getDescription());
            dto.setSchoolLevel(version.getSchoolLevel());
            dto.setCreatedAt(version.getCreatedAt());
            dto.setUpdatedAt(version.getUpdatedAt());
            // 安全访问subject关联，避免懒加载异常
            if (version.getSubject() != null) {
                dto.setSubjectId(version.getSubject().getId());
            }
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubjectVersionDTO> getVersionsBySubjectIdAsDTO(Long subjectId) {
        log.info("根据科目ID获取版本（DTO格式）: {}", subjectId);
        List<SubjectVersion> versions = subjectVersionRepository.findBySubjectIdOrderByCreatedAtAsc(subjectId);

        return versions.stream().map(version -> {
            SubjectVersionDTO dto = new SubjectVersionDTO();
            dto.setId(version.getId());
            dto.setName(version.getName());
            dto.setDescription(version.getDescription());
            dto.setSchoolLevel(version.getSchoolLevel());
            dto.setCreatedAt(version.getCreatedAt());
            dto.setUpdatedAt(version.getUpdatedAt());
            dto.setSubjectId(subjectId); // 直接使用传入的subjectId，避免懒加载
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public SubjectVersionDTO getSubjectVersionByIdAsDTO(Long id) {
        log.info("根据ID获取科目版本（DTO格式）: {}", id);
        SubjectVersion version = subjectVersionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("科目版本不存在: " + id));

        SubjectVersionDTO dto = new SubjectVersionDTO();
        dto.setId(version.getId());
        dto.setName(version.getName());
        dto.setDescription(version.getDescription());
        dto.setSchoolLevel(version.getSchoolLevel());
        dto.setCreatedAt(version.getCreatedAt());
        dto.setUpdatedAt(version.getUpdatedAt());
        // 安全访问subject关联，避免懒加载异常
        if (version.getSubject() != null) {
            dto.setSubjectId(version.getSubject().getId());
        }
        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubjectVersionDTO> getAllSubjectVersionsAsDTO() {
        log.info("获取所有科目版本（DTO格式）");
        List<SubjectVersion> versions = getAllSubjectVersions();
        return versions.stream().map(version -> {
            SubjectVersionDTO dto = new SubjectVersionDTO();
            dto.setId(version.getId());
            dto.setName(version.getName());
            dto.setDescription(version.getDescription());
            dto.setSchoolLevel(version.getSchoolLevel());
            dto.setCreatedAt(version.getCreatedAt());
            dto.setUpdatedAt(version.getUpdatedAt());
            // 安全访问subject关联，避免懒加载异常
            if (version.getSubject() != null) {
                dto.setSubjectId(version.getSubject().getId());
                dto.setSubjectName(version.getSubject().getName());
            }
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }
}
