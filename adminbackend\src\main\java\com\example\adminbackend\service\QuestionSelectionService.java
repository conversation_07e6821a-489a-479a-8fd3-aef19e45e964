package com.example.adminbackend.service;

import com.example.adminbackend.model.Question;
import com.example.adminbackend.model.PracticeConfig;

import java.util.List;
import java.util.Map;

/**
 * 题目选择服务接口
 * 负责根据配置智能选择题目
 */
public interface QuestionSelectionService {

    // ==================== 核心抽题方法 ====================

    /**
     * 为知识点练习选择题目
     * @param knowledgePointId 知识点ID
     * @param studentId 学生ID（可选，用于错题优先策略）
     * @return 选择的题目列表
     */
    List<Question> selectQuestionsForKnowledgePointPractice(Long knowledgePointId, Long studentId);

    /**
     * 为章节测试选择题目
     * @param chapterId 章节ID
     * @param studentId 学生ID（可选，用于错题优先策略）
     * @return 选择的题目列表
     */
    List<Question> selectQuestionsForChapterTest(Long chapterId, Long studentId);

    /**
     * 根据指定策略选择题目
     * @param questions 候选题目列表
     * @param count 需要选择的题目数量
     * @param strategy 选择策略
     * @param studentId 学生ID（可选）
     * @return 选择的题目列表
     */
    List<Question> selectQuestionsByStrategy(
            List<Question> questions, 
            int count, 
            PracticeConfig.SelectionStrategy strategy, 
            Long studentId);

    // ==================== 配置获取方法 ====================

    /**
     * 获取知识点的有效练习题目数量配置
     * @param knowledgePointId 知识点ID
     * @return 题目数量，如果没有配置则返回默认值
     */
    Integer getEffectivePracticeCount(Long knowledgePointId);

    /**
     * 计算章节测试中每个知识点应该抽取的题目数量
     * @param knowledgePointId 知识点ID
     * @param totalQuestions 该知识点的总题目数量
     * @return 应该抽取的题目数量
     */
    Integer calculateChapterTestQuestionCount(Long knowledgePointId, Integer totalQuestions);

    /**
     * 获取知识点的有效抽题策略
     * @param knowledgePointId 知识点ID
     * @return 抽题策略
     */
    PracticeConfig.SelectionStrategy getEffectiveSelectionStrategy(Long knowledgePointId);

    // ==================== 章节测试相关 ====================

    /**
     * 获取章节测试的题目分配方案
     * @param chapterId 章节ID
     * @return Map<知识点ID, 题目数量>
     */
    Map<Long, Integer> getChapterTestQuestionAllocation(Long chapterId);

    /**
     * 计算章节测试总题目数量
     * @param chapterId 章节ID
     * @return 总题目数量
     */
    Integer calculateChapterTestTotalCount(Long chapterId);

    // ==================== 抽题策略实现 ====================

    /**
     * 随机选择题目
     * @param questions 候选题目列表
     * @param count 需要选择的数量
     * @return 选择的题目列表
     */
    List<Question> randomSelection(List<Question> questions, int count);

    /**
     * 难度均衡选择题目
     * @param questions 候选题目列表
     * @param count 需要选择的数量
     * @return 选择的题目列表
     */
    List<Question> difficultyBalancedSelection(List<Question> questions, int count);

    /**
     * 错题优先选择题目
     * @param questions 候选题目列表
     * @param count 需要选择的数量
     * @param studentId 学生ID
     * @return 选择的题目列表
     */
    List<Question> errorPrioritySelection(List<Question> questions, int count, Long studentId);

    /**
     * 题型均衡选择题目
     * @param questions 候选题目列表
     * @param count 需要选择的数量
     * @return 选择的题目列表
     */
    List<Question> typeBalancedSelection(List<Question> questions, int count);

    // ==================== 辅助方法 ====================

    /**
     * 获取学生的错题ID集合
     * @param studentId 学生ID
     * @param knowledgePointId 知识点ID（可选，限制范围）
     * @return 错题ID集合
     */
    List<Long> getStudentWrongQuestionIds(Long studentId, Long knowledgePointId);

    /**
     * 验证题目选择结果
     * @param selectedQuestions 选择的题目列表
     * @param expectedCount 期望的题目数量
     * @return 是否有效
     */
    boolean validateSelectionResult(List<Question> selectedQuestions, int expectedCount);

    /**
     * 获取题目选择统计信息
     * @param knowledgePointId 知识点ID
     * @return 统计信息
     */
    Map<String, Object> getSelectionStatistics(Long knowledgePointId);
}
