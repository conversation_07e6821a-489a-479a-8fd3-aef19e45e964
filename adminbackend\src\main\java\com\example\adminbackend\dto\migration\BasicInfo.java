package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.Objects;

/**
 * 基础层级信息DTO
 * 包含学科、版本、章节、知识点信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicInfo {
    
    /**
     * 学科信息
     */
    private SourceSubject subject;
    
    /**
     * 版本信息
     */
    private SourceBookVersion bookversion;
    
    /**
     * 章节信息
     */
    private SourceChapter chapter;
    
    /**
     * 知识点列表
     */
    private List<SourceKnowledgePoint> knowledgePoints;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BasicInfo basicInfo = (BasicInfo) o;
        return Objects.equals(subject, o) &&
               Objects.equals(bookversion, ((BasicInfo) o).bookversion) &&
               Objects.equals(chapter, ((BasicInfo) o).chapter) &&
               Objects.equals(knowledgePoints, ((BasicInfo) o).knowledgePoints);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(subject, bookversion, chapter, knowledgePoints);
    }
}
