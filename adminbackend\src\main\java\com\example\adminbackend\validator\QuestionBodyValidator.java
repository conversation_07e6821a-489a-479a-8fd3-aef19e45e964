package com.example.adminbackend.validator;

import com.example.adminbackend.model.Question;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 题目Body字段完整验证器
 * 严格按照《Questions表Body字段JSON数据格式规范》执行验证
 */
@Component
@Slf4j
public class QuestionBodyValidator {

    // HTML标签基础验证正则
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
    
    // LaTeX数学公式验证正则
    private static final Pattern LATEX_PATTERN = Pattern.compile("\\\\\\([^\\)]+\\\\\\)|\\\\\\[[^\\]]+\\\\\\]");
    
    // 选择题答案字母验证正则
    private static final Pattern CHOICE_ANSWER_PATTERN = Pattern.compile("^[A-Z]$");

    /**
     * 验证题目Body的完整性
     * @param questionBody 题目Body数据
     * @return 验证结果
     */
    public ValidationResult validateQuestionBody(Map<String, Object> questionBody) {
        ValidationResult result = new ValidationResult();
        
        try {
            // 1. 验证所有必需字段
            validateRequiredFields(questionBody, result);
            if (!result.isValid()) {
                return result;
            }
            
            // 2. 验证字段类型和格式
            validateFieldTypes(questionBody, result);
            if (!result.isValid()) {
                return result;
            }
            
            // 3. 验证题型特定字段
            validateQuestionTypeSpecific(questionBody, result);
            if (!result.isValid()) {
                return result;
            }
            
            // 4. 验证答案格式
            validateAnswerFormat(questionBody, result);
            if (!result.isValid()) {
                return result;
            }
            
            // 5. 验证HTML内容安全性
            validateHtmlContent(questionBody, result);
            if (!result.isValid()) {
                return result;
            }
            
            result.setValid(true);
            result.setMessage("验证通过");
            
        } catch (Exception e) {
            log.error("验证题目Body时发生异常", e);
            result.setValid(false);
            result.setMessage("验证过程中发生异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证所有必需字段
     */
    private void validateRequiredFields(Map<String, Object> questionBody, ValidationResult result) {
        String[] requiredFields = {"type", "id", "subject", "difficulty", "tags", "content", "answer", "explanation"};
        
        for (String field : requiredFields) {
            if (!questionBody.containsKey(field)) {
                result.addError(new ValidationError(
                    field, 
                    "MISSING_FIELD", 
                    null, 
                    "必需字段", 
                    "缺少必需字段: " + field,
                    false
                ));
                return;
            }
            
            Object value = questionBody.get(field);
            if (value == null) {
                result.addError(new ValidationError(
                    field, 
                    "NULL_VALUE", 
                    "null", 
                    "非空值", 
                    "字段不能为空: " + field,
                    false
                ));
                return;
            }
        }
    }

    /**
     * 验证字段类型和格式
     */
    private void validateFieldTypes(Map<String, Object> questionBody, ValidationResult result) {
        // 验证type字段
        String type = (String) questionBody.get("type");
        try {
            Question.QuestionType.valueOf(type);
        } catch (IllegalArgumentException e) {
            result.addError(new ValidationError(
                "type", 
                "INVALID_ENUM", 
                type, 
                "有效的题目类型枚举值", 
                "无效的题目类型: " + type,
                false
            ));
            return;
        }
        
        // 验证subject字段
        String subject = (String) questionBody.get("subject");
        try {
            Question.Subject.valueOf(subject);
        } catch (IllegalArgumentException e) {
            result.addError(new ValidationError(
                "subject", 
                "INVALID_ENUM", 
                subject, 
                "有效的科目枚举值(ENGLISH/MATH/PHYSICS/CHEMISTRY)", 
                "无效的科目: " + subject,
                false
            ));
            return;
        }
        
        // 验证difficulty字段
        String difficulty = (String) questionBody.get("difficulty");
        if (!Arrays.asList("EASY", "MEDIUM", "HARD").contains(difficulty)) {
            result.addError(new ValidationError(
                "difficulty", 
                "INVALID_ENUM", 
                difficulty, 
                "有效的难度枚举值(EASY/MEDIUM/HARD)", 
                "无效的难度: " + difficulty,
                false
            ));
            return;
        }
        
        // 验证id字段
        String id = (String) questionBody.get("id");
        if (id == null || id.trim().isEmpty()) {
            result.addError(new ValidationError(
                "id", 
                "EMPTY_STRING", 
                id, 
                "非空字符串", 
                "题目ID不能为空",
                false
            ));
            return;
        }
        
        // 验证tags字段
        Object tagsObj = questionBody.get("tags");
        if (!(tagsObj instanceof List)) {
            result.addError(new ValidationError(
                "tags", 
                "INVALID_TYPE", 
                tagsObj == null ? "null" : tagsObj.getClass().getSimpleName(), 
                "字符串数组", 
                "tags字段必须是字符串数组",
                false
            ));
            return;
        }
        
        // 验证content字段
        String content = (String) questionBody.get("content");
        if (content == null || content.trim().isEmpty()) {
            result.addError(new ValidationError(
                "content", 
                "EMPTY_STRING", 
                content, 
                "非空HTML字符串", 
                "题目内容不能为空",
                false
            ));
            return;
        }
        
        // 验证explanation字段
        String explanation = (String) questionBody.get("explanation");
        if (explanation == null || explanation.trim().isEmpty()) {
            result.addError(new ValidationError(
                "explanation", 
                "EMPTY_STRING", 
                explanation, 
                "非空HTML字符串", 
                "题目解析不能为空",
                false
            ));
            return;
        }
    }

    /**
     * 验证题型特定字段
     */
    private void validateQuestionTypeSpecific(Map<String, Object> questionBody, ValidationResult result) {
        String type = (String) questionBody.get("type");
        Question.QuestionType questionType = Question.QuestionType.valueOf(type);
        
        switch (questionType) {
            case SINGLE_CHOICE:
            case MULTIPLE_CHOICE:
                validateChoiceQuestion(questionBody, result);
                break;
            case READING_COMPREHENSION:
            case CLOZE_TEST:
            case LISTENING:
                validateNestedQuestion(questionBody, result);
                break;
            case FILL_IN_BLANK:
            case TRUE_FALSE:
            case MATCHING:
                // 简单题型，只需要基础字段验证
                break;
        }
    }

    /**
     * 验证选择题特定字段
     */
    private void validateChoiceQuestion(Map<String, Object> questionBody, ValidationResult result) {
        // 验证options字段
        Object optionsObj = questionBody.get("options");
        if (optionsObj == null) {
            result.addError(new ValidationError(
                "options", 
                "MISSING_FIELD", 
                null, 
                "字符串数组", 
                "选择题必须包含options字段",
                false
            ));
            return;
        }
        
        if (!(optionsObj instanceof List)) {
            result.addError(new ValidationError(
                "options", 
                "INVALID_TYPE", 
                optionsObj.getClass().getSimpleName(), 
                "字符串数组", 
                "options字段必须是字符串数组",
                false
            ));
            return;
        }
        
        @SuppressWarnings("unchecked")
        List<String> options = (List<String>) optionsObj;
        if (options.size() < 2) {
            result.addError(new ValidationError(
                "options", 
                "INSUFFICIENT_OPTIONS", 
                String.valueOf(options.size()), 
                "至少2个选项", 
                "选择题至少需要2个选项",
                false
            ));
            return;
        }
        
        // 验证选项内容不为空且HTML格式正确
        for (int i = 0; i < options.size(); i++) {
            String option = options.get(i);
            if (option == null || option.trim().isEmpty()) {
                result.addError(new ValidationError(
                    "options[" + i + "]",
                    "EMPTY_OPTION",
                    option,
                    "非空字符串",
                    "选项内容不能为空",
                    false
                ));
                return;
            }

            // 验证选项的HTML格式（支持富文本内容）
            if (!isValidHtml(option)) {
                result.addError(new ValidationError(
                    "options[" + i + "]",
                    "INVALID_HTML",
                    option.length() > 50 ? option.substring(0, 50) + "..." : option,
                    "有效的HTML格式",
                    "选项" + (char)('A' + i) + "的HTML格式不正确",
                    false
                ));
                return;
            }
        }
    }

    /**
     * 验证嵌套题型特定字段
     */
    private void validateNestedQuestion(Map<String, Object> questionBody, ValidationResult result) {
        String type = (String) questionBody.get("type");



        // 只有阅读理解和完形填空需要material字段
        if ("READING_COMPREHENSION".equals(type) || "CLOZE_TEST".equals(type)) {
            String material = (String) questionBody.get("material");
            if (material == null || material.trim().isEmpty()) {
                String typeName = "READING_COMPREHENSION".equals(type) ? "阅读理解" : "完形填空";

                result.addError(new ValidationError(
                    "material",
                    "MISSING_FIELD",
                    material,
                    "非空HTML字符串",
                    typeName + "必须包含material字段",
                    false
                ));
                return;
            }
        }
        // 听力题不需要material字段，音频内容在content字段中
        
        // 验证subQuestions字段
        Object subQuestionsObj = questionBody.get("subQuestions");
        if (subQuestionsObj == null) {

            result.addError(new ValidationError(
                "subQuestions",
                "MISSING_FIELD",
                null,
                "子题目数组",
                "嵌套题型必须包含subQuestions字段",
                false
            ));
            return;
        }
        
        if (!(subQuestionsObj instanceof List)) {
            result.addError(new ValidationError(
                "subQuestions", 
                "INVALID_TYPE", 
                subQuestionsObj.getClass().getSimpleName(), 
                "子题目数组", 
                "subQuestions字段必须是数组",
                false
            ));
            return;
        }
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> subQuestions = (List<Map<String, Object>>) subQuestionsObj;
        if (subQuestions.isEmpty()) {
            result.addError(new ValidationError(
                "subQuestions", 
                "EMPTY_ARRAY", 
                "[]", 
                "至少1个子题目", 
                "嵌套题型至少需要1个子题目",
                false
            ));
            return;
        }
        
        // 验证每个子题目
        for (int i = 0; i < subQuestions.size(); i++) {
            validateSubQuestion(subQuestions.get(i), i, result);
            if (!result.isValid()) {
                return;
            }
        }
    }

    /**
     * 验证子题目
     */
    private void validateSubQuestion(Map<String, Object> subQuestion, int index, ValidationResult result) {
        String prefix = "subQuestions[" + index + "]";

        // 验证子题目必需字段
        String[] requiredFields = {"id", "content", "answer"};
        for (String field : requiredFields) {
            if (!subQuestion.containsKey(field) || subQuestion.get(field) == null) {
                result.addError(new ValidationError(
                    prefix + "." + field,
                    "MISSING_FIELD",
                    null,
                    "必需字段",
                    "子题目缺少必需字段: " + field,
                    false
                ));
                return;
            }
        }

        // 验证子题目内容不为空
        String content = (String) subQuestion.get("content");
        if (content.trim().isEmpty()) {
            result.addError(new ValidationError(
                prefix + ".content",
                "EMPTY_STRING",
                content,
                "非空字符串",
                "子题目内容不能为空",
                false
            ));
            return;
        }

        // 验证子题目内容的HTML格式
        if (!isValidHtml(content)) {
            result.addError(new ValidationError(
                prefix + ".content",
                "INVALID_HTML",
                content.length() > 50 ? content.substring(0, 50) + "..." : content,
                "有效的HTML格式",
                "子题目内容HTML格式不正确",
                false
            ));
            return;
        }

        // 如果子题目有选项（选择题），验证选项内容
        Object optionsObj = subQuestion.get("options");
        if (optionsObj != null && optionsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> options = (List<String>) optionsObj;

            for (int i = 0; i < options.size(); i++) {
                String option = options.get(i);
                if (option != null && !option.trim().isEmpty()) {
                    // 验证选项的HTML格式
                    if (!isValidHtml(option)) {
                        result.addError(new ValidationError(
                            prefix + ".options[" + i + "]",
                            "INVALID_HTML",
                            option.length() > 50 ? option.substring(0, 50) + "..." : option,
                            "有效的HTML格式",
                            "子题目选项" + (char)('A' + i) + "的HTML格式不正确",
                            false
                        ));
                        return;
                    }
                }
            }
        }
    }

    /**
     * 验证答案格式
     */
    private void validateAnswerFormat(Map<String, Object> questionBody, ValidationResult result) {
        String type = (String) questionBody.get("type");
        Object answer = questionBody.get("answer");
        Question.QuestionType questionType = Question.QuestionType.valueOf(type);

        switch (questionType) {
            case SINGLE_CHOICE:
                validateSingleChoiceAnswer(answer, questionBody, result);
                break;
            case MULTIPLE_CHOICE:
                validateMultipleChoiceAnswer(answer, questionBody, result);
                break;
            case FILL_IN_BLANK:
                validateFillInBlankAnswer(answer, result);
                break;
            case TRUE_FALSE:
                validateTrueFalseAnswer(answer, result);
                break;
            case READING_COMPREHENSION:
            case CLOZE_TEST:
            case LISTENING:
                // 嵌套题型的答案在子题目中验证
                break;
            case MATCHING:
                // 匹配题答案格式验证
                break;
        }
    }

    /**
     * 验证单选题答案格式
     */
    private void validateSingleChoiceAnswer(Object answer, Map<String, Object> questionBody, ValidationResult result) {
        if (!(answer instanceof String)) {
            result.addError(new ValidationError(
                "answer",
                "INVALID_TYPE",
                answer == null ? "null" : answer.getClass().getSimpleName(),
                "字母字符串(如 'A', 'B')",
                "单选题答案必须是字母字符串",
                false
            ));
            return;
        }

        String answerStr = (String) answer;
        if (!CHOICE_ANSWER_PATTERN.matcher(answerStr).matches()) {
            result.addError(new ValidationError(
                "answer",
                "INVALID_FORMAT",
                answerStr,
                "A-Z字母",
                "单选题答案必须是A-Z字母",
                false
            ));
            return;
        }

        // 验证答案是否在选项范围内
        @SuppressWarnings("unchecked")
        List<String> options = (List<String>) questionBody.get("options");
        if (options != null) {
            int answerIndex = answerStr.charAt(0) - 'A';
            if (answerIndex >= options.size()) {
                result.addError(new ValidationError(
                    "answer",
                    "OUT_OF_RANGE",
                    answerStr,
                    "A-" + (char)('A' + options.size() - 1),
                    "答案超出选项范围",
                    false
                ));
                return;
            }
        }
    }

    /**
     * 验证多选题答案格式
     */
    private void validateMultipleChoiceAnswer(Object answer, Map<String, Object> questionBody, ValidationResult result) {
        if (!(answer instanceof List)) {
            result.addError(new ValidationError(
                "answer",
                "INVALID_TYPE",
                answer == null ? "null" : answer.getClass().getSimpleName(),
                "字母数组(如 ['A', 'C'])",
                "多选题答案必须是字母数组",
                false
            ));
            return;
        }

        @SuppressWarnings("unchecked")
        List<String> answerList = (List<String>) answer;
        if (answerList.isEmpty()) {
            result.addError(new ValidationError(
                "answer",
                "EMPTY_ARRAY",
                "[]",
                "至少一个字母",
                "多选题答案不能为空",
                false
            ));
            return;
        }

        // 验证每个答案都是有效字母
        for (int i = 0; i < answerList.size(); i++) {
            String answerItem = answerList.get(i);
            if (!CHOICE_ANSWER_PATTERN.matcher(answerItem).matches()) {
                result.addError(new ValidationError(
                    "answer[" + i + "]",
                    "INVALID_FORMAT",
                    answerItem,
                    "A-Z字母",
                    "多选题答案必须都是A-Z字母",
                    false
                ));
                return;
            }
        }

        // 验证答案是否在选项范围内
        @SuppressWarnings("unchecked")
        List<String> options = (List<String>) questionBody.get("options");
        if (options != null) {
            for (String answerItem : answerList) {
                int answerIndex = answerItem.charAt(0) - 'A';
                if (answerIndex >= options.size()) {
                    result.addError(new ValidationError(
                        "answer",
                        "OUT_OF_RANGE",
                        answerItem,
                        "A-" + (char)('A' + options.size() - 1),
                        "答案 " + answerItem + " 超出选项范围",
                        false
                    ));
                    return;
                }
            }
        }
    }

    /**
     * 验证填空题答案格式
     */
    private void validateFillInBlankAnswer(Object answer, ValidationResult result) {
        if (!(answer instanceof List)) {
            result.addError(new ValidationError(
                "answer",
                "INVALID_TYPE",
                answer == null ? "null" : answer.getClass().getSimpleName(),
                "字符串数组",
                "填空题答案必须是字符串数组",
                false
            ));
            return;
        }

        @SuppressWarnings("unchecked")
        List<String> answerList = (List<String>) answer;
        if (answerList.isEmpty()) {
            result.addError(new ValidationError(
                "answer",
                "EMPTY_ARRAY",
                "[]",
                "至少一个答案",
                "填空题答案不能为空",
                false
            ));
            return;
        }

        // 验证每个答案都不为空
        for (int i = 0; i < answerList.size(); i++) {
            String answerItem = answerList.get(i);
            if (answerItem == null || answerItem.trim().isEmpty()) {
                result.addError(new ValidationError(
                    "answer[" + i + "]",
                    "EMPTY_STRING",
                    answerItem,
                    "非空字符串",
                    "填空题答案不能为空",
                    false
                ));
                return;
            }
        }
    }

    /**
     * 验证判断题答案格式
     */
    private void validateTrueFalseAnswer(Object answer, ValidationResult result) {
        if (!(answer instanceof Boolean)) {
            result.addError(new ValidationError(
                "answer",
                "INVALID_TYPE",
                answer == null ? "null" : answer.getClass().getSimpleName(),
                "布尔值(true/false)",
                "判断题答案必须是布尔值",
                false
            ));
            return;
        }
    }

    /**
     * 验证HTML内容安全性
     */
    private void validateHtmlContent(Map<String, Object> questionBody, ValidationResult result) {
        // 验证content字段的HTML格式
        String content = (String) questionBody.get("content");
        if (!isValidHtml(content)) {
            result.addError(new ValidationError(
                "content",
                "INVALID_HTML",
                content.length() > 50 ? content.substring(0, 50) + "..." : content,
                "有效的HTML格式",
                "题目内容HTML格式不正确",
                false
            ));
            return;
        }

        // 验证explanation字段的HTML格式
        String explanation = (String) questionBody.get("explanation");
        if (!isValidHtml(explanation)) {
            result.addError(new ValidationError(
                "explanation",
                "INVALID_HTML",
                explanation.length() > 50 ? explanation.substring(0, 50) + "..." : explanation,
                "有效的HTML格式",
                "题目解析HTML格式不正确",
                false
            ));
            return;
        }
    }

    /**
     * 简单的HTML格式验证
     */
    private boolean isValidHtml(String html) {
        if (html == null || html.trim().isEmpty()) {
            return false;
        }

        // 基础HTML标签匹配验证
        // 这里可以根据需要添加更严格的HTML验证逻辑
        return true; // 暂时返回true，后续可以添加更严格的验证
    }
}
