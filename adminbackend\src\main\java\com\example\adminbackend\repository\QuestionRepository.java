package com.example.adminbackend.repository;

import com.example.adminbackend.model.Question;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 题目数据访问层
 */
@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {

    // ==================== 基础查询方法 ====================

    /**
     * 根据知识点ID分页查询题目
     */
    Page<Question> findByKnowledgePointId(Long knowledgePointId, Pageable pageable);

    /**
     * 根据知识点ID查询启用的题目
     */
    List<Question> findByKnowledgePointIdAndEnabledTrue(Long knowledgePointId);

    /**
     * 查询所有启用的题目
     */
    List<Question> findByEnabledTrue();

    /**
     * 根据题目类型查询
     */
    List<Question> findByQuestionType(Question.QuestionType questionType);

    /**
     * 根据知识点ID和题目类型查询
     */
    List<Question> findByKnowledgePointIdAndQuestionType(Long knowledgePointId, Question.QuestionType questionType);

    // ==================== 统计查询方法 ====================

    /**
     * 统计知识点下的题目总数
     */
    long countByKnowledgePointId(Long knowledgePointId);

    /**
     * 统计知识点下启用的题目数
     */
    long countByKnowledgePointIdAndEnabledTrue(Long knowledgePointId);

    /**
     * 统计启用的题目总数
     */
    long countByEnabledTrue();

    /**
     * 统计各题型的题目数量
     */
    @Query("SELECT q.questionType, COUNT(q) FROM Question q WHERE q.knowledgePointId = :knowledgePointId GROUP BY q.questionType")
    List<Object[]> countByQuestionTypeAndKnowledgePointId(@Param("knowledgePointId") Long knowledgePointId);

    // ==================== JSON查询方法 ====================

    /**
     * 根据科目查询题目（从JSON body中提取）
     */
    @Query(value = "SELECT * FROM questions WHERE JSON_EXTRACT(body, '$.subject') = :subject", nativeQuery = true)
    List<Question> findBySubject(@Param("subject") String subject);

    /**
     * 根据难度查询题目（从JSON body中提取）
     */
    @Query(value = "SELECT * FROM questions WHERE JSON_EXTRACT(body, '$.difficulty') = :difficulty", nativeQuery = true)
    List<Question> findByDifficulty(@Param("difficulty") String difficulty);

    /**
     * 根据标签查询题目（从JSON body中提取）
     */
    @Query(value = "SELECT * FROM questions WHERE JSON_CONTAINS(JSON_EXTRACT(body, '$.tags'), JSON_ARRAY(:tag))", nativeQuery = true)
    List<Question> findByTag(@Param("tag") String tag);

    /**
     * 根据body中的id字段查询题目（用于重复检测）
     */
    @Query(value = "SELECT * FROM questions WHERE JSON_EXTRACT(body, '$.id') = :bodyId", nativeQuery = true)
    List<Question> findByBodyId(@Param("bodyId") String bodyId);

    // ==================== 复合查询方法 ====================

    /**
     * 复合条件查询题目
     */
    @Query(value = """
        SELECT * FROM questions q
        WHERE (:knowledgePointId IS NULL OR q.knowledge_point_id = :knowledgePointId)
        AND (:questionType IS NULL OR q.question_type = :questionType)
        AND (:subject IS NULL OR JSON_EXTRACT(q.body, '$.subject') = :subject)
        AND (:difficulty IS NULL OR JSON_EXTRACT(q.body, '$.difficulty') = :difficulty)
        AND (:enabled IS NULL OR q.enabled = :enabled)
        """,
        countQuery = """
        SELECT COUNT(*) FROM questions q
        WHERE (:knowledgePointId IS NULL OR q.knowledge_point_id = :knowledgePointId)
        AND (:questionType IS NULL OR q.question_type = :questionType)
        AND (:subject IS NULL OR JSON_EXTRACT(q.body, '$.subject') = :subject)
        AND (:difficulty IS NULL OR JSON_EXTRACT(q.body, '$.difficulty') = :difficulty)
        AND (:enabled IS NULL OR q.enabled = :enabled)
        """,
        nativeQuery = true)
    Page<Question> findByComplexConditions(
            @Param("knowledgePointId") Long knowledgePointId,
            @Param("questionType") String questionType,
            @Param("subject") String subject,
            @Param("difficulty") String difficulty,
            @Param("enabled") Boolean enabled,
            Pageable pageable);

    /**
     * 复合条件查询题目（包含搜索）
     */
    @Query(value = """
        SELECT * FROM questions q
        WHERE (:knowledgePointId IS NULL OR q.knowledge_point_id = :knowledgePointId)
        AND (:questionType IS NULL OR q.question_type = :questionType)
        AND (:subject IS NULL OR JSON_EXTRACT(q.body, '$.subject') = :subject)
        AND (:difficulty IS NULL OR JSON_EXTRACT(q.body, '$.difficulty') = :difficulty)
        AND (:enabled IS NULL OR q.enabled = :enabled)
        AND (:search IS NULL OR :search = '' OR
             JSON_EXTRACT(q.body, '$.content') LIKE CONCAT('%', :search, '%') OR
             JSON_EXTRACT(q.body, '$.id') LIKE CONCAT('%', :search, '%') OR
             JSON_CONTAINS(JSON_EXTRACT(q.body, '$.tags'), JSON_QUOTE(:search)))
        ORDER BY q.created_at DESC
        """,
        countQuery = """
        SELECT COUNT(*) FROM questions q
        WHERE (:knowledgePointId IS NULL OR q.knowledge_point_id = :knowledgePointId)
        AND (:questionType IS NULL OR q.question_type = :questionType)
        AND (:subject IS NULL OR JSON_EXTRACT(q.body, '$.subject') = :subject)
        AND (:difficulty IS NULL OR JSON_EXTRACT(q.body, '$.difficulty') = :difficulty)
        AND (:enabled IS NULL OR q.enabled = :enabled)
        AND (:search IS NULL OR :search = '' OR
             JSON_EXTRACT(q.body, '$.content') LIKE CONCAT('%', :search, '%') OR
             JSON_EXTRACT(q.body, '$.id') LIKE CONCAT('%', :search, '%') OR
             JSON_CONTAINS(JSON_EXTRACT(q.body, '$.tags'), JSON_QUOTE(:search)))
        """,
        nativeQuery = true)
    Page<Question> findByComplexConditionsWithSearch(
            @Param("knowledgePointId") Long knowledgePointId,
            @Param("questionType") String questionType,
            @Param("subject") String subject,
            @Param("difficulty") String difficulty,
            @Param("enabled") Boolean enabled,
            @Param("search") String search,
            Pageable pageable);

    // ==================== 数据验证方法 ====================

    /**
     * 验证JSON格式的题目数量
     */
    @Query(value = "SELECT COUNT(*) FROM questions WHERE JSON_VALID(body) = 1", nativeQuery = true)
    long countValidJsonQuestions();

    /**
     * 查找JSON格式无效的题目
     */
    @Query(value = "SELECT * FROM questions WHERE JSON_VALID(body) = 0", nativeQuery = true)
    List<Question> findInvalidJsonQuestions();

    // ==================== 级联删除方法 ====================

    /**
     * 根据知识点ID删除所有相关题目
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM Question q WHERE q.knowledgePointId = :knowledgePointId")
    void deleteByKnowledgePointId(@Param("knowledgePointId") Long knowledgePointId);



    /**
     * 根据知识点ID列表批量删除题目
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM Question q WHERE q.knowledgePointId IN :knowledgePointIds")
    void deleteByKnowledgePointIds(@Param("knowledgePointIds") List<Long> knowledgePointIds);
}
