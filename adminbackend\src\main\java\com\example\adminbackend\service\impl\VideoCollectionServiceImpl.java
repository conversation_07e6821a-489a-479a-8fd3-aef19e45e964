package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.Video;
import com.example.adminbackend.model.VideoCollection;
import com.example.adminbackend.repository.VideoCollectionRepository;
import com.example.adminbackend.service.VideoCollectionService;
import com.example.adminbackend.service.VideoService;
import com.example.adminbackend.model.CollectionVideo;
import com.example.adminbackend.repository.CollectionVideoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 视频合集服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VideoCollectionServiceImpl implements VideoCollectionService {

    private final VideoCollectionRepository videoCollectionRepository;
    private final VideoService videoService;
    private final CollectionVideoRepository collectionVideoRepository;

    @Override
    public List<VideoCollection> getAllVideoCollections() {
        log.info("获取所有视频合集");
        return videoCollectionRepository.findAllByOrderByCreatedAtDesc();
    }

    @Override
    public VideoCollection getVideoCollectionById(Long id) {
        log.info("根据ID获取视频合集: {}", id);
        return videoCollectionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("视频合集不存在: " + id));
    }

    @Override
    public VideoCollection getVideoCollectionByName(String name) {
        log.info("根据名称获取视频合集: {}", name);
        return videoCollectionRepository.findByName(name)
                .orElseThrow(() -> new RuntimeException("视频合集不存在: " + name));
    }

    @Override
    @Transactional
    public VideoCollection createVideoCollection(VideoCollection videoCollection) {
        log.info("创建视频合集: {}", videoCollection.getName());
        
        // 检查名称是否已存在
        if (videoCollectionRepository.existsByName(videoCollection.getName())) {
            throw new RuntimeException("视频合集名称已存在: " + videoCollection.getName());
        }
        
        return videoCollectionRepository.save(videoCollection);
    }

    @Override
    @Transactional
    public VideoCollection updateVideoCollection(Long id, VideoCollection videoCollection) {
        log.info("更新视频合集: {}", id);
        
        VideoCollection existing = getVideoCollectionById(id);
        
        // 检查名称是否与其他合集冲突
        if (!existing.getName().equals(videoCollection.getName()) && 
            videoCollectionRepository.existsByName(videoCollection.getName())) {
            throw new RuntimeException("视频合集名称已存在: " + videoCollection.getName());
        }
        
        existing.setName(videoCollection.getName());
        existing.setDescription(videoCollection.getDescription());
        existing.setCoverImageUrl(videoCollection.getCoverImageUrl());
        existing.setUpdatedAt(new Date());
        
        return videoCollectionRepository.save(existing);
    }

    @Override
    @Transactional
    public void deleteVideoCollection(Long id) {
        log.info("删除视频合集: {}", id);

        VideoCollection videoCollection = getVideoCollectionById(id);

        // 收集所有视频ID，直接删除（知识点视频不会被其他知识点引用）
        List<Long> videoIds = new ArrayList<>();
        if (videoCollection.getVideos() != null) {
            for (CollectionVideo cv : videoCollection.getVideos()) {
                if (cv.getVideo() != null) {
                    videoIds.add(cv.getVideo().getId());
                }
            }
        }

        // 删除视频合集（会级联删除collection_videos记录）
        videoCollectionRepository.delete(videoCollection);
        log.info("视频合集删除成功: {}", id);

        // 直接删除所有相关视频记录
        if (!videoIds.isEmpty()) {
            deleteAllVideos(videoIds);
        }
    }

    /**
     * 删除所有相关视频记录
     * 知识点的学习视频是专属的，不会被其他知识点引用，可以直接删除
     */
    private void deleteAllVideos(List<Long> videoIds) {
        log.info("开始删除视频记录，数量: {}", videoIds.size());

        int deletedCount = 0;
        for (Long videoId : videoIds) {
            try {
                videoService.deleteVideo(videoId);
                deletedCount++;
                log.info("删除视频记录: videoId={}", videoId);
            } catch (Exception e) {
                log.warn("删除视频失败: videoId={}, 错误: {}", videoId, e.getMessage());
            }
        }

        log.info("视频删除完成，成功删除: {} 个", deletedCount);
    }

    @Override
    public boolean existsByName(String name) {
        return videoCollectionRepository.existsByName(name);
    }

    @Override
    public List<VideoCollection> searchVideoCollections(String keyword) {
        log.info("搜索视频合集: {}", keyword);
        return videoCollectionRepository.findByKeyword(keyword);
    }

    @Override
    @Transactional
    public VideoCollection findOrCreateVideoCollectionByUrl(String videoUrl) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return null;
        }

        log.info("根据视频URL查找或创建视频合集: {}", videoUrl);

        try {
            // 1. 查找或创建视频
            Video video = videoService.findOrCreateVideoByUrl(videoUrl);

            // 2. 为这个视频创建一个专门的合集
            String collectionName = "视频合集-" + video.getTitle();

            // 检查是否已存在同名合集
            return videoCollectionRepository.findByName(collectionName)
                .orElseGet(() -> {
                    // 创建新的视频合集
                    VideoCollection newCollection = VideoCollection.builder()
                        .name(collectionName)
                        .description("从知识点视频自动创建的合集")
                        .coverImageUrl(video.getCoverImageUrl())
                        .createdAt(new Date())
                        .updatedAt(new Date())
                        .build();

                    VideoCollection savedCollection = videoCollectionRepository.save(newCollection);

                    // 创建视频与合集的关联记录
                    if (!collectionVideoRepository.existsByCollectionIdAndVideoId(savedCollection.getId(), video.getId())) {
                        CollectionVideo collectionVideo = CollectionVideo.builder()
                            .collection(savedCollection)
                            .video(video)
                            .createdAt(new Date())
                            .build();

                        collectionVideoRepository.save(collectionVideo);
                        log.info("创建视频合集关联记录: 合集ID={}, 视频ID={}", savedCollection.getId(), video.getId());
                    }

                    log.info("创建新视频合集: {} (视频: {})", savedCollection.getName(), video.getTitle());
                    return savedCollection;
                });

        } catch (Exception e) {
            log.error("根据视频URL创建视频合集失败: {}", videoUrl, e);
            return null;
        }
    }

    @Override
    @Transactional
    public VideoCollection findOrCreateVideoCollectionByUrlAndKnowledgePoint(String videoUrl, String knowledgePointName) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return null;
        }

        log.info("根据视频URL和知识点名称查找或创建视频合集: URL={}, 知识点={}", videoUrl, knowledgePointName);

        try {
            // 1. 查找或创建视频，使用知识点名称作为视频标题
            Video video = videoService.findOrCreateVideoByUrlAndTitle(videoUrl, knowledgePointName);

            // 2. 生成带时间戳的合集名称
            String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String collectionName = knowledgePointName + "_" + timestamp;

            // 检查是否已存在同名合集
            return videoCollectionRepository.findByName(collectionName)
                .orElseGet(() -> {
                    // 创建新的视频合集
                    VideoCollection newCollection = VideoCollection.builder()
                        .name(collectionName)
                        .description("知识点视频合集: " + knowledgePointName)
                        .coverImageUrl(video.getCoverImageUrl())
                        .createdAt(new Date())
                        .updatedAt(new Date())
                        .build();

                    VideoCollection savedCollection = videoCollectionRepository.save(newCollection);

                    // 创建视频与合集的关联记录
                    if (!collectionVideoRepository.existsByCollectionIdAndVideoId(savedCollection.getId(), video.getId())) {
                        CollectionVideo collectionVideo = CollectionVideo.builder()
                            .collection(savedCollection)
                            .video(video)
                            .createdAt(new Date())
                            .build();

                        collectionVideoRepository.save(collectionVideo);
                        log.info("创建视频合集关联记录: 合集ID={}, 视频ID={}", savedCollection.getId(), video.getId());
                    }

                    log.info("创建新视频合集: {} (知识点: {}, 视频: {})", savedCollection.getName(), knowledgePointName, video.getTitle());
                    return savedCollection;
                });

        } catch (Exception e) {
            log.error("根据视频URL和知识点名称创建视频合集失败: URL={}, 知识点={}", videoUrl, knowledgePointName, e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean addVideoToCollection(Long collectionId, String videoUrl) {
        if (collectionId == null || videoUrl == null || videoUrl.trim().isEmpty()) {
            log.error("参数不能为空: collectionId={}, videoUrl={}", collectionId, videoUrl);
            return false;
        }

        try {
            // 检查视频合集是否存在
            VideoCollection collection = videoCollectionRepository.findById(collectionId)
                .orElse(null);
            if (collection == null) {
                log.error("视频合集不存在: ID={}", collectionId);
                return false;
            }

            // 从合集名称中提取知识点名称（去掉时间戳后缀）
            String collectionName = collection.getName();
            String knowledgePointName = extractKnowledgePointNameFromCollectionName(collectionName);

            // 查找或创建视频，使用知识点名称作为标题
            Video video = videoService.findOrCreateVideoByUrlAndTitle(videoUrl, knowledgePointName);
            if (video == null) {
                log.error("创建视频失败: URL={}", videoUrl);
                return false;
            }

            // 检查是否已存在关联记录
            if (collectionVideoRepository.existsByCollectionIdAndVideoId(collectionId, video.getId())) {
                log.info("视频已存在于合集中: 合集ID={}, 视频ID={}", collectionId, video.getId());
                return true; // 已存在，视为成功
            }

            // 创建新的关联记录
            CollectionVideo collectionVideo = CollectionVideo.builder()
                .collection(collection)
                .video(video)
                .createdAt(new Date())
                .build();

            collectionVideoRepository.save(collectionVideo);
            log.info("成功向视频合集添加视频: 合集ID={}, 视频ID={}, 视频标题={}, 视频URL={}",
                collectionId, video.getId(), video.getTitle(), videoUrl);

            return true;

        } catch (Exception e) {
            log.error("向视频合集添加视频失败: 合集ID={}, 视频URL={}", collectionId, videoUrl, e);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public int getVideoCountInCollection(Long collectionId) {
        if (collectionId == null) {
            return 0;
        }

        try {
            return (int) collectionVideoRepository.countByCollectionId(collectionId);
        } catch (Exception e) {
            log.error("获取视频合集中的视频数量失败: 合集ID={}", collectionId, e);
            return 0;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getVideoUrlsInCollection(Long collectionId) {
        if (collectionId == null) {
            return new ArrayList<>();
        }

        try {
            List<CollectionVideo> collectionVideos = collectionVideoRepository.findByCollectionId(collectionId);
            List<String> videoUrls = new ArrayList<>();

            for (CollectionVideo cv : collectionVideos) {
                if (cv.getVideo() != null && cv.getVideo().getVideoUrl() != null) {
                    videoUrls.add(cv.getVideo().getVideoUrl());
                }
            }

            log.info("获取视频合集中的视频URL: 合集ID={}, URL数量={}", collectionId, videoUrls.size());
            return videoUrls;
        } catch (Exception e) {
            log.error("获取视频合集中的视频URL失败: 合集ID={}", collectionId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean updateVideoCollectionWithNewUrl(Long collectionId, String newVideoUrl, String knowledgePointName) {
        if (collectionId == null || newVideoUrl == null || newVideoUrl.trim().isEmpty()) {
            log.warn("更新视频合集参数无效: 合集ID={}, 视频URL={}", collectionId, newVideoUrl);
            return false;
        }

        try {
            // 1. 检查视频合集是否存在
            VideoCollection collection = videoCollectionRepository.findById(collectionId).orElse(null);
            if (collection == null) {
                log.error("视频合集不存在: ID={}", collectionId);
                return false;
            }

            // 2. 获取现有的视频URL列表
            List<String> existingUrls = getVideoUrlsInCollection(collectionId);

            // 3. 检查新URL是否已存在
            if (existingUrls.contains(newVideoUrl)) {
                log.info("视频URL已存在于合集中，无需更新: 合集ID={}, URL={}", collectionId, newVideoUrl);
                return true;
            }

            log.info("开始更新视频合集: 合集ID={}, 新视频URL={}, 现有视频数量={}",
                collectionId, newVideoUrl, existingUrls.size());

            // 4. 清理现有的视频关联
            List<CollectionVideo> existingCollectionVideos = collectionVideoRepository.findByCollectionId(collectionId);
            List<Long> videoIdsToDelete = new ArrayList<>();

            for (CollectionVideo cv : existingCollectionVideos) {
                if (cv.getVideo() != null) {
                    videoIdsToDelete.add(cv.getVideo().getId());
                }
            }

            // 删除关联记录
            collectionVideoRepository.deleteByCollectionId(collectionId);
            log.info("清理视频合集关联记录: 合集ID={}, 清理数量={}", collectionId, existingCollectionVideos.size());

            // 5. 删除不再使用的视频记录（知识点视频是专属的，可以安全删除）
            for (Long videoId : videoIdsToDelete) {
                try {
                    // 检查视频是否被其他合集引用
                    long referenceCount = collectionVideoRepository.countByVideoId(videoId);
                    if (referenceCount == 0) {
                        videoService.deleteVideo(videoId);
                        log.info("删除不再使用的视频: videoId={}", videoId);
                    } else {
                        log.info("视频被其他合集引用，跳过删除: videoId={}, 引用数={}", videoId, referenceCount);
                    }
                } catch (Exception e) {
                    log.warn("删除视频失败: videoId={}, 错误: {}", videoId, e.getMessage());
                }
            }

            // 6. 创建新的视频和关联记录
            Video newVideo = videoService.findOrCreateVideoByUrlAndTitle(newVideoUrl, knowledgePointName);
            if (newVideo == null) {
                log.error("创建新视频失败: URL={}", newVideoUrl);
                return false;
            }

            // 创建新的关联记录
            CollectionVideo newCollectionVideo = CollectionVideo.builder()
                .collection(collection)
                .video(newVideo)
                .createdAt(new Date())
                .build();

            collectionVideoRepository.save(newCollectionVideo);

            // 更新合集的更新时间
            collection.setUpdatedAt(new Date());
            videoCollectionRepository.save(collection);

            log.info("视频合集更新成功: 合集ID={}, 新视频ID={}, 新视频URL={}",
                collectionId, newVideo.getId(), newVideoUrl);
            return true;

        } catch (Exception e) {
            log.error("更新视频合集失败: 合集ID={}, 新视频URL={}", collectionId, newVideoUrl, e);
            return false;
        }
    }

    /**
     * 从视频合集名称中提取知识点名称
     * 合集名称格式: "知识点名称_时间戳"
     */
    private String extractKnowledgePointNameFromCollectionName(String collectionName) {
        if (collectionName == null || collectionName.trim().isEmpty()) {
            return "未知知识点";
        }

        // 查找最后一个下划线的位置
        int lastUnderscoreIndex = collectionName.lastIndexOf('_');
        if (lastUnderscoreIndex > 0) {
            // 提取下划线之前的部分作为知识点名称
            return collectionName.substring(0, lastUnderscoreIndex);
        }

        // 如果没有找到下划线，返回整个名称
        return collectionName;
    }
}
