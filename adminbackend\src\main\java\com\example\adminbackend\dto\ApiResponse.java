package com.example.adminbackend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一API响应格式
 * @param <T> 响应数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 错误详情列表
     */
    private List<ErrorDetail> errors;
    
    /**
     * 响应时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 请求路径
     */
    private String path;
    
    /**
     * 请求ID（用于追踪）
     */
    private String requestId;
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .code(200)
                .message("操作成功")
                .data(data)
                .build();
    }
    
    /**
     * 成功响应（自定义消息）
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return ApiResponse.<T>builder()
                .code(200)
                .message(message)
                .data(data)
                .build();
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .build();
    }
    
    /**
     * 失败响应（带错误详情）
     */
    public static <T> ApiResponse<T> error(int code, String message, List<ErrorDetail> errors) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .errors(errors)
                .build();
    }
    
    /**
     * 参数验证失败响应
     */
    public static <T> ApiResponse<T> validationError(String message, List<ErrorDetail> errors) {
        return ApiResponse.<T>builder()
                .code(400)
                .message(message)
                .errors(errors)
                .build();
    }
    
    /**
     * 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return ApiResponse.<T>builder()
                .code(401)
                .message(message != null ? message : "未授权访问")
                .build();
    }
    
    /**
     * 禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return ApiResponse.<T>builder()
                .code(403)
                .message(message != null ? message : "没有权限访问此资源")
                .build();
    }
    
    /**
     * 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return ApiResponse.<T>builder()
                .code(404)
                .message(message != null ? message : "请求的资源不存在")
                .build();
    }
    
    /**
     * 服务器内部错误响应
     */
    public static <T> ApiResponse<T> internalError(String message) {
        return ApiResponse.<T>builder()
                .code(500)
                .message(message != null ? message : "服务器内部错误")
                .build();
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 设置请求信息
     */
    public ApiResponse<T> withRequestInfo(String path, String requestId) {
        this.path = path;
        this.requestId = requestId;
        return this;
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code >= 200 && code < 300;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
