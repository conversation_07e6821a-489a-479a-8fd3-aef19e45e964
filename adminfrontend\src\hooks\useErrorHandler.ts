import React, { useState, useCallback, useRef } from 'react';
import { AxiosError } from 'axios';
import { ProcessedError, ErrorHandler, ErrorType } from '../utils/errorHandler';
import { <PERSON>tryHandler } from '../utils/retryHandler';

interface UseErrorHandlerOptions {
  showSnackbar?: boolean;
  autoRetry?: boolean;
  maxRetries?: number;
  onError?: (error: ProcessedError) => void;
}

interface UseErrorHandlerReturn {
  error: ProcessedError | null;
  isLoading: boolean;
  clearError: () => void;
  handleError: (error: any) => void;
  executeWithErrorHandling: <T>(
    operation: () => Promise<T>,
    options?: { showLoading?: boolean; retryable?: boolean }
  ) => Promise<T | null>;
  retryLastOperation: () => Promise<void>;
}

/**
 * 错误处理Hook
 */
export const useErrorHandler = (options: UseErrorHandlerOptions = {}): UseErrorHandlerReturn => {
  const [error, setError] = useState<ProcessedError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const lastOperationRef = useRef<(() => Promise<any>) | null>(null);

  const {
    showSnackbar = true,
    autoRetry = false,
    maxRetries = 3,
    onError
  } = options;

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * 处理错误
   */
  const handleError = useCallback((rawError: any) => {
    let processedError: ProcessedError;

    if (rawError instanceof Error || (rawError && rawError.response)) {
      // Axios错误或普通Error对象
      processedError = ErrorHandler.handleApiError(rawError as AxiosError);
    } else if (rawError && typeof rawError === 'object' && rawError.type) {
      // 已经是ProcessedError
      processedError = rawError as ProcessedError;
    } else {
      // 其他类型的错误
      processedError = {
        type: ErrorType.UNKNOWN_ERROR,
        code: 0,
        message: String(rawError) || '发生未知错误',
        retryable: false
      };
    }

    setError(processedError);

    // 调用外部错误处理函数
    if (onError) {
      onError(processedError);
    }

    // 显示错误提示
    if (showSnackbar) {
      // 这里可以集成全局的snackbar显示逻辑
    }

    return processedError;
  }, [onError, showSnackbar]);

  /**
   * 执行带错误处理的操作
   */
  const executeWithErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    operationOptions: { showLoading?: boolean; retryable?: boolean } = {}
  ): Promise<T | null> => {
    const { showLoading = true, retryable = autoRetry } = operationOptions;

    try {
      if (showLoading) {
        setIsLoading(true);
      }
      
      clearError();
      
      // 保存操作以便重试
      lastOperationRef.current = operation;

      let result: T;

      if (retryable) {
        // 使用重试机制
        result = await RetryHandler.executeWithRetry(operation, {
          maxRetries,
          retryCondition: (error) => ErrorHandler.isRetryable(error)
        });
      } else {
        // 直接执行
        result = await operation();
      }

      return result;
    } catch (error) {
      const processedError = handleError(error);
      
      // 如果是认证错误，不返回null，让上层处理重定向
      if (ErrorHandler.needsReauth(processedError)) {
        throw processedError;
      }
      
      return null;
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  }, [autoRetry, maxRetries, clearError, handleError]);

  /**
   * 重试上次操作
   */
  const retryLastOperation = useCallback(async () => {
    if (!lastOperationRef.current) {
      console.warn('没有可重试的操作');
      return;
    }

    await executeWithErrorHandling(lastOperationRef.current, { retryable: true });
  }, [executeWithErrorHandling]);

  return {
    error,
    isLoading,
    clearError,
    handleError,
    executeWithErrorHandling,
    retryLastOperation
  };
};

/**
 * 异步操作Hook
 */
export const useAsyncOperation = <T>(
  operation: () => Promise<T>,
  dependencies: any[] = [],
  options: UseErrorHandlerOptions = {}
) => {
  const [data, setData] = useState<T | null>(null);
  const errorHandler = useErrorHandler(options);

  const execute = useCallback(async () => {
    const result = await errorHandler.executeWithErrorHandling(operation);
    if (result !== null) {
      setData(result);
    }
    return result;
  }, [operation, errorHandler]);

  // 自动执行
  React.useEffect(() => {
    execute();
  }, dependencies);

  return {
    data,
    ...errorHandler,
    execute
  };
};

/**
 * API调用Hook
 */
export const useApiCall = <T>(
  apiCall: () => Promise<T>,
  options: UseErrorHandlerOptions & {
    immediate?: boolean;
    dependencies?: any[];
  } = {}
) => {
  const { immediate = false, dependencies = [], ...errorOptions } = options;
  const [data, setData] = useState<T | null>(null);
  const errorHandler = useErrorHandler(errorOptions);

  const execute = useCallback(async () => {
    const result = await errorHandler.executeWithErrorHandling(apiCall, {
      retryable: true
    });
    
    if (result !== null) {
      setData(result);
    }
    
    return result;
  }, [apiCall, errorHandler]);

  // 立即执行或依赖变化时执行
  React.useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute, ...dependencies]);

  return {
    data,
    ...errorHandler,
    execute,
    refetch: execute
  };
};

/**
 * 表单提交Hook
 */
export const useFormSubmit = <T>(
  submitFunction: (data: any) => Promise<T>,
  options: UseErrorHandlerOptions & {
    onSuccess?: (result: T) => void;
    resetOnSuccess?: boolean;
  } = {}
) => {
  const { onSuccess, resetOnSuccess = false, ...errorOptions } = options;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const errorHandler = useErrorHandler(errorOptions);

  const submit = useCallback(async (formData: any) => {
    setIsSubmitting(true);
    
    try {
      const result = await errorHandler.executeWithErrorHandling(
        () => submitFunction(formData),
        { showLoading: false }
      );
      
      if (result !== null) {
        if (onSuccess) {
          onSuccess(result);
        }
        
        if (resetOnSuccess) {
          errorHandler.clearError();
        }
      }
      
      return result;
    } finally {
      setIsSubmitting(false);
    }
  }, [submitFunction, onSuccess, resetOnSuccess, errorHandler]);

  return {
    ...errorHandler,
    isSubmitting,
    submit
  };
};
