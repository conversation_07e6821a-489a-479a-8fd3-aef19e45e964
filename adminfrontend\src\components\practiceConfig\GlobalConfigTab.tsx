import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Divider,
  Chip,
} from '@mui/material';
import {
  Save,
  Refresh,
  Public,
  Psychology,
  Quiz,
} from '@mui/icons-material';
import { PracticeConfigAPI, PracticeConfig } from '../../services/practiceConfigApi';

interface GlobalConfigTabProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

interface GlobalConfigForm {
  knowledgePointPracticeCount: number;
  knowledgePointPracticeStrategy: 'RANDOM' | 'DIFFICULTY_BALANCED' | 'ERROR_PRIORITY' | 'TYPE_BALANCED';
  chapterTestStrategy: 'RANDOM' | 'DIFFICULTY_BALANCED' | 'ERROR_PRIORITY' | 'TYPE_BALANCED';
}

const GlobalConfigTab: React.FC<GlobalConfigTabProps> = ({ onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [configs, setConfigs] = useState<PracticeConfig[]>([]);
  const [formData, setFormData] = useState<GlobalConfigForm>({
    knowledgePointPracticeCount: 10,
    knowledgePointPracticeStrategy: 'RANDOM',
    chapterTestStrategy: 'RANDOM',
  });

  // 策略选项
  const strategyOptions = [
    { value: 'RANDOM', label: '随机选择', description: '随机从题库中选择题目' },
    { value: 'DIFFICULTY_BALANCED', label: '难度均衡', description: '按难度比例均衡选择题目' },
    { value: 'ERROR_PRIORITY', label: '错题优先', description: '优先选择学生的错题' },
    { value: 'TYPE_BALANCED', label: '题型均衡', description: '按题型比例均衡选择题目' },
  ];

  // 加载全局配置
  const loadGlobalConfigs = async () => {
    try {
      setLoading(true);
      const allConfigs = await PracticeConfigAPI.getAllEnabledConfigs();
      const globalConfigs = allConfigs.filter(config => config.scopeType === 'GLOBAL');
      setConfigs(globalConfigs);

      // 设置表单数据
      const knowledgePointConfig = globalConfigs.find(
        config => config.configType === 'KNOWLEDGE_POINT_PRACTICE'
      );
      const chapterTestConfig = globalConfigs.find(
        config => config.configType === 'CHAPTER_TEST'
      );

      setFormData({
        knowledgePointPracticeCount: knowledgePointConfig?.questionCount || 10,
        knowledgePointPracticeStrategy: knowledgePointConfig?.selectionStrategy || 'RANDOM',
        chapterTestStrategy: chapterTestConfig?.selectionStrategy || 'RANDOM',
      });
    } catch (error: any) {
      console.error('加载全局配置失败:', error);
      onError('加载全局配置失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 保存知识点练习配置
  const saveKnowledgePointConfig = async () => {
    try {
      setSaving(true);
      await PracticeConfigAPI.createOrUpdateGlobalConfig(
        'KNOWLEDGE_POINT_PRACTICE',
        formData.knowledgePointPracticeCount,
        formData.knowledgePointPracticeStrategy
      );
      onSuccess('知识点练习全局配置保存成功');
      await loadGlobalConfigs();
    } catch (error: any) {
      console.error('保存知识点练习配置失败:', error);
      onError('保存知识点练习配置失败: ' + (error.message || '未知错误'));
    } finally {
      setSaving(false);
    }
  };

  // 保存章节测试配置
  const saveChapterTestConfig = async () => {
    try {
      setSaving(true);
      await PracticeConfigAPI.createOrUpdateGlobalConfig(
        'CHAPTER_TEST',
        1, // 章节测试使用固定的计算规则
        formData.chapterTestStrategy
      );
      onSuccess('章节测试全局配置保存成功');
      await loadGlobalConfigs();
    } catch (error: any) {
      console.error('保存章节测试配置失败:', error);
      onError('保存章节测试配置失败: ' + (error.message || '未知错误'));
    } finally {
      setSaving(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof GlobalConfigForm, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 获取策略标签
  const getStrategyLabel = (strategy: string) => {
    const option = strategyOptions.find(opt => opt.value === strategy);
    return option?.label || strategy;
  };

  // 获取策略描述
  const getStrategyDescription = (strategy: string) => {
    const option = strategyOptions.find(opt => opt.value === strategy);
    return option?.description || '';
  };

  useEffect(() => {
    loadGlobalConfigs();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* 页面说明 */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          全局配置是系统的默认配置，当科目、章节、知识点没有特定配置时，将使用全局配置。
          配置优先级：知识点配置 &gt; 章节配置 &gt; 科目配置 &gt; 全局配置
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* 知识点练习配置 */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Psychology sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" fontWeight="bold">
                  知识点练习配置
                </Typography>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                设置学生点击"立即攻克"时的默认题目数量和抽题策略
              </Typography>

              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="默认题目数量"
                  type="number"
                  value={formData.knowledgePointPracticeCount}
                  onChange={(e) => handleFieldChange('knowledgePointPracticeCount', parseInt(e.target.value) || 1)}
                  inputProps={{ min: 1, max: 100 }}
                  helperText="建议设置为5-20道题目"
                  sx={{ mb: 2 }}
                />

                <FormControl fullWidth>
                  <InputLabel>抽题策略</InputLabel>
                  <Select
                    value={formData.knowledgePointPracticeStrategy}
                    label="抽题策略"
                    onChange={(e) => handleFieldChange('knowledgePointPracticeStrategy', e.target.value)}
                  >
                    {strategyOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        <Box>
                          <Typography variant="body2">{option.label}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {option.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <Button
                variant="contained"
                startIcon={<Save />}
                onClick={saveKnowledgePointConfig}
                disabled={saving}
                fullWidth
              >
                {saving ? '保存中...' : '保存知识点练习配置'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* 章节测试配置 */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Quiz sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" fontWeight="bold">
                  章节测试配置
                </Typography>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                设置学生点击"测一测"时的抽题策略，题目数量按固定规则计算
              </Typography>

              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>章节测试题目数量计算规则：</strong><br />
                  每个知识点题目数 = 该知识点总题目数 ÷ 10（最小值为1道）
                </Typography>
              </Alert>

              <Box sx={{ mb: 3 }}>
                <FormControl fullWidth>
                  <InputLabel>抽题策略</InputLabel>
                  <Select
                    value={formData.chapterTestStrategy}
                    label="抽题策略"
                    onChange={(e) => handleFieldChange('chapterTestStrategy', e.target.value)}
                  >
                    {strategyOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        <Box>
                          <Typography variant="body2">{option.label}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {option.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <Button
                variant="contained"
                startIcon={<Save />}
                onClick={saveChapterTestConfig}
                disabled={saving}
                fullWidth
              >
                {saving ? '保存中...' : '保存章节测试配置'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* 当前配置状态 */}
        <Grid size={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Public sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6" fontWeight="bold">
                    当前全局配置状态
                  </Typography>
                </Box>
                <Button
                  startIcon={<Refresh />}
                  onClick={loadGlobalConfigs}
                  disabled={loading}
                >
                  刷新
                </Button>
              </Box>

              {configs.length === 0 ? (
                <Alert severity="warning">
                  暂无全局配置，请先保存配置
                </Alert>
              ) : (
                <Grid container spacing={2}>
                  {configs.map((config) => (
                    <Grid size={{ xs: 12, md: 6 }} key={config.id}>
                      <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Chip
                            label={config.configType === 'KNOWLEDGE_POINT_PRACTICE' ? '知识点练习' : '章节测试'}
                            color="primary"
                            size="small"
                            sx={{ mr: 1 }}
                          />
                          <Chip
                            label={config.enabled ? '已启用' : '已禁用'}
                            color={config.enabled ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>题目数量：</strong>{config.questionCount}道
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>抽题策略：</strong>{getStrategyLabel(config.selectionStrategy)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {getStrategyDescription(config.selectionStrategy)}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GlobalConfigTab;
