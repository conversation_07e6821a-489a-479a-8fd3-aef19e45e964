package com.example.adminbackend.validator;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果类
 */
@Data
public class ValidationResult {
    
    private boolean valid = true;
    private String message = "";
    private List<ValidationError> errors = new ArrayList<>();
    
    public ValidationResult() {
    }
    
    public ValidationResult(boolean valid, String message) {
        this.valid = valid;
        this.message = message;
    }
    
    /**
     * 添加验证错误
     */
    public void addError(ValidationError error) {
        this.errors.add(error);
        this.valid = false;
        if (this.message.isEmpty()) {
            this.message = error.getFixSuggestion();
        }
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 获取第一个错误信息
     */
    public String getFirstErrorMessage() {
        if (errors.isEmpty()) {
            return message;
        }
        return errors.get(0).getFixSuggestion();
    }
    
    /**
     * 获取所有错误信息
     */
    public List<String> getAllErrorMessages() {
        List<String> messages = new ArrayList<>();
        for (ValidationError error : errors) {
            messages.add(error.getFixSuggestion());
        }
        return messages;
    }
    
    /**
     * 转换为Map格式（兼容现有API）
     */
    public java.util.Map<String, Object> toMap() {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("valid", valid);
        result.put("message", getFirstErrorMessage());
        result.put("errors", getAllErrorMessages());
        result.put("errorDetails", errors);
        return result;
    }
}
