package com.example.adminbackend.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 练习配置实体类
 * 用于配置不同场景下的题目练习数量和策略
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "practice_configs")
public class PracticeConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "config_type", nullable = false)
    private ConfigType configType;

    /**
     * 适用范围
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "scope_type", nullable = false)
    private ScopeType scopeType;

    /**
     * 目标ID（根据scopeType确定具体含义）
     * GLOBAL: null
     * SUBJECT: 科目ID
     * CHAPTER: 章节ID  
     * KNOWLEDGE_POINT: 知识点ID
     */
    @Column(name = "target_id")
    private Long targetId;

    /**
     * 题目数量配置
     */
    @Column(name = "question_count", nullable = false)
    private Integer questionCount;

    /**
     * 抽题策略
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_strategy", nullable = false)
    @Builder.Default
    private SelectionStrategy selectionStrategy = SelectionStrategy.RANDOM;

    /**
     * 是否启用
     */
    @Builder.Default
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 配置描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }

    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        KNOWLEDGE_POINT_PRACTICE,  // 知识点练习
        CHAPTER_TEST              // 章节测试
    }

    /**
     * 适用范围枚举
     */
    public enum ScopeType {
        GLOBAL,          // 全局配置
        SUBJECT,         // 科目级配置
        CHAPTER,         // 章节级配置
        KNOWLEDGE_POINT  // 知识点级配置
    }

    /**
     * 抽题策略枚举
     */
    public enum SelectionStrategy {
        RANDOM,              // 随机选择
        DIFFICULTY_BALANCED, // 难度均衡
        ERROR_PRIORITY,      // 错题优先
        TYPE_BALANCED       // 题型均衡
    }

    /**
     * 获取配置的显示名称
     */
    public String getDisplayName() {
        StringBuilder sb = new StringBuilder();
        
        // 配置类型
        switch (configType) {
            case KNOWLEDGE_POINT_PRACTICE:
                sb.append("知识点练习");
                break;
            case CHAPTER_TEST:
                sb.append("章节测试");
                break;
        }
        
        sb.append(" - ");
        
        // 适用范围
        switch (scopeType) {
            case GLOBAL:
                sb.append("全局配置");
                break;
            case SUBJECT:
                sb.append("科目配置");
                break;
            case CHAPTER:
                sb.append("章节配置");
                break;
            case KNOWLEDGE_POINT:
                sb.append("知识点配置");
                break;
        }
        
        return sb.toString();
    }

    /**
     * 检查配置是否匹配指定的条件
     */
    public boolean matches(ConfigType configType, ScopeType scopeType, Long targetId) {
        return this.configType == configType 
            && this.scopeType == scopeType 
            && (this.targetId == null ? targetId == null : this.targetId.equals(targetId))
            && this.enabled;
    }
}
