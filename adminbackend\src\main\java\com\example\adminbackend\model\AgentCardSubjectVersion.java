package com.example.adminbackend.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agent_card_subject_versions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"agent_card_subject_id", "subject_version_id"}, name = "uk_card_subject_version")
})
public class AgentCardSubjectVersion {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_card_subject_id", nullable = false)
    private AgentCardSubject agentCardSubject;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_version_id", nullable = false)
    private SubjectVersion subjectVersion;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }
} 