import React, { useState } from 'react';
import { Box, Typography, Paper, Button, Stack } from '@mui/material';
import MathLiveEditor from '../components/MathLiveEditor';

const MathKeyboardTestPage: React.FC = () => {
  const [latex1, setLatex1] = useState('');
  const [latex2, setLatex2] = useState('');

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        数学键盘测试页面
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        测试自定义键盘与MathLive编辑器的集成效果
      </Typography>

      <Stack spacing={4}>
        {/* 使用自定义键盘的编辑器 */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            自定义键盘编辑器
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            点击输入框会显示自定义数学键盘
          </Typography>
          <MathLiveEditor
            value={latex1}
            onChange={setLatex1}
            placeholder="点击这里输入数学公式..."
            height={80}
            useCustomKeyboard={true}
            virtualKeyboard={true}
          />
          <Typography variant="body2" sx={{ mt: 1 }}>
            LaTeX: {latex1}
          </Typography>
        </Paper>

        {/* 使用内置键盘的编辑器 */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            内置键盘编辑器
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            点击输入框会显示MathLive内置键盘
          </Typography>
          <MathLiveEditor
            value={latex2}
            onChange={setLatex2}
            placeholder="点击这里输入数学公式..."
            height={80}
            useCustomKeyboard={false}
            virtualKeyboard={true}
          />
          <Typography variant="body2" sx={{ mt: 1 }}>
            LaTeX: {latex2}
          </Typography>
        </Paper>

        {/* 测试按钮 */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            测试功能
          </Typography>
          <Stack direction="row" spacing={2}>
            <Button 
              variant="outlined" 
              onClick={() => setLatex1('\\frac{a}{b} + \\sqrt{x^2 + y^2}')}
            >
              设置示例公式1
            </Button>
            <Button 
              variant="outlined" 
              onClick={() => setLatex2('\\int_{0}^{\\infty} e^{-x} dx')}
            >
              设置示例公式2
            </Button>
            <Button 
              variant="outlined" 
              onClick={() => {
                setLatex1('');
                setLatex2('');
              }}
            >
              清空所有
            </Button>
          </Stack>
        </Paper>
      </Stack>
    </Box>
  );
};

export default MathKeyboardTestPage;
