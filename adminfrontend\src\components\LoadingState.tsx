import React from 'react';
import {
  Box,
  CircularProgress,
  LinearProgress,
  Typography,
  Skeleton,
  Paper
} from '@mui/material';

interface LoadingStateProps {
  type?: 'circular' | 'linear' | 'skeleton' | 'dots';
  message?: string;
  size?: 'small' | 'medium' | 'large';
  fullHeight?: boolean;
  overlay?: boolean;
}

/**
 * 统一的加载状态组件
 * 支持多种加载样式和配置
 */
const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'circular',
  message = '加载中...',
  size = 'medium',
  fullHeight = false,
  overlay = false
}) => {
  const getSizeValue = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 60;
      case 'medium':
      default:
        return 40;
    }
  };

  const renderCircularLoading = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        minHeight: fullHeight ? '200px' : 'auto',
        p: 3
      }}
    >
      <CircularProgress size={getSizeValue()} />
      {message && (
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      )}
    </Box>
  );

  const renderLinearLoading = () => (
    <Box sx={{ width: '100%', p: 2 }}>
      {message && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {message}
        </Typography>
      )}
      <LinearProgress />
    </Box>
  );

  const renderSkeletonLoading = () => (
    <Box sx={{ p: 2 }}>
      {message && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {message}
        </Typography>
      )}
      <Skeleton variant="text" width="60%" height={32} />
      <Skeleton variant="text" width="80%" height={24} />
      <Skeleton variant="text" width="40%" height={24} />
      <Skeleton variant="rectangular" width="100%" height={120} sx={{ mt: 2 }} />
    </Box>
  );

  const renderDotsLoading = () => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 1,
        p: 2
      }}
    >
      {message && (
        <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
          {message}
        </Typography>
      )}
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        {[0, 1, 2].map((index) => (
          <Box
            key={index}
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              animation: 'pulse 1.5s ease-in-out infinite',
              animationDelay: `${index * 0.3}s`,
              '@keyframes pulse': {
                '0%, 80%, 100%': {
                  opacity: 0.3,
                  transform: 'scale(0.8)'
                },
                '40%': {
                  opacity: 1,
                  transform: 'scale(1)'
                }
              }
            }}
          />
        ))}
      </Box>
    </Box>
  );

  const renderContent = () => {
    switch (type) {
      case 'linear':
        return renderLinearLoading();
      case 'skeleton':
        return renderSkeletonLoading();
      case 'dots':
        return renderDotsLoading();
      case 'circular':
      default:
        return renderCircularLoading();
    }
  };

  if (overlay) {
    return (
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {renderContent()}
      </Box>
    );
  }

  return renderContent();
};

/**
 * 表格加载状态组件
 */
export const TableLoadingState: React.FC<{ rows?: number; columns?: number }> = ({
  rows = 5,
  columns = 6
}) => (
  <Box sx={{ p: 2 }}>
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <Box key={rowIndex} sx={{ display: 'flex', gap: 2, mb: 1 }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton
            key={colIndex}
            variant="text"
            width={`${100 / columns}%`}
            height={40}
          />
        ))}
      </Box>
    ))}
  </Box>
);

/**
 * 卡片加载状态组件
 */
export const CardLoadingState: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
    {Array.from({ length: count }).map((_, index) => (
      <Paper key={index} sx={{ p: 2 }}>
        <Skeleton variant="text" width="40%" height={32} />
        <Skeleton variant="text" width="100%" height={24} />
        <Skeleton variant="text" width="80%" height={24} />
        <Skeleton variant="rectangular" width="100%" height={100} sx={{ mt: 1 }} />
      </Paper>
    ))}
  </Box>
);

export default LoadingState;
