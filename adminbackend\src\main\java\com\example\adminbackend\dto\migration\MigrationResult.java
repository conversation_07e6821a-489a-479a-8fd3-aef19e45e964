package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据迁移结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationResult {
    
    /**
     * 迁移是否成功
     */
    private boolean success;
    
    /**
     * 总数据量
     */
    private long totalCount;
    
    /**
     * 成功数量
     */
    private long successCount;
    
    /**
     * 失败数量
     */
    private long errorCount;
    
    /**
     * 跳过数量（现用作更新数量）
     */
    private long skipCount;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 耗时（毫秒）
     */
    private long durationMs;
    
    /**
     * 错误信息列表
     */
    private List<String> errors;
    
    /**
     * 详细统计信息
     */
    private Map<String, Object> statistics;
    
    /**
     * 迁移消息
     */
    private String message;
    
    /**
     * 批次结果列表
     */
    private List<BatchResult> batchResults;
    
    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }
    
    /**
     * 获取处理速度（条/秒）
     */
    public double getProcessingSpeed() {
        if (durationMs == 0) {
            return 0.0;
        }
        return (double) totalCount / (durationMs / 1000.0);
    }
}
