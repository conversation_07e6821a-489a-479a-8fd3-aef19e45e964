import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  MenuItem,
  Snackbar,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import UserForm from '../components/UserForm';
import { User } from '../types';
import { supervisorAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const SupervisorManagePage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  
  // 搜索和筛选
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  
  // 对话框状态
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  // 消息提示
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // 显示消息提示
  const showSnackbar = useCallback((message: string, severity: 'success' | 'error' = 'success') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  // 加载督学数据 - 只有管理员可以管理督学
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const supervisors = await supervisorAPI.getAllSupervisors(); // 使用督学专用接口
      setUsers(supervisors);
    } catch (error) {
      console.error('获取督学列表失败:', error);
      showSnackbar('获取督学列表失败', 'error');
    } finally {
      setLoading(false);
    }
  }, [showSnackbar]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // 过滤督学数据
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const matchesSearch = searchTerm === '' || 
        (user.fullName && user.fullName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.username && user.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.phone && user.phone.includes(searchTerm));
      
      const matchesStatus = statusFilter === null || 
        (statusFilter === 'active' && user.enabled) ||
        (statusFilter === 'inactive' && !user.enabled);
      
      return matchesSearch && matchesStatus;
    });
  }, [users, searchTerm, statusFilter]);

  // 处理添加督学
  const handleAdd = () => {
    setSelectedUser(null);
    setFormDialogOpen(true);
  };

  // 处理编辑督学
  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setFormDialogOpen(true);
  };

  // 处理删除督学
  const handleDelete = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  // 确认删除督学
  const confirmDelete = async () => {
    if (!selectedUser) return;

    try {
      await supervisorAPI.deleteSupervisor(selectedUser.id); // 使用督学专用接口
      showSnackbar('删除督学成功');
      fetchUsers();
    } catch (error: any) {
      console.error('删除督学失败:', error);
      showSnackbar(error.response?.data?.message || '删除督学失败', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setSelectedUser(null);
    }
  };

  // 处理表单提交
  const handleFormSubmit = () => {
    setFormDialogOpen(false);
    setSelectedUser(null);
    fetchUsers();
    showSnackbar(selectedUser ? '更新督学成功' : '添加督学成功');
  };

  // 处理分页
  const handlePageChange = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Layout>
      <Box>
        {/* 页面标题 */}
        <Typography variant="h4" gutterBottom fontWeight="bold">
          督学账号管理
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          管理督学账号的创建、编辑和删除
        </Typography>

        {/* 操作栏 */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="搜索督学..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
              }}
              sx={{ minWidth: 250 }}
            />
            
            <Button 
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={(e) => setFilterAnchorEl(e.currentTarget)}
            >
              筛选
              {statusFilter && (
                <Chip 
                  label={1} 
                  size="small" 
                  color="primary"
                  sx={{ ml: 1, height: 16, minWidth: 16 }}
                />
              )}
            </Button>
            
            <Box sx={{ flexGrow: 1 }} />
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
            >
              添加督学
            </Button>
          </Box>
        </Paper>

        {/* 督学列表 */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>姓名</TableCell>
                <TableCell>用户名</TableCell>
                <TableCell>手机号</TableCell>
                <TableCell>状态</TableCell>
                <TableCell>创建时间</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography color="text.secondary">
                      暂无督学数据
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.slice(page * pageSize, (page + 1) * pageSize).map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>{user.fullName}</TableCell>
                    <TableCell>{user.username}</TableCell>
                    <TableCell>{user.phone}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.enabled ? '启用' : '禁用'}
                        color={user.enabled ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {user.createdAt ? new Date(user.createdAt).toLocaleDateString('zh-CN') : '-'}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton onClick={() => handleEdit(user)} size="small">
                        <EditIcon />
                      </IconButton>
                      <IconButton onClick={() => handleDelete(user)} size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          
          <TablePagination
            component="div"
            count={filteredUsers.length}
            page={page}
            onPageChange={handlePageChange}
            rowsPerPage={pageSize}
            onRowsPerPageChange={handlePageSizeChange}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage="每页行数:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
          />
        </TableContainer>

        {/* 筛选菜单 */}
        <Menu
          anchorEl={filterAnchorEl}
          open={Boolean(filterAnchorEl)}
          onClose={() => setFilterAnchorEl(null)}
        >
          <MenuItem onClick={() => { setStatusFilter(null); setFilterAnchorEl(null); }}>
            全部状态
          </MenuItem>
          <MenuItem onClick={() => { setStatusFilter('active'); setFilterAnchorEl(null); }}>
            启用
          </MenuItem>
          <MenuItem onClick={() => { setStatusFilter('inactive'); setFilterAnchorEl(null); }}>
            禁用
          </MenuItem>
        </Menu>

        {/* 添加/编辑督学对话框 */}
        <Dialog open={formDialogOpen} onClose={() => setFormDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {selectedUser ? '编辑督学' : '添加督学'}
          </DialogTitle>
          <DialogContent>
            <UserForm
              user={selectedUser}
              onSubmit={handleFormSubmit}
              onCancel={() => setFormDialogOpen(false)}
              supervisorMode={true}
            />
          </DialogContent>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>确认删除</DialogTitle>
          <DialogContent>
            <Typography>
              确定要删除督学 "{selectedUser?.fullName}" 吗？此操作不可撤销。
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
            <Button onClick={confirmDelete} color="error" variant="contained">
              删除
            </Button>
          </DialogActions>
        </Dialog>

        {/* 消息提示 */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default SupervisorManagePage;
