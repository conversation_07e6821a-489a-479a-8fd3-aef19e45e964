package com.example.adminbackend.repository;

import com.example.adminbackend.model.Subject;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 科目数据访问层
 */
@Repository
public interface SubjectRepository extends JpaRepository<Subject, Long> {

    /**
     * 根据名称查找科目
     */
    Optional<Subject> findByName(String name);

    /**
     * 根据科目名称查找所有匹配的科目（用于处理重复数据）
     */
    List<Subject> findAllByName(String name);

    /**
     * 检查科目名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 获取所有科目，按创建时间排序
     */
    List<Subject> findAllByOrderByCreatedAtAsc();

    /**
     * 获取所有科目，按名称排序
     */
    List<Subject> findAllByOrderByNameAsc();

    /**
     * 查询包含指定关键词的科目
     */
    @Query("SELECT s FROM Subject s WHERE s.name LIKE %:keyword% OR s.description LIKE %:keyword%")
    List<Subject> findByKeyword(String keyword);

    /**
     * 获取有版本的科目列表
     */
    @Query("SELECT DISTINCT s FROM Subject s JOIN s.versions v")
    List<Subject> findSubjectsWithVersions();
}
