package com.example.adminbackend.model.question;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果类
 * 用于存储题目数据验证的结果信息
 */
@Data
@NoArgsConstructor
public class ValidationResult {
    
    /**
     * 是否验证通过
     */
    private boolean valid = true;
    
    /**
     * 错误信息列表
     */
    private List<String> errors = new ArrayList<>();
    
    /**
     * 警告信息列表
     */
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(String error) {
        this.errors.add(error);
        this.valid = false;
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    /**
     * 是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 获取所有错误信息的字符串表示
     * 
     * @return 错误信息字符串
     */
    public String getErrorsAsString() {
        return String.join("; ", errors);
    }
    
    /**
     * 获取所有警告信息的字符串表示
     * 
     * @return 警告信息字符串
     */
    public String getWarningsAsString() {
        return String.join("; ", warnings);
    }
    
    /**
     * 创建一个成功的验证结果
     * 
     * @return 成功的验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult();
    }
    
    /**
     * 创建一个失败的验证结果
     * 
     * @param error 错误信息
     * @return 失败的验证结果
     */
    public static ValidationResult failure(String error) {
        ValidationResult result = new ValidationResult();
        result.addError(error);
        return result;
    }
}
