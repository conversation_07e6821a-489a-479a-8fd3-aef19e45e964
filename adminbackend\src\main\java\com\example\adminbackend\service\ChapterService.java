package com.example.adminbackend.service;

import com.example.adminbackend.model.Chapter;
import com.example.adminbackend.dto.ChapterDTO;

import java.util.List;

/**
 * 章节服务接口
 */
public interface ChapterService {

    /**
     * 获取所有章节
     */
    List<Chapter> getAllChapters();

    /**
     * 根据ID获取章节
     */
    Chapter getChapterById(Long id);

    /**
     * 根据科目版本ID获取所有章节
     */
    List<com.example.adminbackend.dto.ChapterDTO> getChaptersBySubjectVersionId(Long subjectVersionId);

    /**
     * 根据科目版本ID和章节名称获取章节
     */
    Chapter getChapterBySubjectVersionIdAndName(Long subjectVersionId, String name);

    /**
     * 根据科目ID获取所有章节
     */
    List<Chapter> getChaptersBySubjectId(Long subjectId);

    /**
     * 创建章节
     */
    Chapter createChapter(Chapter chapter);

    /**
     * 更新章节
     */
    Chapter updateChapter(Long id, Chapter chapter);

    /**
     * 删除章节（简单删除，不处理关联知识点）
     */
    void deleteChapter(Long id);

    /**
     * 级联删除章节及其所有知识点
     * @param id 章节ID
     * @return 删除的文件数量
     */
    int deleteChapterCascade(Long id);

    /**
     * 检查科目版本下是否存在指定名称的章节
     */
    boolean existsBySubjectVersionIdAndName(Long subjectVersionId, String name);

    /**
     * 根据关键词搜索章节
     */
    List<Chapter> searchChapters(String keyword);

    /**
     * 获取有知识点的章节列表
     */
    List<Chapter> getChaptersWithKnowledgePoints();

    /**
     * 获取科目版本下章节的最大顺序索引
     */
    Integer getMaxOrderIndexBySubjectVersionId(Long subjectVersionId);

    /**
     * 根据科目ID获取所有章节，返回DTO（避免懒加载问题）
     */
    List<ChapterDTO> getChaptersBySubjectIdAsDTO(Long subjectId);

    /**
     * 获取有知识点的章节列表，返回DTO（避免懒加载问题）
     */
    List<ChapterDTO> getChaptersWithKnowledgePointsAsDTO();

    /**
     * 根据ID获取章节，返回DTO（避免懒加载问题）
     */
    ChapterDTO getChapterByIdAsDTO(Long id);

    /**
     * 搜索章节，返回DTO（避免懒加载问题）
     */
    List<ChapterDTO> searchChaptersAsDTO(String keyword);

    /**
     * 获取所有章节，返回DTO（避免懒加载问题）
     */
    List<ChapterDTO> getAllChaptersAsDTO();
}
