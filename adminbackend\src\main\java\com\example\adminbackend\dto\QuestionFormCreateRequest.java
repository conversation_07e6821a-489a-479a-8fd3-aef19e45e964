package com.example.adminbackend.dto;

import com.example.adminbackend.model.Question;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 专门为前端创建题目表单设计的请求DTO
 * 自动处理科目映射和数据构建
 */
@Data
public class QuestionFormCreateRequest {

    @NotNull(message = "知识点ID不能为空")
    private Long knowledgePointId;

    @NotNull(message = "题目类型不能为空")
    private Question.QuestionType questionType;

    @NotBlank(message = "题目内容不能为空")
    private String content;

    // 答案字段支持多种类型：字符串、数组等
    private Object answer;

    private String explanation;

    private String difficulty = "EASY";

    private List<String> tags;

    private List<String> options;

    private String material;

    private List<Map<String, Object>> subQuestions;

    private Boolean enabled = true;
}
