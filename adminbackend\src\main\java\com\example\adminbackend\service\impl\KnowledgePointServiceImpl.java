package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.KnowledgePoint;
import com.example.adminbackend.model.VideoCollection;
import com.example.adminbackend.model.CollectionVideo;
import com.example.adminbackend.dto.KnowledgePointDTO;
import com.example.adminbackend.dto.KnowledgePointHierarchyInfo;
import com.example.adminbackend.repository.KnowledgePointRepository;
import com.example.adminbackend.repository.StudentProgressRepository;
import com.example.adminbackend.repository.StudentAnswerRepository;
import com.example.adminbackend.repository.LegacyDataCleanupRepository;
import com.example.adminbackend.repository.QuestionRepository;
import com.example.adminbackend.service.KnowledgePointService;
import com.example.adminbackend.service.VideoCollectionService;
import com.example.adminbackend.service.QiniuUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 知识点服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KnowledgePointServiceImpl implements KnowledgePointService {

    private final KnowledgePointRepository knowledgePointRepository;
    private final VideoCollectionService videoCollectionService;
    private final QiniuUploadService qiniuUploadService;
    private final StudentProgressRepository studentProgressRepository;
    private final StudentAnswerRepository studentAnswerRepository;
    private final LegacyDataCleanupRepository legacyDataCleanupRepository;
    private final QuestionRepository questionRepository;

    @Override
    public List<KnowledgePoint> getAllKnowledgePoints() {
        log.info("获取所有知识点");
        return knowledgePointRepository.findAll();
    }

    @Override
    public KnowledgePoint getKnowledgePointById(Long id) {
        log.info("根据ID获取知识点: {}", id);
        return knowledgePointRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("知识点不存在: " + id));
    }

    @Override
    public List<KnowledgePoint> getKnowledgePointsByChapterId(Long chapterId) {
        log.info("根据章节ID获取知识点: {}", chapterId);
        return knowledgePointRepository.findByChapterIdOrderByOrderIndexAsc(chapterId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<KnowledgePointDTO> getKnowledgePointDTOsByChapterId(Long chapterId) {
        log.info("根据章节ID获取知识点DTO: {}", chapterId);
        // 使用预加载视频合集信息的查询，避免懒加载异常
        List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdWithVideoCollectionOrderByOrderIndexAsc(chapterId);

        return knowledgePoints.stream().map(kp -> {
            KnowledgePointDTO dto = new KnowledgePointDTO();
            dto.setId(kp.getId());
            dto.setName(kp.getName());
            dto.setOrderIndex(kp.getOrderIndex());
            dto.setCoverImageUrl(kp.getCoverImageUrl());
            dto.setDescription(kp.getDescription());
            dto.setEnabled(kp.getEnabled());
            dto.setCreatedAt(kp.getCreatedAt());
            dto.setUpdatedAt(kp.getUpdatedAt());
            dto.setChapterId(kp.getChapter().getId());

            // 现在可以安全地访问视频合集信息，因为已经预加载了
            if (kp.getVideoCollection() != null) {
                dto.setVideoCollectionId(kp.getVideoCollection().getId());
                dto.setVideoCollectionName(kp.getVideoCollection().getName());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public KnowledgePointHierarchyInfo getKnowledgePointHierarchy(Long knowledgePointId) {
        log.info("根据知识点ID获取层级信息: {}", knowledgePointId);

        // 使用预加载查询避免懒加载异常
        KnowledgePoint knowledgePoint = knowledgePointRepository.findByIdWithHierarchy(knowledgePointId)
                .orElseThrow(() -> new RuntimeException("知识点不存在: " + knowledgePointId));

        KnowledgePointHierarchyInfo hierarchyInfo = new KnowledgePointHierarchyInfo();

        // 设置知识点信息
        hierarchyInfo.setKnowledgePointId(knowledgePoint.getId());
        hierarchyInfo.setKnowledgePointName(knowledgePoint.getName());

        // 设置章节信息
        if (knowledgePoint.getChapter() != null) {
            hierarchyInfo.setChapterId(knowledgePoint.getChapter().getId());
            hierarchyInfo.setChapterName(knowledgePoint.getChapter().getName());

            // 设置科目版本信息
            if (knowledgePoint.getChapter().getSubjectVersion() != null) {
                hierarchyInfo.setSubjectVersionId(knowledgePoint.getChapter().getSubjectVersion().getId());
                hierarchyInfo.setSubjectVersionName(knowledgePoint.getChapter().getSubjectVersion().getName());

                // 设置科目信息
                if (knowledgePoint.getChapter().getSubjectVersion().getSubject() != null) {
                    hierarchyInfo.setSubjectId(knowledgePoint.getChapter().getSubjectVersion().getSubject().getId());
                    hierarchyInfo.setSubjectName(knowledgePoint.getChapter().getSubjectVersion().getSubject().getName());
                }
            }
        }

        return hierarchyInfo;
    }

    @Override
    public KnowledgePoint getKnowledgePointByChapterIdAndName(Long chapterId, String name) {
        log.info("根据章节ID和名称获取知识点: chapterId={}, name={}", chapterId, name);
        List<KnowledgePoint> results = knowledgePointRepository.findAllByChapterIdAndName(chapterId, name);
        if (results.isEmpty()) {
            throw new RuntimeException("知识点不存在: " + name);
        }
        if (results.size() > 1) {
            log.warn("发现重复的知识点记录: chapterId={}, name={}, 数量={}, 使用第一个", chapterId, name, results.size());
        }
        return results.get(0);
    }

    @Override
    public List<KnowledgePoint> getKnowledgePointsBySubjectVersionId(Long subjectVersionId) {
        log.info("根据科目版本ID获取知识点: {}", subjectVersionId);
        return knowledgePointRepository.findBySubjectVersionId(subjectVersionId);
    }

    @Override
    public List<KnowledgePoint> getKnowledgePointsBySubjectId(Long subjectId) {
        log.info("根据科目ID获取知识点: {}", subjectId);
        return knowledgePointRepository.findBySubjectId(subjectId);
    }

    @Override
    @Transactional
    public KnowledgePoint createKnowledgePoint(KnowledgePoint knowledgePoint) {
        log.info("创建知识点: {}", knowledgePoint.getName());
        
        // 检查同一章节下名称是否已存在
        if (knowledgePointRepository.existsByChapterIdAndName(
                knowledgePoint.getChapter().getId(), knowledgePoint.getName())) {
            throw new RuntimeException("知识点名称已存在: " + knowledgePoint.getName());
        }
        
        // 如果没有设置顺序索引，自动设置为最大值+1
        if (knowledgePoint.getOrderIndex() == null) {
            Integer maxOrderIndex = knowledgePointRepository.findMaxOrderIndexByChapterId(
                    knowledgePoint.getChapter().getId());
            knowledgePoint.setOrderIndex(maxOrderIndex == null ? 1 : maxOrderIndex + 1);
        } else {
            // 检查顺序索引是否已存在
            if (knowledgePointRepository.existsByChapterIdAndOrderIndex(
                    knowledgePoint.getChapter().getId(), knowledgePoint.getOrderIndex())) {
                throw new RuntimeException("知识点顺序索引已存在: " + knowledgePoint.getOrderIndex());
            }
        }
        
        return knowledgePointRepository.save(knowledgePoint);
    }

    @Override
    @Transactional
    public KnowledgePoint updateKnowledgePoint(Long id, KnowledgePoint knowledgePoint) {
        log.info("更新知识点: {}", id);
        
        KnowledgePoint existingKnowledgePoint = getKnowledgePointById(id);

        // 确定目标章节ID（如果提供了新章节，使用新章节；否则保持原章节）
        Long targetChapterId = knowledgePoint.getChapter() != null ?
            knowledgePoint.getChapter().getId() : existingKnowledgePoint.getChapter().getId();

        // 检查名称是否与目标章节下其他知识点冲突
        if (!existingKnowledgePoint.getName().equals(knowledgePoint.getName()) &&
            knowledgePointRepository.existsByChapterIdAndName(targetChapterId, knowledgePoint.getName())) {
            throw new RuntimeException("知识点名称已存在: " + knowledgePoint.getName());
        }

        // 检查顺序索引是否与目标章节下其他知识点冲突
        if (!existingKnowledgePoint.getOrderIndex().equals(knowledgePoint.getOrderIndex()) &&
            knowledgePointRepository.existsByChapterIdAndOrderIndex(targetChapterId, knowledgePoint.getOrderIndex())) {
            throw new RuntimeException("知识点顺序索引已存在: " + knowledgePoint.getOrderIndex());
        }

        // 更新基本字段
        existingKnowledgePoint.setName(knowledgePoint.getName());
        existingKnowledgePoint.setOrderIndex(knowledgePoint.getOrderIndex());
        existingKnowledgePoint.setCoverImageUrl(knowledgePoint.getCoverImageUrl());
        existingKnowledgePoint.setDescription(knowledgePoint.getDescription());
        existingKnowledgePoint.setEnabled(knowledgePoint.getEnabled());

        // 更新章节关联（如果提供了新章节）
        if (knowledgePoint.getChapter() != null) {
            existingKnowledgePoint.setChapter(knowledgePoint.getChapter());
        }
        
        return knowledgePointRepository.save(existingKnowledgePoint);
    }

    @Override
    @Transactional
    public void deleteKnowledgePoint(Long id) {
        log.info("删除知识点: {}", id);

        KnowledgePoint knowledgePoint = getKnowledgePointById(id);
        knowledgePointRepository.delete(knowledgePoint);
    }

    @Override
    @Transactional
    public int deleteKnowledgePointCascade(Long id) {
        log.info("级联删除知识点: {}", id);

        KnowledgePoint knowledgePoint = getKnowledgePointById(id);

        // 1. 先删除外键关联记录（按照外键依赖顺序删除）
        log.info("开始清理知识点 {} 的外键关联记录", id);

        // 删除学生答题记录（必须在删除题目之前，因为student_answers引用questions）
        long answerCount = studentAnswerRepository.countByKnowledgePointId(id);
        if (answerCount > 0) {
            log.info("删除 {} 条学生答题记录", answerCount);
            studentAnswerRepository.deleteByKnowledgePointId(id);
        }

        // 删除题目记录
        long questionCount = questionRepository.countByKnowledgePointId(id);
        if (questionCount > 0) {
            log.info("删除 {} 条题目记录", questionCount);
            questionRepository.deleteByKnowledgePointId(id);
        }

        // 删除学生进度记录
        long progressCount = studentProgressRepository.countByKnowledgePointId(id);
        if (progressCount > 0) {
            log.info("删除 {} 条学生进度记录", progressCount);
            studentProgressRepository.deleteByKnowledgePointId(id);
        }

        // 清理遗留数据（如learning_steps表）
        int legacyDeleted = legacyDataCleanupRepository.cleanupKnowledgePointLegacyData(id);
        if (legacyDeleted > 0) {
            log.info("清理 {} 条遗留数据记录", legacyDeleted);
        }

        List<String> filesToDelete = new ArrayList<>();

        // 2. 收集封面图片URL
        if (knowledgePoint.getCoverImageUrl() != null && !knowledgePoint.getCoverImageUrl().trim().isEmpty()) {
            filesToDelete.add(knowledgePoint.getCoverImageUrl());
            log.info("准备删除知识点封面图片: {}", knowledgePoint.getCoverImageUrl());
        }

        // 3. 收集视频文件URL并删除视频合集
        VideoCollection videoCollection = knowledgePoint.getVideoCollection();
        if (videoCollection != null) {
            log.info("准备删除知识点 {} 的视频合集: {}", id, videoCollection.getName());

            // 收集视频URL
            List<CollectionVideo> videos = videoCollection.getVideos();
            if (videos != null) {
                for (CollectionVideo cv : videos) {
                    if (cv.getVideo() != null && cv.getVideo().getVideoUrl() != null) {
                        filesToDelete.add(cv.getVideo().getVideoUrl());
                    }
                }
            }

            // 删除视频合集
            try {
                videoCollectionService.deleteVideoCollection(videoCollection.getId());
                log.info("视频合集删除成功: ID={}", videoCollection.getId());
            } catch (Exception e) {
                log.warn("删除视频合集失败: ID={}, 错误: {}", videoCollection.getId(), e.getMessage());
            }
        }

        // 4. 删除知识点
        knowledgePointRepository.delete(knowledgePoint);

        // 5. 删除七牛云文件
        int deletedCount = 0;
        if (!filesToDelete.isEmpty()) {
            try {
                log.info("开始删除七牛云文件，数量: {}", filesToDelete.size());
                deletedCount = qiniuUploadService.deleteFiles(filesToDelete);
                log.info("七牛云文件删除完成，成功删除: {} 个", deletedCount);
            } catch (Exception e) {
                log.warn("删除七牛云文件失败: {}", e.getMessage());
            }
        }

        log.info("知识点级联删除完成: {} (ID: {}), 删除文件数: {}", knowledgePoint.getName(), id, deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByChapterIdAndName(Long chapterId, String name) {
        return knowledgePointRepository.existsByChapterIdAndName(chapterId, name);
    }

    @Override
    @Transactional(readOnly = true)
    public List<KnowledgePoint> searchKnowledgePoints(String keyword) {
        log.info("搜索知识点: {}", keyword);
        return knowledgePointRepository.findByNameContaining(keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getMaxOrderIndexByChapterId(Long chapterId) {
        return knowledgePointRepository.findMaxOrderIndexByChapterId(chapterId);
    }

    @Override
    public long countByChapterId(Long chapterId) {
        return knowledgePointRepository.countByChapterId(chapterId);
    }

    @Override
    public long countBySubjectVersionId(Long subjectVersionId) {
        return knowledgePointRepository.countBySubjectVersionId(subjectVersionId);
    }

    @Override
    public long countBySubjectId(Long subjectId) {
        return knowledgePointRepository.countBySubjectId(subjectId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<KnowledgePointDTO> getAllKnowledgePointsAsDTO() {
        log.info("获取所有知识点（DTO格式）");
        List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findAll();

        return knowledgePoints.stream().map(kp -> {
            KnowledgePointDTO dto = new KnowledgePointDTO();
            dto.setId(kp.getId());
            dto.setName(kp.getName());
            dto.setOrderIndex(kp.getOrderIndex());
            dto.setCoverImageUrl(kp.getCoverImageUrl());
            dto.setDescription(kp.getDescription());
            dto.setEnabled(kp.getEnabled());
            dto.setCreatedAt(kp.getCreatedAt());
            dto.setUpdatedAt(kp.getUpdatedAt());

            // 安全访问chapter关联，避免懒加载异常
            if (kp.getChapter() != null) {
                dto.setChapterId(kp.getChapter().getId());
            }

            // 安全访问视频合集信息
            if (kp.getVideoCollection() != null) {
                dto.setVideoCollectionId(kp.getVideoCollection().getId());
                dto.setVideoCollectionName(kp.getVideoCollection().getName());
            }

            return dto;
        }).collect(Collectors.toList());
    }
}
