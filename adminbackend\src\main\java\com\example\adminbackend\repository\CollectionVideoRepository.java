package com.example.adminbackend.repository;

import com.example.adminbackend.model.CollectionVideo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 视频合集-视频关联数据访问层
 */
@Repository
public interface CollectionVideoRepository extends JpaRepository<CollectionVideo, Long> {

    /**
     * 根据合集ID查找所有关联的视频
     */
    List<CollectionVideo> findByCollectionId(Long collectionId);

    /**
     * 根据视频ID查找所有关联的合集
     */
    List<CollectionVideo> findByVideoId(Long videoId);

    /**
     * 根据合集ID和视频ID查找关联记录
     */
    Optional<CollectionVideo> findByCollectionIdAndVideoId(Long collectionId, Long videoId);

    /**
     * 检查合集和视频的关联是否存在
     */
    boolean existsByCollectionIdAndVideoId(Long collectionId, Long videoId);

    /**
     * 根据合集ID删除所有关联记录
     */
    void deleteByCollectionId(Long collectionId);

    /**
     * 根据视频ID删除所有关联记录
     */
    void deleteByVideoId(Long videoId);

    /**
     * 统计合集中的视频数量
     */
    @Query("SELECT COUNT(cv) FROM CollectionVideo cv WHERE cv.collection.id = :collectionId")
    long countByCollectionId(@Param("collectionId") Long collectionId);

    /**
     * 统计视频被多少个合集使用
     */
    @Query("SELECT COUNT(cv) FROM CollectionVideo cv WHERE cv.video.id = :videoId")
    long countByVideoId(@Param("videoId") Long videoId);

    /**
     * 获取合集中的所有视频，按创建时间排序
     */
    @Query("SELECT cv FROM CollectionVideo cv WHERE cv.collection.id = :collectionId ORDER BY cv.createdAt ASC")
    List<CollectionVideo> findByCollectionIdOrderByCreatedAtAsc(@Param("collectionId") Long collectionId);
}
