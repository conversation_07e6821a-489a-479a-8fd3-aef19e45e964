package com.example.adminbackend.service.migration.impl;

import com.example.adminbackend.dto.migration.*;
import com.example.adminbackend.exception.MigrationException;
import com.example.adminbackend.model.Question;
import com.example.adminbackend.repository.QuestionRepository;
import com.example.adminbackend.service.migration.*;
import com.example.adminbackend.util.JsonValidationUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 题目数据迁移服务实现类
 */
@Service
@Lazy
@RequiredArgsConstructor
@Slf4j
public class QuestionMigrationServiceImpl implements QuestionMigrationService {
    
    private final QuestionRepository questionRepository;
    private final QuestionDataConverter dataConverter;
    private final HierarchyDataProcessor hierarchyProcessor;
    private final MigrationValidator migrationValidator;
    private final EntityManager entityManager;
    private final JsonValidationUtil jsonValidationUtil;
    
    @Value("${migration.batch-size:1000}")
    private int batchSize;
    
    @Value("${migration.thread-pool-size:4}")
    private int threadPoolSize;
    
    private ExecutorService executorService;
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicBoolean migrationRunning = new AtomicBoolean(false);
    private volatile MigrationResult currentMigrationResult;
    
    /**
     * 主迁移方法
     */
    @Override
    @Transactional
    public MigrationResult migrateQuestions(List<SourceQuestionData> sourceData) {
        if (migrationRunning.get()) {
            throw new MigrationException("迁移任务正在进行中，请等待完成或停止当前任务");
        }
        
        migrationRunning.set(true);
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            log.info("开始题目数据迁移，数据量: {}", sourceData.size());
            
            // 初始化线程池
            initializeExecutorService();
            
            // 重置计数器
            processedCount.set(0);
            errorCount.set(0);
            
            // 1. 预处理层级数据
            hierarchyProcessor.preprocessHierarchyData(sourceData);
            log.info("层级数据预处理完成");
            
            // 2. 分批处理题目数据
            List<List<SourceQuestionData>> batches = partitionData(sourceData, batchSize);
            log.info("数据分批完成，共 {} 批，每批 {} 条", batches.size(), batchSize);
            
            // 3. 并发处理各批次
            List<CompletableFuture<BatchResult>> futures = new ArrayList<>();
            for (int i = 0; i < batches.size(); i++) {
                final int batchNumber = i + 1;
                final List<SourceQuestionData> batch = batches.get(i);
                
                CompletableFuture<BatchResult> future = CompletableFuture.supplyAsync(
                    () -> processBatch(batchNumber, batch), executorService);
                futures.add(future);
            }
            
            // 4. 等待所有批次完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            // 5. 汇总结果
            MigrationResult result = aggregateResults(futures, sourceData.size(), startTime);
            currentMigrationResult = result;

            // 自动清理缓存，确保下次迁移使用最新数据
            hierarchyProcessor.clearCache();
            log.info("批量迁移完成后自动清理缓存");

            log.info("迁移完成，成功: {}, 失败: {}", result.getSuccessCount(), result.getErrorCount());
            return result;
            
        } catch (Exception e) {
            log.error("迁移过程发生异常", e);
            throw new MigrationException("数据迁移失败", e);
        } finally {
            migrationRunning.set(false);
            shutdownExecutorService();
        }
    }
    
    /**
     * 处理单个批次
     */
    private BatchResult processBatch(int batchNumber, List<SourceQuestionData> batch) {
        LocalDateTime batchStartTime = LocalDateTime.now();
        BatchResult result = BatchResult.builder()
            .batchNumber(batchNumber)
            .batchSize(batch.size())
            .startTime(batchStartTime)
            .build();
        
        List<Question> questionsToSave = new ArrayList<>();
        
        for (SourceQuestionData sourceQuestion : batch) {
            try {
                // 1. 数据验证
                ValidationResult validation = migrationValidator.validateSourceData(sourceQuestion);
                if (!validation.isValid()) {
                    result.addError(sourceQuestion.getId().toString(), "数据验证失败: " + validation.getErrorMessage());
                    errorCount.incrementAndGet();
                    continue;
                }
                
                // 2. 数据转换
                Question targetQuestion = dataConverter.convertToTargetFormat(sourceQuestion);

                // 3. 业务验证
                ValidationResult businessValidation = migrationValidator.validateTargetData(targetQuestion);
                if (!businessValidation.isValid()) {
                    result.addError(sourceQuestion.getId().toString(), "业务验证失败: " + businessValidation.getErrorMessage());
                    errorCount.incrementAndGet();
                    continue;
                }

                // 4. 重复检查
                Question existingQuestion = findExistingQuestion(targetQuestion);
                if (existingQuestion != null) {
                    log.warn("⚠️ 批量迁移发现重复题目，执行更新操作: 源题目ID={}, 知识点ID={}, 题目类型={}, 现有题目ID={}",
                        sourceQuestion.getId(), targetQuestion.getKnowledgePointId(), targetQuestion.getQuestionType(), existingQuestion.getId());

                    // 输出更新提示到控制台
                    System.out.println("=== 🔄 批量迁移重复题目更新 ===");
                    System.out.println("批次号: " + batchNumber);
                    System.out.println("源题目ID: " + sourceQuestion.getId());
                    System.out.println("题目类型: " + targetQuestion.getQuestionType());
                    System.out.println("知识点ID: " + targetQuestion.getKnowledgePointId());
                    System.out.println("现有题目ID: " + existingQuestion.getId());
                    System.out.println("操作: 更新现有题目数据");
                    System.out.println("===============================");

                    // 执行更新操作
                    try {
                        Question updatedQuestion = updateExistingQuestion(existingQuestion, targetQuestion);
                        result.addSuccessId(sourceQuestion.getId().toString() + " (更新)");
                        result.incrementSuccess();
                    } catch (Exception updateException) {
                        log.error("更新题目失败: 源题目ID={}, 现有题目ID={}",
                            sourceQuestion.getId(), existingQuestion.getId(), updateException);
                        result.addError(sourceQuestion.getId().toString(), "更新失败: " + updateException.getMessage());
                        errorCount.incrementAndGet();
                    }
                    continue;
                }

                questionsToSave.add(targetQuestion);
                result.incrementSuccess();
                result.addSuccessId(sourceQuestion.getId().toString());
                
            } catch (Exception e) {
                log.error("处理题目数据失败，ID: {}", sourceQuestion.getId(), e);
                result.addError(sourceQuestion.getId().toString(), e.getMessage());
                errorCount.incrementAndGet();
            }
        }
        
        // 批量保存
        if (!questionsToSave.isEmpty()) {
            try {
                batchSaveQuestions(questionsToSave);
                log.info("批次 {} 保存完成，成功保存 {} 条数据", batchNumber, questionsToSave.size());
            } catch (Exception e) {
                log.error("批次 {} 保存失败", batchNumber, e);
                questionsToSave.forEach(q -> {
                    String questionId = (String) q.getBody().get("id");
                    result.addError(questionId, "保存失败: " + e.getMessage());
                });
                errorCount.addAndGet(questionsToSave.size());
            }
        }
        
        LocalDateTime batchEndTime = LocalDateTime.now();
        result.setEndTime(batchEndTime);
        result.setDurationMs(java.time.Duration.between(batchStartTime, batchEndTime).toMillis());
        
        processedCount.addAndGet(batch.size());
        
        return result;
    }
    
    /**
     * 批量保存题目
     */
    @Transactional
    private void batchSaveQuestions(List<Question> questions) {
        for (int i = 0; i < questions.size(); i += 100) {
            List<Question> subBatch = questions.subList(i, Math.min(i + 100, questions.size()));
            questionRepository.saveAll(subBatch);
            
            // 每100条清理一次持久化上下文
            if (i % 100 == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
    }
    
    /**
     * 从题目对象迁移单个题目
     */
    @Override
    public MigrationResult migrateQuestionFromObject(SourceQuestionData questionData) {
        try {
            log.info("开始迁移单个题目，题目ID: {}", questionData.getId());

            // 直接处理单个题目
            return migrateSingleQuestion(questionData);
        } catch (Exception e) {
            log.error("题目对象迁移失败", e);
            throw new MigrationException("题目对象迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从JSON字符串迁移单个题目
     */
    @Override
    public MigrationResult migrateQuestionFromJsonString(String jsonData) {
        try {
            // 使用JsonValidationUtil进行详细验证
            JsonValidationUtil.JsonValidationResult validationResult = jsonValidationUtil.validateJsonFormat(jsonData);

            if (!validationResult.isValid()) {
                throw new MigrationException(validationResult.getMessage());
            }

            log.info("JSON格式验证通过");

            // 使用JsonValidationUtil解析单个JSON对象
            List<SourceQuestionData> questionList = jsonValidationUtil.parseMultipleJsonObjects(jsonData);

            if (questionList.isEmpty()) {
                throw new MigrationException("JSON数据解析失败，未找到有效的题目数据");
            }

            if (questionList.size() > 1) {
                throw new MigrationException("此方法仅支持单个题目，检测到多个题目，请使用批量迁移接口");
            }

            return migrateSingleQuestion(questionList.get(0));
        } catch (MigrationException e) {
            // 重新抛出MigrationException
            throw e;
        } catch (Exception e) {
            log.error("JSON字符串迁移失败", e);
            String detailedError = buildDetailedMigrationError(jsonData, e);
            throw new MigrationException(detailedError, e);
        }
    }

    /**
     * 迁移单个题目
     */
    private MigrationResult migrateSingleQuestion(SourceQuestionData questionData) {
        LocalDateTime startTime = LocalDateTime.now();

        try {
            log.info("开始处理题目: ID={}, 类型={}", questionData.getId(), questionData.getType());

            // 验证题目数据
            ValidationResult validation = migrationValidator.validateSourceData(questionData);
            if (!validation.isValid()) {
                throw new MigrationException("题目数据验证失败: " + validation.getErrorMessage());
            }

            // 处理层级数据
            hierarchyProcessor.processHierarchy(questionData.getBasicInfo());

            // 转换为Question实体
            Question question = dataConverter.convertToTargetFormat(questionData);

            // 检查是否已存在相同题目
            Question existingQuestion = findExistingQuestion(question);
            Question savedQuestion;
            boolean isUpdate = false;

            if (existingQuestion != null) {
                log.warn("⚠️ 发现重复题目，执行更新操作: 源题目ID={}, 知识点ID={}, 题目类型={}, 现有题目ID={}",
                    questionData.getId(), question.getKnowledgePointId(), question.getQuestionType(), existingQuestion.getId());

                // 输出更新提示到控制台
                System.out.println("=== 🔄 重复题目更新 ===");
                System.out.println("源题目ID: " + questionData.getId());
                System.out.println("题目类型: " + question.getQuestionType());
                System.out.println("知识点ID: " + question.getKnowledgePointId());
                System.out.println("现有题目ID: " + existingQuestion.getId());
                System.out.println("操作: 更新现有题目数据");
                System.out.println("========================");

                // 更新现有题目
                savedQuestion = updateExistingQuestion(existingQuestion, question);
                isUpdate = true;
            } else {
                // 保存到数据库
                savedQuestion = questionRepository.save(question);
                log.info("✅ 新题目保存成功: 源ID={}, 数据库ID={}", questionData.getId(), savedQuestion.getId());
            }

            LocalDateTime endTime = LocalDateTime.now();
            long durationMs = java.time.Duration.between(startTime, endTime).toMillis();

            // 自动清理缓存，确保下次迁移使用最新数据
            hierarchyProcessor.clearCache();
            log.info("迁移完成后自动清理缓存");

            // 构建成功结果
            String resultMessage;
            if (isUpdate) {
                resultMessage = String.format("题目迁移完成（更新现有题目）: 源ID=%s, 更新题目ID=%s",
                    questionData.getId(), savedQuestion.getId());
                log.info("🔄 {}", resultMessage);
            } else {
                resultMessage = String.format("新题目迁移成功: 源ID=%s, 数据库ID=%s",
                    questionData.getId(), savedQuestion.getId());
                log.info("✅ {}", resultMessage);
            }

            return MigrationResult.builder()
                .success(true)
                .totalCount(1)
                .successCount(isUpdate ? 0 : 1) // 更新不算新增成功
                .errorCount(0)
                .skipCount(isUpdate ? 1 : 0) // 使用skipCount表示更新数量
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(durationMs)
                .message(resultMessage)
                .build();

        } catch (Exception e) {
            log.error("题目迁移失败: ID={}", questionData.getId(), e);

            System.out.println("=== 题目迁移失败！！！ ===");
            System.out.println("题目ID: " + questionData.getId());
            System.out.println("题目类型: " + questionData.getType());
            System.out.println("错误类型: " + e.getClass().getSimpleName());
            System.out.println("错误消息: " + e.getMessage());
            e.printStackTrace();

            // 记录详细的错误信息
            log.error("题目迁移失败详细信息:");
            log.error("  - 题目ID: {}", questionData.getId());
            log.error("  - 题目类型: {}", questionData.getType());
            log.error("  - 错误类型: {}", e.getClass().getSimpleName());
            log.error("  - 错误消息: {}", e.getMessage());

            if (questionData.getBasicInfo() != null) {
                log.error("  - 基础信息:");
                if (questionData.getBasicInfo().getSubject() != null) {
                    log.error("    - 学科: {}", questionData.getBasicInfo().getSubject().getName());
                }
                if (questionData.getBasicInfo().getBookversion() != null) {
                    log.error("    - 版本: {}", questionData.getBasicInfo().getBookversion().getName());
                }
                if (questionData.getBasicInfo().getChapter() != null) {
                    log.error("    - 章节: ID={}, name={}",
                        questionData.getBasicInfo().getChapter().getId(),
                        questionData.getBasicInfo().getChapter().getName());
                }
                if (questionData.getBasicInfo().getKnowledgePoints() != null && !questionData.getBasicInfo().getKnowledgePoints().isEmpty()) {
                    log.error("    - 知识点: ID={}, name={}",
                        questionData.getBasicInfo().getKnowledgePoints().get(0).getId(),
                        questionData.getBasicInfo().getKnowledgePoints().get(0).getName());
                }
            }

            LocalDateTime endTime = LocalDateTime.now();
            long durationMs = java.time.Duration.between(startTime, endTime).toMillis();

            // 即使失败也清理缓存，避免缓存中保留无效数据
            hierarchyProcessor.clearCache();
            log.info("迁移失败后自动清理缓存");

            // 构建失败结果
            return MigrationResult.builder()
                .success(false)
                .totalCount(1)
                .successCount(0)
                .errorCount(1)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(durationMs)
                .errors(List.of("题目ID " + questionData.getId() + ": " + e.getMessage()))
                .message("单个题目迁移失败")
                .build();
        }
    }



    /**
     * 查找是否存在相同的题目
     * 根据body.id字段判断是否重复（旧数据库的唯一ID）
     */
    private Question findExistingQuestion(Question question) {
        try {
            // 获取题目的body.id用于重复检测
            String bodyId = extractBodyId(question);
            if (bodyId == null || bodyId.trim().isEmpty()) {
                log.warn("题目body.id为空，无法进行重复检测，将继续保存");
                return null; // 无法获取body.id，认为不重复
            }

            // 根据body.id查找现有题目
            List<Question> existingQuestions = questionRepository.findByBodyId(bodyId);

            if (!existingQuestions.isEmpty()) {
                Question existingQuestion = existingQuestions.get(0);
                log.info("找到重复题目: body.id={}, 现有数据库ID={}, 知识点ID={}, 题目类型={}",
                    bodyId, existingQuestion.getId(), existingQuestion.getKnowledgePointId(), existingQuestion.getQuestionType());

                if (existingQuestions.size() > 1) {
                    log.warn("发现多个相同body.id的题目: body.id={}, 数量={}, 将使用第一个",
                        bodyId, existingQuestions.size());
                }

                return existingQuestion;
            }

            return null; // 没有找到重复题目

        } catch (Exception e) {
            log.warn("检查重复题目时发生异常，将继续保存: {}", e.getMessage());
            return null; // 出现异常时，为了安全起见，认为不重复
        }
    }

    /**
     * 从题目body中提取id字段用于重复检测
     */
    private String extractBodyId(Question question) {
        try {
            Map<String, Object> body = question.getBody();
            if (body == null) {
                return null;
            }

            // 获取题目的body.id
            Object idObj = body.get("id");
            if (idObj == null) {
                return null;
            }

            return idObj.toString().trim();

        } catch (Exception e) {
            log.warn("提取题目body.id失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 更新现有题目数据
     */
    private Question updateExistingQuestion(Question existingQuestion, Question newQuestion) {
        try {
            log.info("开始更新题目: 数据库ID={}, body.id={}",
                existingQuestion.getId(), extractBodyId(existingQuestion));

            // 更新题目数据
            existingQuestion.setQuestionType(newQuestion.getQuestionType());
            existingQuestion.setBody(newQuestion.getBody());
            existingQuestion.setKnowledgePointId(newQuestion.getKnowledgePointId());
            existingQuestion.setEnabled(newQuestion.getEnabled());
            existingQuestion.setUpdatedAt(new Date());

            // 保存更新
            Question updatedQuestion = questionRepository.save(existingQuestion);

            log.info("✅ 题目更新成功: 数据库ID={}, body.id={}",
                updatedQuestion.getId(), extractBodyId(updatedQuestion));

            return updatedQuestion;

        } catch (Exception e) {
            log.error("更新题目失败: 数据库ID={}", existingQuestion.getId(), e);
            throw new RuntimeException("更新题目失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从题目body中提取内容用于重复比较（已废弃，保留用于兼容性）
     * @deprecated 现在使用body.id进行重复检测
     */
    @Deprecated
    private String extractQuestionContent(Question question) {
        try {
            Map<String, Object> body = question.getBody();
            if (body == null) {
                return null;
            }

            // 获取题目内容
            Object contentObj = body.get("content");
            if (contentObj == null) {
                return null;
            }

            String content = contentObj.toString().trim();

            // 简单的内容清理，移除HTML标签和多余空白
            content = content.replaceAll("<[^>]+>", ""); // 移除HTML标签
            content = content.replaceAll("\\s+", " "); // 合并多个空白字符

            return content;

        } catch (Exception e) {
            log.warn("提取题目内容失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 构建详细的迁移错误信息
     */
    private String buildDetailedMigrationError(String jsonData, Exception e) {
        StringBuilder errorMsg = new StringBuilder();

        // 判断错误类型
        if (e instanceof com.fasterxml.jackson.core.JsonParseException) {
            com.fasterxml.jackson.core.JsonParseException jpe = (com.fasterxml.jackson.core.JsonParseException) e;
            errorMsg.append("JSON解析错误，位置: 第").append(jpe.getLocation().getLineNr())
                   .append("行，第").append(jpe.getLocation().getColumnNr()).append("列");
        } else if (e instanceof com.fasterxml.jackson.databind.JsonMappingException) {
            errorMsg.append("JSON数据映射错误");
        } else if (e.getMessage() != null && e.getMessage().contains("层级")) {
            errorMsg.append("层级数据处理错误");
        } else if (e.getMessage() != null && e.getMessage().contains("验证")) {
            errorMsg.append("数据验证错误");
        } else if (e.getMessage() != null && e.getMessage().contains("数据库")) {
            errorMsg.append("数据库操作错误");
        } else {
            errorMsg.append("迁移过程错误");
        }

        errorMsg.append("。详细信息: ").append(e.getMessage());

        // 检查JSON格式问题
        if (jsonData != null) {
            if (!jsonData.trim().startsWith("[")) {
                errorMsg.append("。提示：数据应该是JSON数组格式，以 '[' 开始");
            } else if (!jsonData.trim().endsWith("]")) {
                errorMsg.append("。提示：JSON数组应该以 ']' 结束，请检查是否有额外内容");
            }

            // 提供数据长度信息
            errorMsg.append("。数据长度: ").append(jsonData.length()).append(" 字符");

            // 如果数据很长，只显示开头和结尾
            if (jsonData.length() > 200) {
                String preview = jsonData.substring(0, 100) + "..." +
                               jsonData.substring(jsonData.length() - 100);
                errorMsg.append("。数据预览: ").append(preview);
            }
        }

        return errorMsg.toString();
    }
    

    
    /**
     * 验证源数据
     */
    @Override
    public MigrationResult validateSourceData(List<SourceQuestionData> sourceData) {
        LocalDateTime startTime = LocalDateTime.now();
        List<String> errors = new ArrayList<>();
        long validCount = 0;
        
        for (SourceQuestionData data : sourceData) {
            ValidationResult validation = migrationValidator.validateSourceData(data);
            if (validation.isValid()) {
                validCount++;
            } else {
                errors.add("ID " + data.getId() + ": " + validation.getErrorMessage());
            }
        }
        
        LocalDateTime endTime = LocalDateTime.now();
        
        return MigrationResult.builder()
            .success(errors.isEmpty())
            .totalCount(sourceData.size())
            .successCount(validCount)
            .errorCount(sourceData.size() - validCount)
            .startTime(startTime)
            .endTime(endTime)
            .durationMs(java.time.Duration.between(startTime, endTime).toMillis())
            .errors(errors)
            .message("数据验证完成")
            .build();
    }
    
    /**
     * 获取当前迁移进度
     */
    @Override
    public MigrationResult getCurrentMigrationProgress() {
        if (currentMigrationResult == null) {
            return MigrationResult.builder()
                .success(false)
                .message("没有正在进行的迁移任务")
                .build();
        }
        
        return currentMigrationResult;
    }
    
    /**
     * 停止迁移
     */
    @Override
    public void stopMigration() {
        if (migrationRunning.get()) {
            migrationRunning.set(false);
            shutdownExecutorService();
            log.info("迁移任务已停止");
        }
    }
    
    /**
     * 清理缓存
     */
    @Override
    public void clearMigrationCache() {
        hierarchyProcessor.clearCache();
        currentMigrationResult = null;
        log.info("迁移缓存已清理");
    }
    
    /**
     * 初始化线程池
     */
    private void initializeExecutorService() {
        if (executorService == null || executorService.isShutdown()) {
            executorService = Executors.newFixedThreadPool(threadPoolSize);
        }
    }
    
    /**
     * 关闭线程池
     */
    private void shutdownExecutorService() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 数据分批
     */
    private List<List<SourceQuestionData>> partitionData(List<SourceQuestionData> data, int batchSize) {
        List<List<SourceQuestionData>> batches = new ArrayList<>();
        for (int i = 0; i < data.size(); i += batchSize) {
            batches.add(data.subList(i, Math.min(i + batchSize, data.size())));
        }
        return batches;
    }
    
    /**
     * 汇总结果
     */
    private MigrationResult aggregateResults(List<CompletableFuture<BatchResult>> futures, 
                                           int totalCount, LocalDateTime startTime) {
        LocalDateTime endTime = LocalDateTime.now();
        List<BatchResult> batchResults = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
        
        long successCount = batchResults.stream().mapToLong(BatchResult::getSuccessCount).sum();
        long errorCount = batchResults.stream().mapToLong(BatchResult::getErrorCount).sum();

        // 统计更新题目数量（从成功ID中包含"更新"的记录）
        long updateCount = batchResults.stream()
            .flatMap(br -> br.getSuccessIds().stream())
            .filter(id -> id.contains("更新"))
            .count();

        // 实际新增的题目数量
        long actualNewCount = successCount - updateCount;

        List<String> allErrors = batchResults.stream()
            .flatMap(br -> br.getErrors().entrySet().stream())
            .map(entry -> "ID " + entry.getKey() + ": " + entry.getValue())
            .collect(Collectors.toList());

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("batchCount", batchResults.size());
        statistics.put("averageBatchSize", totalCount / (double) batchResults.size());
        statistics.put("updateCount", updateCount);
        statistics.put("actualNewCount", actualNewCount);
        statistics.put("hierarchyCache", hierarchyProcessor.getCacheStatistics());

        // 构建结果消息
        String resultMessage;
        if (errorCount == 0) {
            if (updateCount > 0) {
                resultMessage = String.format("迁移成功完成，新增 %d 条，更新 %d 条", actualNewCount, updateCount);
            } else {
                resultMessage = "迁移成功完成";
            }
        } else {
            if (updateCount > 0) {
                resultMessage = String.format("迁移完成，新增 %d 条，更新 %d 条，失败 %d 条", actualNewCount, updateCount, errorCount);
            } else {
                resultMessage = "迁移完成，但有部分数据失败";
            }
        }

        // 输出汇总统计到控制台
        System.out.println("=== 📊 迁移统计汇总 ===");
        System.out.println("总数据量: " + totalCount);
        System.out.println("新增题目: " + actualNewCount);
        System.out.println("更新题目: " + updateCount);
        System.out.println("失败数量: " + errorCount);
        System.out.println("成功率: " + String.format("%.1f%%", (double)(actualNewCount + updateCount) / totalCount * 100));
        System.out.println("======================");

        return MigrationResult.builder()
            .success(errorCount == 0)
            .totalCount(totalCount)
            .successCount(actualNewCount) // 只统计实际新增的题目
            .errorCount(errorCount)
            .skipCount(updateCount) // 使用skipCount表示更新数量
            .startTime(startTime)
            .endTime(endTime)
            .durationMs(java.time.Duration.between(startTime, endTime).toMillis())
            .errors(allErrors)
            .statistics(statistics)
            .batchResults(batchResults)
            .message(resultMessage)
            .build();
    }
}
