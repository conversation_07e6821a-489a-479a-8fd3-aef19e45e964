package com.example.adminbackend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.persistence.EntityManagerFactory;
import java.util.Properties;

/**
 * Hibernate配置类
 * 优化懒加载和批处理性能
 */
@Configuration
public class HibernateConfig {

    /**
     * 配置Hibernate属性以优化懒加载性能
     */
    @Bean
    @Lazy
    public Properties hibernateProperties() {
        Properties properties = new Properties();
        
        // 批处理优化
        properties.setProperty("hibernate.jdbc.batch_size", "50");
        properties.setProperty("hibernate.jdbc.batch_versioned_data", "true");
        properties.setProperty("hibernate.order_inserts", "true");
        properties.setProperty("hibernate.order_updates", "true");
        
        // 连接池优化
        properties.setProperty("hibernate.connection.provider_disables_autocommit", "true");
        
        // 懒加载优化
        properties.setProperty("hibernate.enable_lazy_load_no_trans", "false");
        properties.setProperty("hibernate.bytecode.use_reflection_optimizer", "true");
        
        // 查询优化
        properties.setProperty("hibernate.query.plan_cache_max_size", "2048");
        properties.setProperty("hibernate.query.plan_parameter_metadata_max_size", "128");
        
        // 二级缓存配置（如果需要）
        properties.setProperty("hibernate.cache.use_second_level_cache", "false");
        properties.setProperty("hibernate.cache.use_query_cache", "false");
        
        return properties;
    }

    /**
     * 事务管理器配置
     */
    @Bean
    @Lazy
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        
        // 优化事务超时设置
        transactionManager.setDefaultTimeout(300); // 5分钟
        
        return transactionManager;
    }
}
