package com.example.adminbackend.controller;

import com.example.adminbackend.model.KnowledgePoint;
import com.example.adminbackend.model.Subject;
import com.example.adminbackend.model.SubjectVersion;
import com.example.adminbackend.model.Chapter;
import com.example.adminbackend.model.VideoCollection;
import com.example.adminbackend.model.CollectionVideo;
import com.example.adminbackend.model.Video;
import com.example.adminbackend.repository.CollectionVideoRepository;

import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Optional;
import com.example.adminbackend.service.ChapterService;
import com.example.adminbackend.service.KnowledgePointService;
import com.example.adminbackend.service.VideoService;
import com.example.adminbackend.service.VideoCollectionService;
import com.example.adminbackend.service.SubjectService;
import com.example.adminbackend.service.SubjectVersionService;
import com.example.adminbackend.service.QiniuUploadService;
import com.example.adminbackend.dto.SubjectDTO;
import com.example.adminbackend.dto.SubjectVersionDTO;
import com.example.adminbackend.dto.ChapterDTO;
import com.example.adminbackend.dto.KnowledgePointDTO;
import com.example.adminbackend.dto.KnowledgePointHierarchyInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Collections;

/**
 * 课程体系控制器
 * 提供科目、章节、知识点的层级查询API
 */
@RestController
@RequestMapping("/curriculum")
@RequiredArgsConstructor
@Slf4j
public class CurriculumController {

    private final SubjectService subjectService;
    private final SubjectVersionService subjectVersionService;
    private final ChapterService chapterService;
    private final KnowledgePointService knowledgePointService;
    private final VideoService videoService;
    private final VideoCollectionService videoCollectionService;
    private final QiniuUploadService qiniuUploadService;
    private final CollectionVideoRepository collectionVideoRepository;

    // ==================== 科目相关接口 ====================

    /**
     * 获取所有科目
     */
    @GetMapping("/subjects")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<SubjectDTO>> getAllSubjects() {
        List<SubjectDTO> subjects = subjectService.getAllSubjectsAsDTO();
        return ResponseEntity.ok(subjects);
    }

    /**
     * 获取有版本的科目列表
     */
    @GetMapping("/subjects/with-versions")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<SubjectDTO>> getSubjectsWithVersions() {
        try {
            log.info("获取有版本的科目列表");
            List<SubjectDTO> subjects = subjectService.getSubjectsWithVersions();
            log.info("成功获取到 {} 个有版本的科目", subjects.size());
            return ResponseEntity.ok(subjects);
        } catch (Exception e) {
            log.error("获取有版本的科目列表失败: {}", e.getMessage(), e);
            // 返回一个空的列表，并保持 500 状态码，或者返回一个包含错误信息的 DTO
            // 这里为了简化，直接返回空列表。前端会根据 HTTP 状态码来处理错误。
            return ResponseEntity.status(500).body(Collections.emptyList());
        }
    }

    /**
     * 根据ID获取科目
     */
    @GetMapping("/subjects/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<SubjectDTO> getSubjectById(@PathVariable Long id) {
        SubjectDTO subject = subjectService.getSubjectByIdAsDTO(id);
        return ResponseEntity.ok(subject);
    }

    // ==================== 科目版本相关接口 ====================

    /**
     * 根据科目ID获取所有版本
     */
    @GetMapping("/subjects/{subjectId}/versions")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<SubjectVersionDTO>> getVersionsBySubjectId(@PathVariable Long subjectId) {
        List<SubjectVersionDTO> versions = subjectVersionService.getVersionsBySubjectIdAsDTO(subjectId);
        return ResponseEntity.ok(versions);
    }

    /**
     * 获取有章节的科目版本列表
     */
    @GetMapping("/subject-versions/with-chapters")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<SubjectVersionDTO>> getSubjectVersionsWithChapters() {
        List<SubjectVersionDTO> versions = subjectVersionService.getSubjectVersionsWithChaptersAsDTO();
        return ResponseEntity.ok(versions);
    }

    /**
     * 根据ID获取科目版本
     */
    @GetMapping("/subject-versions/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<SubjectVersionDTO> getSubjectVersionById(@PathVariable Long id) {
        SubjectVersionDTO version = subjectVersionService.getSubjectVersionByIdAsDTO(id);
        return ResponseEntity.ok(version);
    }

    /**
     * 获取所有科目版本
     */
    @GetMapping("/subject-versions")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<SubjectVersionDTO>> getAllSubjectVersions() {
        List<SubjectVersionDTO> versions = subjectVersionService.getAllSubjectVersionsAsDTO();
        return ResponseEntity.ok(versions);
    }

    /**
     * 创建科目版本
     */
    @PostMapping("/subject-versions")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<SubjectVersionDTO> createSubjectVersion(@RequestBody SubjectVersion subjectVersion) {
        try {
            SubjectVersion createdVersion = subjectVersionService.createSubjectVersion(subjectVersion);
            SubjectVersionDTO versionDTO = subjectVersionService.getSubjectVersionByIdAsDTO(createdVersion.getId());
            return ResponseEntity.ok(versionDTO);
        } catch (Exception e) {
            log.error("创建科目版本失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新科目版本
     */
    @PutMapping("/subject-versions/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<SubjectVersionDTO> updateSubjectVersion(@PathVariable Long id, @RequestBody SubjectVersion subjectVersion) {
        try {
            SubjectVersion updatedVersion = subjectVersionService.updateSubjectVersion(id, subjectVersion);
            SubjectVersionDTO versionDTO = subjectVersionService.getSubjectVersionByIdAsDTO(updatedVersion.getId());
            return ResponseEntity.ok(versionDTO);
        } catch (Exception e) {
            log.error("更新科目版本失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除科目版本
     */
    @DeleteMapping("/subject-versions/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteSubjectVersion(@PathVariable Long id) {
        try {
            int deletedFilesCount = subjectVersionService.deleteSubjectVersion(id);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "科目版本删除成功，已同时删除所有章节、知识点和相关文件",
                "deletedFilesCount", deletedFilesCount
            ));
        } catch (Exception e) {
            log.error("删除科目版本失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "删除科目版本失败: " + e.getMessage()));
        }
    }

    // ==================== 章节相关接口 ====================

    /**
     * 获取所有章节
     */
    @GetMapping("/chapters")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<ChapterDTO>> getAllChapters() {
        List<ChapterDTO> chapters = chapterService.getAllChaptersAsDTO();
        return ResponseEntity.ok(chapters);
    }

    /**
     * 根据科目版本ID获取所有章节
     */
    @GetMapping("/subject-versions/{subjectVersionId}/chapters")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<com.example.adminbackend.dto.ChapterDTO>> getChaptersBySubjectVersionId(@PathVariable Long subjectVersionId) {
        List<com.example.adminbackend.dto.ChapterDTO> chapters = chapterService.getChaptersBySubjectVersionId(subjectVersionId);
        return ResponseEntity.ok(chapters);
    }

    /**
     * 根据科目ID获取所有章节
     */
    @GetMapping("/subjects/{subjectId}/chapters")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<ChapterDTO>> getChaptersBySubjectId(@PathVariable Long subjectId) {
        List<ChapterDTO> chapters = chapterService.getChaptersBySubjectIdAsDTO(subjectId);
        return ResponseEntity.ok(chapters);
    }

    /**
     * 获取有知识点的章节列表
     */
    @GetMapping("/chapters/with-knowledge-points")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<ChapterDTO>> getChaptersWithKnowledgePoints() {
        List<ChapterDTO> chapters = chapterService.getChaptersWithKnowledgePointsAsDTO();
        return ResponseEntity.ok(chapters);
    }

    /**
     * 根据ID获取章节
     */
    @GetMapping("/chapters/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ChapterDTO> getChapterById(@PathVariable Long id) {
        ChapterDTO chapter = chapterService.getChapterByIdAsDTO(id);
        return ResponseEntity.ok(chapter);
    }

    // ==================== 知识点相关接口 ====================

    /**
     * 获取所有知识点
     */
    @GetMapping("/knowledge-points")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<KnowledgePointDTO>> getAllKnowledgePoints() {
        List<KnowledgePointDTO> knowledgePoints = knowledgePointService.getAllKnowledgePointsAsDTO();
        return ResponseEntity.ok(knowledgePoints);
    }

    /**
     * 根据章节ID获取所有知识点
     */
    @GetMapping("/chapters/{chapterId}/knowledge-points")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<KnowledgePointDTO>> getKnowledgePointsByChapterId(@PathVariable Long chapterId) {
        List<KnowledgePointDTO> knowledgePoints = knowledgePointService.getKnowledgePointDTOsByChapterId(chapterId);
        return ResponseEntity.ok(knowledgePoints);
    }

    /**
     * 根据科目版本ID获取所有知识点
     */
    @GetMapping("/subject-versions/{subjectVersionId}/knowledge-points")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<KnowledgePoint>> getKnowledgePointsBySubjectVersionId(@PathVariable Long subjectVersionId) {
        List<KnowledgePoint> knowledgePoints = knowledgePointService.getKnowledgePointsBySubjectVersionId(subjectVersionId);
        return ResponseEntity.ok(knowledgePoints);
    }

    /**
     * 根据科目ID获取所有知识点
     */
    @GetMapping("/subjects/{subjectId}/knowledge-points")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<KnowledgePoint>> getKnowledgePointsBySubjectId(@PathVariable Long subjectId) {
        List<KnowledgePoint> knowledgePoints = knowledgePointService.getKnowledgePointsBySubjectId(subjectId);
        return ResponseEntity.ok(knowledgePoints);
    }

    /**
     * 根据ID获取知识点
     */
    @GetMapping("/knowledge-points/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<KnowledgePoint> getKnowledgePointById(@PathVariable Long id) {
        KnowledgePoint knowledgePoint = knowledgePointService.getKnowledgePointById(id);
        return ResponseEntity.ok(knowledgePoint);
    }

    /**
     * 根据知识点ID获取其层级信息
     */
    @GetMapping("/knowledge-points/{id}/hierarchy")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<KnowledgePointHierarchyInfo> getKnowledgePointHierarchy(@PathVariable Long id) {
        KnowledgePointHierarchyInfo hierarchyInfo = knowledgePointService.getKnowledgePointHierarchy(id);
        return ResponseEntity.ok(hierarchyInfo);
    }

    // ==================== 搜索接口 ====================

    /**
     * 搜索科目
     */
    @GetMapping("/subjects/search")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<Subject>> searchSubjects(@RequestParam String keyword) {
        List<Subject> subjects = subjectService.searchSubjects(keyword);
        return ResponseEntity.ok(subjects);
    }

    /**
     * 搜索章节
     */
    @GetMapping("/chapters/search")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<ChapterDTO>> searchChapters(@RequestParam String keyword) {
        List<ChapterDTO> chapters = chapterService.searchChaptersAsDTO(keyword);
        return ResponseEntity.ok(chapters);
    }

    /**
     * 搜索知识点
     */
    @GetMapping("/knowledge-points/search")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<KnowledgePoint>> searchKnowledgePoints(@RequestParam String keyword) {
        List<KnowledgePoint> knowledgePoints = knowledgePointService.searchKnowledgePoints(keyword);
        return ResponseEntity.ok(knowledgePoints);
    }

    // ==================== 学科管理接口 ====================

    /**
     * 创建学科
     */
    @PostMapping("/subjects")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<SubjectDTO> createSubject(@RequestBody Subject subject) {
        try {
            Subject createdSubject = subjectService.createSubject(subject);
            SubjectDTO subjectDTO = subjectService.getSubjectByIdAsDTO(createdSubject.getId());
            return ResponseEntity.ok(subjectDTO);
        } catch (Exception e) {
            log.error("创建学科失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新学科
     */
    @PutMapping("/subjects/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<SubjectDTO> updateSubject(@PathVariable Long id, @RequestBody Subject subject) {
        try {
            Subject updatedSubject = subjectService.updateSubject(id, subject);
            SubjectDTO subjectDTO = subjectService.getSubjectByIdAsDTO(updatedSubject.getId());
            return ResponseEntity.ok(subjectDTO);
        } catch (Exception e) {
            log.error("更新学科失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除学科
     */
    @DeleteMapping("/subjects/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteSubject(@PathVariable Long id) {
        try {
            // 使用Service层的级联删除方法
            int deletedFilesCount = subjectService.deleteSubjectCascade(id);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "学科删除成功，已同时删除所有版本、章节、知识点和相关文件",
                "deletedFilesCount", deletedFilesCount
            ));

        } catch (Exception e) {
            log.error("删除学科失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "删除学科失败: " + e.getMessage()));
        }
    }

    // ==================== 章节管理接口 ====================

    /**
     * 创建章节
     */
    @PostMapping("/chapters")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ChapterDTO> createChapter(@RequestBody Chapter chapter) {
        try {
            Chapter createdChapter = chapterService.createChapter(chapter);
            ChapterDTO chapterDTO = chapterService.getChapterByIdAsDTO(createdChapter.getId());
            return ResponseEntity.ok(chapterDTO);
        } catch (Exception e) {
            log.error("创建章节失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新章节
     */
    @PutMapping("/chapters/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ChapterDTO> updateChapter(@PathVariable Long id, @RequestBody Chapter chapter) {
        try {
            Chapter updatedChapter = chapterService.updateChapter(id, chapter);
            ChapterDTO chapterDTO = chapterService.getChapterByIdAsDTO(updatedChapter.getId());
            return ResponseEntity.ok(chapterDTO);
        } catch (Exception e) {
            log.error("更新章节失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除章节
     */
    @DeleteMapping("/chapters/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteChapter(@PathVariable Long id) {
        try {
            // 使用Service层的级联删除方法
            int deletedFilesCount = chapterService.deleteChapterCascade(id);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "章节删除成功，已同时删除所有知识点和相关文件",
                "deletedFilesCount", deletedFilesCount
            ));

        } catch (Exception e) {
            log.error("删除章节失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "删除章节失败: " + e.getMessage()));
        }
    }

    // ==================== 知识点管理接口 ====================

    /**
     * 创建知识点
     */
    @PostMapping("/knowledge-points")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<KnowledgePointDTO> createKnowledgePoint(@RequestBody KnowledgePoint knowledgePoint) {
        try {
            KnowledgePoint createdKnowledgePoint = knowledgePointService.createKnowledgePoint(knowledgePoint);
            // 临时使用层级信息创建DTO，后续需要添加专门的方法
            KnowledgePointHierarchyInfo hierarchyInfo = knowledgePointService.getKnowledgePointHierarchy(createdKnowledgePoint.getId());
            KnowledgePointDTO knowledgePointDTO = new KnowledgePointDTO();
            knowledgePointDTO.setId(createdKnowledgePoint.getId());
            knowledgePointDTO.setName(createdKnowledgePoint.getName());
            knowledgePointDTO.setOrderIndex(createdKnowledgePoint.getOrderIndex());
            knowledgePointDTO.setCoverImageUrl(createdKnowledgePoint.getCoverImageUrl());
            knowledgePointDTO.setDescription(createdKnowledgePoint.getDescription());
            knowledgePointDTO.setEnabled(createdKnowledgePoint.getEnabled());
            knowledgePointDTO.setCreatedAt(createdKnowledgePoint.getCreatedAt());
            knowledgePointDTO.setUpdatedAt(createdKnowledgePoint.getUpdatedAt());
            knowledgePointDTO.setChapterId(createdKnowledgePoint.getChapter().getId());
            return ResponseEntity.ok(knowledgePointDTO);
        } catch (Exception e) {
            log.error("创建知识点失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新知识点
     */
    @PutMapping("/knowledge-points/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    @Transactional
    public ResponseEntity<KnowledgePointDTO> updateKnowledgePoint(@PathVariable Long id, @RequestBody KnowledgePoint knowledgePoint) {
        try {
            // 获取原知识点信息，用于比较封面图片变化
            KnowledgePoint existingKnowledgePoint = knowledgePointService.getKnowledgePointById(id);
            String oldCoverImageUrl = existingKnowledgePoint.getCoverImageUrl();
            String newCoverImageUrl = knowledgePoint.getCoverImageUrl();

            // 更新知识点
            KnowledgePoint updatedKnowledgePoint = knowledgePointService.updateKnowledgePoint(id, knowledgePoint);

            // 检查封面图片是否发生变化，如果变化则删除旧图片
            if (oldCoverImageUrl != null && !oldCoverImageUrl.trim().isEmpty()) {
                // 如果新图片为空，或者新旧图片不同，则删除旧图片
                if ((newCoverImageUrl == null || newCoverImageUrl.trim().isEmpty()) ||
                    !oldCoverImageUrl.equals(newCoverImageUrl)) {

                    log.info("知识点封面图片发生变化，删除旧图片: {}", oldCoverImageUrl);
                    boolean deleted = qiniuUploadService.deleteFile(oldCoverImageUrl);
                    if (deleted) {
                        log.info("旧封面图片删除成功: {}", oldCoverImageUrl);
                    } else {
                        log.warn("旧封面图片删除失败: {}", oldCoverImageUrl);
                    }
                }
            }

            // 创建DTO返回
            KnowledgePointDTO knowledgePointDTO = new KnowledgePointDTO();
            knowledgePointDTO.setId(updatedKnowledgePoint.getId());
            knowledgePointDTO.setName(updatedKnowledgePoint.getName());
            knowledgePointDTO.setOrderIndex(updatedKnowledgePoint.getOrderIndex());
            knowledgePointDTO.setCoverImageUrl(updatedKnowledgePoint.getCoverImageUrl());
            knowledgePointDTO.setDescription(updatedKnowledgePoint.getDescription());
            knowledgePointDTO.setEnabled(updatedKnowledgePoint.getEnabled());
            knowledgePointDTO.setCreatedAt(updatedKnowledgePoint.getCreatedAt());
            knowledgePointDTO.setUpdatedAt(updatedKnowledgePoint.getUpdatedAt());
            knowledgePointDTO.setChapterId(updatedKnowledgePoint.getChapter().getId());
            return ResponseEntity.ok(knowledgePointDTO);
        } catch (Exception e) {
            log.error("更新知识点失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除知识点
     */
    @DeleteMapping("/knowledge-points/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteKnowledgePoint(@PathVariable Long id) {
        try {
            // 使用Service层的级联删除方法
            int deletedFilesCount = knowledgePointService.deleteKnowledgePointCascade(id);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "知识点删除成功，已同时删除七牛云文件",
                "deletedFilesCount", deletedFilesCount
            ));

        } catch (Exception e) {
            log.error("删除知识点失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "删除知识点失败: " + e.getMessage()));
        }
    }

    // ==================== 知识点视频管理接口 ====================

    /**
     * 为知识点添加视频
     */
    @PostMapping("/knowledge-points/{id}/video")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    @Transactional
    public ResponseEntity<?> addVideoToKnowledgePoint(
            @PathVariable Long id,
            @RequestBody Map<String, String> request) {
        try {
            String videoUrl = request.get("videoUrl");
            if (videoUrl == null || videoUrl.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "视频URL不能为空"));
            }

            // 获取知识点
            KnowledgePoint knowledgePoint = knowledgePointService.getKnowledgePointById(id);

            VideoCollection videoCollection;
            String videoCollectionName;

            // 检查知识点是否已有视频合集
            if (knowledgePoint.getVideoCollection() != null) {
                // 如果已有视频合集，向现有合集添加新视频
                videoCollection = knowledgePoint.getVideoCollection();
                // 在事务内提前获取名称，避免懒加载异常
                videoCollectionName = videoCollection.getName();
                log.info("向现有视频合集添加视频: 合集ID={}, 合集名称={}, 视频URL={}",
                    videoCollection.getId(), videoCollectionName, videoUrl);

                // 向现有合集添加视频
                boolean added = videoCollectionService.addVideoToCollection(videoCollection.getId(), videoUrl);
                if (!added) {
                    return ResponseEntity.badRequest()
                        .body(Map.of("error", "向视频合集添加视频失败"));
                }
            } else {
                // 如果没有视频合集，创建新的合集
                videoCollection = videoCollectionService
                    .findOrCreateVideoCollectionByUrlAndKnowledgePoint(videoUrl, knowledgePoint.getName());

                if (videoCollection == null) {
                    return ResponseEntity.badRequest()
                        .body(Map.of("error", "视频合集创建失败"));
                }

                // 在事务内提前获取名称，避免懒加载异常
                videoCollectionName = videoCollection.getName();

                // 更新知识点的视频合集关联
                knowledgePoint.setVideoCollection(videoCollection);
                knowledgePointService.updateKnowledgePoint(id, knowledgePoint);
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频添加成功",
                "videoCollectionId", videoCollection.getId(),
                "videoCollectionName", videoCollectionName
            ));

        } catch (Exception e) {
            log.error("为知识点添加视频失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "添加视频失败: " + e.getMessage()));
        }
    }

    /**
     * 移除知识点的视频
     */
    @DeleteMapping("/knowledge-points/{id}/video")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    @Transactional
    public ResponseEntity<?> removeVideoFromKnowledgePoint(@PathVariable Long id) {
        try {
            // 获取知识点
            KnowledgePoint knowledgePoint = knowledgePointService.getKnowledgePointById(id);

            // 获取视频合集信息，用于删除七牛云文件
            VideoCollection videoCollection = knowledgePoint.getVideoCollection();
            List<String> videoUrls = new ArrayList<>();

            if (videoCollection != null) {
                // 在事务内提前获取名称，避免懒加载异常
                String videoCollectionName = videoCollection.getName();
                log.info("准备删除知识点 {} 的视频合集: {}", id, videoCollectionName);

                // 收集所有视频URL - 在事务内访问懒加载属性
                List<CollectionVideo> videos = videoCollection.getVideos();
                if (videos != null) {
                    for (CollectionVideo cv : videos) {
                        if (cv.getVideo() != null && cv.getVideo().getVideoUrl() != null) {
                            videoUrls.add(cv.getVideo().getVideoUrl());
                        }
                    }
                }

                // 删除七牛云文件
                if (!videoUrls.isEmpty()) {
                    log.info("开始删除七牛云视频文件，数量: {}", videoUrls.size());
                    int deletedCount = qiniuUploadService.deleteFiles(videoUrls);
                    log.info("七牛云文件删除完成，成功删除: {} 个", deletedCount);
                }
            }

            // 移除视频合集关联
            knowledgePoint.setVideoCollection(null);
            knowledgePointService.updateKnowledgePoint(id, knowledgePoint);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频移除成功，已同时删除七牛云文件"
            ));

        } catch (Exception e) {
            log.error("移除知识点视频失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "移除视频失败: " + e.getMessage()));
        }
    }

    /**
     * 获取知识点的视频信息
     */
    @GetMapping("/knowledge-points/{id}/video")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    @Transactional(readOnly = true)
    public ResponseEntity<?> getKnowledgePointVideo(@PathVariable Long id) {
        try {
            KnowledgePoint knowledgePoint = knowledgePointService.getKnowledgePointById(id);

            if (knowledgePoint.getVideoCollection() != null) {
                VideoCollection videoCollection = knowledgePoint.getVideoCollection();

                // 在事务内提前获取所有懒加载属性，避免懒加载异常
                String videoCollectionName = videoCollection.getName();
                String description = videoCollection.getDescription();
                String coverImageUrl = videoCollection.getCoverImageUrl();

                // 获取视频合集中的视频数量
                int videoCount = videoCollectionService.getVideoCountInCollection(videoCollection.getId());

                Map<String, Object> result = new HashMap<>();
                result.put("hasVideo", true);
                result.put("videoCollectionId", videoCollection.getId());
                result.put("videoCollectionName", videoCollectionName);
                result.put("description", description);
                result.put("coverImageUrl", coverImageUrl);
                result.put("videoCount", videoCount);

                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.ok(Map.of("hasVideo", false, "videoCount", 0));
            }

        } catch (Exception e) {
            log.error("获取知识点视频信息失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "获取视频信息失败: " + e.getMessage()));
        }
    }

    /**
     * 获取视频集合详细信息（包含所有视频）
     */
    @GetMapping("/video-collections/{id}/details")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    @Transactional(readOnly = true)
    public ResponseEntity<?> getVideoCollectionDetails(@PathVariable Long id) {
        try {
            VideoCollection videoCollection = videoCollectionService.getVideoCollectionById(id);

            // 在事务内提前获取所有懒加载属性，避免懒加载异常
            String name = videoCollection.getName();
            String description = videoCollection.getDescription();
            String coverImageUrl = videoCollection.getCoverImageUrl();

            // 获取集合中的所有视频
            List<CollectionVideo> collectionVideos = collectionVideoRepository.findByCollectionIdOrderByCreatedAtAsc(id);

            Map<String, Object> result = new HashMap<>();
            result.put("id", videoCollection.getId());
            result.put("name", name);
            result.put("description", description);
            result.put("coverImageUrl", coverImageUrl);
            result.put("createdAt", videoCollection.getCreatedAt());
            result.put("updatedAt", videoCollection.getUpdatedAt());

            // 构建视频列表
            List<Map<String, Object>> videos = new ArrayList<>();
            for (CollectionVideo cv : collectionVideos) {
                if (cv.getVideo() != null) {
                    Video video = cv.getVideo();
                    // 在事务内提前获取视频的懒加载属性
                    String videoTitle = video.getTitle();
                    String videoUrl = video.getVideoUrl();
                    String videoCoverImageUrl = video.getCoverImageUrl();

                    Map<String, Object> videoInfo = new HashMap<>();
                    videoInfo.put("id", video.getId());
                    videoInfo.put("title", videoTitle);
                    videoInfo.put("videoUrl", videoUrl);
                    videoInfo.put("coverImageUrl", videoCoverImageUrl);
                    videoInfo.put("createdAt", video.getCreatedAt());
                    videos.add(videoInfo);
                }
            }
            result.put("videos", videos);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取视频集合详情失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "获取视频集合详情失败: " + e.getMessage()));
        }
    }

    /**
     * 从视频集合中移除单个视频
     */
    @DeleteMapping("/video-collections/{collectionId}/videos/{videoId}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    @Transactional
    public ResponseEntity<?> removeVideoFromCollection(@PathVariable Long collectionId, @PathVariable Long videoId) {
        try {
            // 检查关联记录是否存在
            Optional<CollectionVideo> collectionVideo = collectionVideoRepository.findByCollectionIdAndVideoId(collectionId, videoId);
            if (collectionVideo.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "视频不在该集合中"));
            }

            // 删除关联记录
            collectionVideoRepository.delete(collectionVideo.get());

            // 检查视频是否还被其他集合引用
            long referenceCount = collectionVideoRepository.countByVideoId(videoId);
            if (referenceCount == 0) {
                // 如果没有其他引用，删除视频记录和七牛云文件
                Video video = videoService.getVideoById(videoId);
                if (video != null && video.getVideoUrl() != null) {
                    // 删除七牛云文件
                    boolean deleted = qiniuUploadService.deleteFile(video.getVideoUrl());
                    if (deleted) {
                        log.info("七牛云视频文件删除成功: {}", video.getVideoUrl());
                    } else {
                        log.warn("七牛云视频文件删除失败: {}", video.getVideoUrl());
                    }
                }

                // 删除视频记录
                videoService.deleteVideo(videoId);
                log.info("删除孤立视频记录: videoId={}", videoId);
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频移除成功"
            ));

        } catch (Exception e) {
            log.error("从视频集合移除视频失败", e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "移除视频失败: " + e.getMessage()));
        }
    }
}
