package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.Question;
import com.example.adminbackend.model.PracticeConfig;
import com.example.adminbackend.model.KnowledgePoint;
import com.example.adminbackend.repository.QuestionRepository;
import com.example.adminbackend.repository.KnowledgePointRepository;
import com.example.adminbackend.repository.StudentAnswerRepository;
import com.example.adminbackend.service.QuestionSelectionService;
import com.example.adminbackend.service.PracticeConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目选择服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuestionSelectionServiceImpl implements QuestionSelectionService {

    private final QuestionRepository questionRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final StudentAnswerRepository studentAnswerRepository;
    private final PracticeConfigService practiceConfigService;

    // 默认配置常量
    private static final int DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT = 10;
    private static final int DEFAULT_CHAPTER_TEST_DIVISOR = 10;
    private static final int MIN_CHAPTER_TEST_QUESTIONS_PER_POINT = 1;

    // ==================== 核心抽题方法 ====================

    @Override
    public List<Question> selectQuestionsForKnowledgePointPractice(Long knowledgePointId, Long studentId) {
        log.info("为知识点练习选择题目: knowledgePointId={}, studentId={}", knowledgePointId, studentId);

        try {
            // 1. 获取知识点的所有启用题目
            List<Question> allQuestions = questionRepository.findByKnowledgePointIdAndEnabledTrue(knowledgePointId);
            if (allQuestions.isEmpty()) {
                log.warn("知识点没有可用题目: knowledgePointId={}", knowledgePointId);
                return new ArrayList<>();
            }

            // 2. 获取有效的题目数量配置
            Integer questionCount = getEffectivePracticeCount(knowledgePointId);
            
            // 3. 获取有效的抽题策略
            PracticeConfig.SelectionStrategy strategy = getEffectiveSelectionStrategy(knowledgePointId);

            // 4. 根据策略选择题目
            List<Question> selectedQuestions = selectQuestionsByStrategy(allQuestions, questionCount, strategy, studentId);

            log.info("知识点练习题目选择完成: knowledgePointId={}, 总题目数={}, 选择数量={}, 实际选择={}", 
                    knowledgePointId, allQuestions.size(), questionCount, selectedQuestions.size());

            return selectedQuestions;

        } catch (Exception e) {
            log.error("知识点练习题目选择失败: knowledgePointId={}", knowledgePointId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Question> selectQuestionsForChapterTest(Long chapterId, Long studentId) {
        log.info("为章节测试选择题目: chapterId={}, studentId={}", chapterId, studentId);

        try {
            // 1. 获取章节下的所有知识点
            List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdAndEnabledTrueOrderByOrderIndexAsc(chapterId);
            if (knowledgePoints.isEmpty()) {
                log.warn("章节没有可用知识点: chapterId={}", chapterId);
                return new ArrayList<>();
            }

            // 2. 获取题目分配方案
            Map<Long, Integer> allocation = getChapterTestQuestionAllocation(chapterId);

            // 3. 为每个知识点选择题目
            List<Question> allSelectedQuestions = new ArrayList<>();
            for (KnowledgePoint kp : knowledgePoints) {
                Integer count = allocation.get(kp.getId());
                if (count != null && count > 0) {
                    List<Question> questions = questionRepository.findByKnowledgePointIdAndEnabledTrue(kp.getId());
                    if (!questions.isEmpty()) {
                        PracticeConfig.SelectionStrategy strategy = getEffectiveSelectionStrategy(kp.getId());
                        List<Question> selected = selectQuestionsByStrategy(questions, count, strategy, studentId);
                        allSelectedQuestions.addAll(selected);
                    }
                }
            }

            // 4. 打乱题目顺序
            Collections.shuffle(allSelectedQuestions);

            log.info("章节测试题目选择完成: chapterId={}, 知识点数={}, 总选择题目数={}", 
                    chapterId, knowledgePoints.size(), allSelectedQuestions.size());

            return allSelectedQuestions;

        } catch (Exception e) {
            log.error("章节测试题目选择失败: chapterId={}", chapterId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Question> selectQuestionsByStrategy(List<Question> questions, int count, 
                                                   PracticeConfig.SelectionStrategy strategy, Long studentId) {
        if (questions.isEmpty() || count <= 0) {
            return new ArrayList<>();
        }

        // 确保不超过可用题目数量
        int actualCount = Math.min(count, questions.size());

        return switch (strategy) {
            case RANDOM -> randomSelection(questions, actualCount);
            case DIFFICULTY_BALANCED -> difficultyBalancedSelection(questions, actualCount);
            case ERROR_PRIORITY -> errorPrioritySelection(questions, actualCount, studentId);
            case TYPE_BALANCED -> typeBalancedSelection(questions, actualCount);
        };
    }

    // ==================== 配置获取方法 ====================

    @Override
    public Integer getEffectivePracticeCount(Long knowledgePointId) {
        try {
            // 1. 检查知识点自定义配置
            KnowledgePoint knowledgePoint = knowledgePointRepository.findById(knowledgePointId).orElse(null);
            if (knowledgePoint != null && knowledgePoint.getEffectivePracticeCount() != null) {
                log.debug("使用知识点自定义配置: knowledgePointId={}, count={}", 
                        knowledgePointId, knowledgePoint.getEffectivePracticeCount());
                return knowledgePoint.getEffectivePracticeCount();
            }

            // 2. 查找系统配置
            Optional<PracticeConfig> config = practiceConfigService.getEffectiveConfigForKnowledgePoint(
                    knowledgePointId, PracticeConfig.ConfigType.KNOWLEDGE_POINT_PRACTICE);
            
            if (config.isPresent()) {
                log.debug("使用系统配置: knowledgePointId={}, scopeType={}, count={}", 
                        knowledgePointId, config.get().getScopeType(), config.get().getQuestionCount());
                return config.get().getQuestionCount();
            }

            // 3. 使用默认配置
            log.debug("使用默认配置: knowledgePointId={}, count={}", knowledgePointId, DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT);
            return DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT;

        } catch (Exception e) {
            log.error("获取有效练习题目数量失败: knowledgePointId={}", knowledgePointId, e);
            return DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT;
        }
    }

    @Override
    public Integer calculateChapterTestQuestionCount(Long knowledgePointId, Integer totalQuestions) {
        if (totalQuestions == null || totalQuestions <= 0) {
            return 0;
        }

        // 章节测试规则：总题目数 ÷ 10，最小值为1
        int calculatedCount = Math.max(totalQuestions / DEFAULT_CHAPTER_TEST_DIVISOR, MIN_CHAPTER_TEST_QUESTIONS_PER_POINT);
        
        // 不能超过总题目数
        return Math.min(calculatedCount, totalQuestions);
    }

    @Override
    public PracticeConfig.SelectionStrategy getEffectiveSelectionStrategy(Long knowledgePointId) {
        try {
            Optional<PracticeConfig> config = practiceConfigService.getEffectiveConfigForKnowledgePoint(
                    knowledgePointId, PracticeConfig.ConfigType.KNOWLEDGE_POINT_PRACTICE);
            
            if (config.isPresent() && config.get().getSelectionStrategy() != null) {
                return config.get().getSelectionStrategy();
            }

            return PracticeConfig.SelectionStrategy.RANDOM; // 默认策略

        } catch (Exception e) {
            log.error("获取有效抽题策略失败: knowledgePointId={}", knowledgePointId, e);
            return PracticeConfig.SelectionStrategy.RANDOM;
        }
    }

    // ==================== 章节测试相关 ====================

    @Override
    public Map<Long, Integer> getChapterTestQuestionAllocation(Long chapterId) {
        Map<Long, Integer> allocation = new HashMap<>();

        try {
            List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdAndEnabledTrueOrderByOrderIndexAsc(chapterId);
            
            for (KnowledgePoint kp : knowledgePoints) {
                List<Question> questions = questionRepository.findByKnowledgePointIdAndEnabledTrue(kp.getId());
                int totalQuestions = questions.size();
                int allocatedCount = calculateChapterTestQuestionCount(kp.getId(), totalQuestions);
                allocation.put(kp.getId(), allocatedCount);
            }

        } catch (Exception e) {
            log.error("获取章节测试题目分配方案失败: chapterId={}", chapterId, e);
        }

        return allocation;
    }

    @Override
    public Integer calculateChapterTestTotalCount(Long chapterId) {
        Map<Long, Integer> allocation = getChapterTestQuestionAllocation(chapterId);
        return allocation.values().stream().mapToInt(Integer::intValue).sum();
    }

    // ==================== 抽题策略实现 ====================

    @Override
    public List<Question> randomSelection(List<Question> questions, int count) {
        List<Question> shuffled = new ArrayList<>(questions);
        Collections.shuffle(shuffled);
        return shuffled.subList(0, Math.min(count, shuffled.size()));
    }

    @Override
    public List<Question> difficultyBalancedSelection(List<Question> questions, int count) {
        // 按难度分组
        Map<String, List<Question>> difficultyGroups = questions.stream()
                .collect(Collectors.groupingBy(q -> 
                        q.getDifficulty() != null ? q.getDifficulty() : "UNKNOWN"));

        List<Question> selected = new ArrayList<>();
        int perGroupCount = Math.max(1, count / difficultyGroups.size());

        for (List<Question> group : difficultyGroups.values()) {
            Collections.shuffle(group);
            int selectCount = Math.min(group.size(), perGroupCount);
            selected.addAll(group.subList(0, selectCount));

            if (selected.size() >= count) break;
        }

        // 如果还没选够，随机补充
        if (selected.size() < count) {
            List<Question> remaining = questions.stream()
                    .filter(q -> !selected.contains(q))
                    .collect(Collectors.toList());
            Collections.shuffle(remaining);
            int needMore = count - selected.size();
            selected.addAll(remaining.subList(0, Math.min(needMore, remaining.size())));
        }

        return selected.subList(0, Math.min(selected.size(), count));
    }

    @Override
    public List<Question> errorPrioritySelection(List<Question> questions, int count, Long studentId) {
        if (studentId == null) {
            // 如果没有学生ID，降级为随机选择
            return randomSelection(questions, count);
        }

        try {
            // 获取学生错题ID集合
            List<Long> wrongQuestionIds = getStudentWrongQuestionIds(studentId, null);
            Set<Long> wrongIdSet = new HashSet<>(wrongQuestionIds);

            // 分离错题和其他题目
            List<Question> wrongQuestions = questions.stream()
                    .filter(q -> wrongIdSet.contains(q.getId()))
                    .collect(Collectors.toList());

            List<Question> otherQuestions = questions.stream()
                    .filter(q -> !wrongIdSet.contains(q.getId()))
                    .collect(Collectors.toList());

            // 优先选择错题，不足时补充其他题目
            List<Question> selected = new ArrayList<>();
            Collections.shuffle(wrongQuestions);
            Collections.shuffle(otherQuestions);

            int wrongCount = Math.min(wrongQuestions.size(), count);
            selected.addAll(wrongQuestions.subList(0, wrongCount));

            int remainingCount = count - selected.size();
            if (remainingCount > 0 && !otherQuestions.isEmpty()) {
                int otherCount = Math.min(otherQuestions.size(), remainingCount);
                selected.addAll(otherQuestions.subList(0, otherCount));
            }

            return selected;

        } catch (Exception e) {
            log.error("错题优先选择失败，降级为随机选择: studentId={}", studentId, e);
            return randomSelection(questions, count);
        }
    }

    @Override
    public List<Question> typeBalancedSelection(List<Question> questions, int count) {
        // 按题型分组
        Map<Question.QuestionType, List<Question>> typeGroups = questions.stream()
                .collect(Collectors.groupingBy(Question::getQuestionType));

        List<Question> selected = new ArrayList<>();
        int perTypeCount = Math.max(1, count / typeGroups.size());

        for (List<Question> group : typeGroups.values()) {
            Collections.shuffle(group);
            int selectCount = Math.min(group.size(), perTypeCount);
            selected.addAll(group.subList(0, selectCount));

            if (selected.size() >= count) break;
        }

        // 如果还没选够，随机补充
        if (selected.size() < count) {
            List<Question> remaining = questions.stream()
                    .filter(q -> !selected.contains(q))
                    .collect(Collectors.toList());
            Collections.shuffle(remaining);
            int needMore = count - selected.size();
            selected.addAll(remaining.subList(0, Math.min(needMore, remaining.size())));
        }

        return selected.subList(0, Math.min(selected.size(), count));
    }

    // ==================== 辅助方法 ====================

    @Override
    public List<Long> getStudentWrongQuestionIds(Long studentId, Long knowledgePointId) {
        try {
            if (knowledgePointId != null) {
                // 获取特定知识点的错题
                return studentAnswerRepository.findWrongQuestionIdsByStudentAndKnowledgePoint(studentId, knowledgePointId);
            } else {
                // 获取学生所有错题
                return studentAnswerRepository.findWrongQuestionIdsByStudent(studentId);
            }
        } catch (Exception e) {
            log.error("获取学生错题ID失败: studentId={}, knowledgePointId={}", studentId, knowledgePointId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean validateSelectionResult(List<Question> selectedQuestions, int expectedCount) {
        if (selectedQuestions == null) {
            return false;
        }

        // 检查数量是否合理（允许少于期望数量，但不能为0）
        if (selectedQuestions.isEmpty()) {
            return false;
        }

        // 检查是否有重复题目
        Set<Long> questionIds = selectedQuestions.stream()
                .map(Question::getId)
                .collect(Collectors.toSet());

        return questionIds.size() == selectedQuestions.size();
    }

    @Override
    public Map<String, Object> getSelectionStatistics(Long knowledgePointId) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取知识点基本信息
            KnowledgePoint knowledgePoint = knowledgePointRepository.findById(knowledgePointId).orElse(null);
            if (knowledgePoint != null) {
                stats.put("knowledgePointName", knowledgePoint.getName());
                stats.put("knowledgePointId", knowledgePointId);
            }

            // 获取题目统计
            List<Question> allQuestions = questionRepository.findByKnowledgePointIdAndEnabledTrue(knowledgePointId);
            stats.put("totalQuestions", allQuestions.size());

            // 按题型统计
            Map<Question.QuestionType, Long> typeCount = allQuestions.stream()
                    .collect(Collectors.groupingBy(Question::getQuestionType, Collectors.counting()));
            stats.put("questionsByType", typeCount);

            // 按难度统计
            Map<String, Long> difficultyCount = allQuestions.stream()
                    .collect(Collectors.groupingBy(
                            q -> q.getDifficulty() != null ? q.getDifficulty() : "UNKNOWN",
                            Collectors.counting()));
            stats.put("questionsByDifficulty", difficultyCount);

            // 获取有效配置
            Integer practiceCount = getEffectivePracticeCount(knowledgePointId);
            stats.put("effectivePracticeCount", practiceCount);

            PracticeConfig.SelectionStrategy strategy = getEffectiveSelectionStrategy(knowledgePointId);
            stats.put("effectiveStrategy", strategy.name());

            // 章节测试配置
            if (knowledgePoint != null) {
                Integer chapterTestCount = calculateChapterTestQuestionCount(knowledgePointId, allQuestions.size());
                stats.put("chapterTestCount", chapterTestCount);
            }

        } catch (Exception e) {
            log.error("获取题目选择统计信息失败: knowledgePointId={}", knowledgePointId, e);
            stats.put("error", "获取统计信息失败: " + e.getMessage());
        }

        return stats;
    }
}
