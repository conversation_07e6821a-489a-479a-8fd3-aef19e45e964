import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  MenuBook,
  Refresh,
} from '@mui/icons-material';
import { PracticeConfigAPI, PracticeConfig } from '../../services/practiceConfigApi';
import CurriculumDataService, { SimpleSubject, SimpleChapter } from '../../services/curriculumDataService';

interface ChapterConfigTabProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

// 使用CurriculumDataService中的类型
type Subject = SimpleSubject;
type Chapter = SimpleChapter;

interface ConfigForm {
  chapterId: number;
  configType: 'KNOWLEDGE_POINT_PRACTICE' | 'CHAPTER_TEST';
  questionCount: number;
  selectionStrategy: 'RANDOM' | 'DIFFICULTY_BALANCED' | 'ERROR_PRIORITY' | 'TYPE_BALANCED';
}

const ChapterConfigTab: React.FC<ChapterConfigTabProps> = ({ onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [configs, setConfigs] = useState<PracticeConfig[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingConfig, setEditingConfig] = useState<PracticeConfig | null>(null);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [formData, setFormData] = useState<ConfigForm>({
    chapterId: 0,
    configType: 'KNOWLEDGE_POINT_PRACTICE',
    questionCount: 10,
    selectionStrategy: 'RANDOM',
  });

  // 策略选项
  const strategyOptions = [
    { value: 'RANDOM', label: '随机选择' },
    { value: 'DIFFICULTY_BALANCED', label: '难度均衡' },
    { value: 'ERROR_PRIORITY', label: '错题优先' },
    { value: 'TYPE_BALANCED', label: '题型均衡' },
  ];

  // 配置类型选项
  const configTypeOptions = [
    { value: 'KNOWLEDGE_POINT_PRACTICE', label: '知识点练习' },
    { value: 'CHAPTER_TEST', label: '章节测试' },
  ];

  // 加载科目列表
  const loadSubjects = async () => {
    try {
      const subjects = await CurriculumDataService.getSubjects();
      setSubjects(subjects);
    } catch (error: any) {
      console.error('加载科目列表失败:', error);
      onError('加载科目列表失败: ' + (error.message || '未知错误'));
    }
  };

  // 加载章节列表
  const loadChapters = async () => {
    try {
      const chapters = await CurriculumDataService.getChapters();
      setChapters(chapters);
    } catch (error: any) {
      console.error('加载章节列表失败:', error);
      onError('加载章节列表失败: ' + (error.message || '未知错误'));
    }
  };

  // 加载章节配置
  const loadChapterConfigs = async () => {
    try {
      setLoading(true);
      const allConfigs = await PracticeConfigAPI.getAllEnabledConfigs();
      const chapterConfigs = allConfigs.filter(config => config.scopeType === 'CHAPTER');
      setConfigs(chapterConfigs);
    } catch (error: any) {
      console.error('加载章节配置失败:', error);
      onError('加载章节配置失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取章节信息
  const getChapterInfo = (chapterId: number) => {
    const chapter = chapters.find(c => c.id === chapterId);
    return chapter || { id: chapterId, name: `章节${chapterId}`, subjectName: '未知科目' };
  };

  // 获取策略标签
  const getStrategyLabel = (strategy: string) => {
    const option = strategyOptions.find(opt => opt.value === strategy);
    return option?.label || strategy;
  };

  // 获取配置类型标签
  const getConfigTypeLabel = (configType: string) => {
    const option = configTypeOptions.find(opt => opt.value === configType);
    return option?.label || configType;
  };

  // 获取过滤后的章节列表
  const getFilteredChapters = () => {
    if (!selectedSubject) return chapters;
    return chapters.filter(chapter => chapter.subjectId === selectedSubject.id);
  };

  // 打开新增对话框
  const handleAdd = () => {
    setEditingConfig(null);
    setSelectedSubject(null);
    setFormData({
      chapterId: 0,
      configType: 'KNOWLEDGE_POINT_PRACTICE',
      questionCount: 10,
      selectionStrategy: 'RANDOM',
    });
    setOpenDialog(true);
  };

  // 打开编辑对话框
  const handleEdit = (config: PracticeConfig) => {
    setEditingConfig(config);
    const chapterInfo = getChapterInfo(config.targetId!);
    const subject = subjects.find(s => s.name === chapterInfo.subjectName);
    setSelectedSubject(subject || null);
    setFormData({
      chapterId: config.targetId || 0,
      configType: config.configType,
      questionCount: config.questionCount,
      selectionStrategy: config.selectionStrategy,
    });
    setOpenDialog(true);
  };

  // 删除配置
  const handleDelete = async (config: PracticeConfig) => {
    if (!window.confirm('确定要删除这个配置吗？')) {
      return;
    }

    try {
      await PracticeConfigAPI.deleteConfig(config.id!);
      onSuccess('配置删除成功');
      await loadChapterConfigs();
    } catch (error: any) {
      console.error('删除配置失败:', error);
      onError('删除配置失败: ' + (error.message || '未知错误'));
    }
  };

  // 保存配置
  const handleSave = async () => {
    if (formData.chapterId === 0) {
      onError('请选择章节');
      return;
    }

    try {
      setSaving(true);
      
      if (editingConfig) {
        // 更新配置
        await PracticeConfigAPI.updateConfig(editingConfig.id!, {
          questionCount: formData.questionCount,
          selectionStrategy: formData.selectionStrategy,
        });
        onSuccess('配置更新成功');
      } else {
        // 创建新配置
        await PracticeConfigAPI.createOrUpdateChapterConfig(
          formData.chapterId,
          formData.configType,
          formData.questionCount,
          formData.selectionStrategy
        );
        onSuccess('配置创建成功');
      }

      setOpenDialog(false);
      await loadChapterConfigs();
    } catch (error: any) {
      console.error('保存配置失败:', error);
      onError('保存配置失败: ' + (error.message || '未知错误'));
    } finally {
      setSaving(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof ConfigForm, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  useEffect(() => {
    loadSubjects();
    loadChapters();
    loadChapterConfigs();
  }, []);

  return (
    <Box>
      {/* 页面说明 */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          章节配置会覆盖科目配置和全局配置，适用于该章节下的所有知识点。
          如果知识点有特定配置，则优先使用知识点配置。
        </Typography>
      </Alert>

      {/* 操作栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <MenuBook sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" fontWeight="bold">
            章节配置管理
          </Typography>
        </Box>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={loadChapterConfigs}
            disabled={loading}
            sx={{ mr: 1 }}
          >
            刷新
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAdd}
          >
            新增配置
          </Button>
        </Box>
      </Box>

      {/* 配置列表 */}
      <Card>
        <CardContent sx={{ p: 0 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>章节</TableCell>
                    <TableCell>科目</TableCell>
                    <TableCell>配置类型</TableCell>
                    <TableCell>题目数量</TableCell>
                    <TableCell>抽题策略</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>创建时间</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {configs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Typography color="text.secondary">
                          暂无章节配置
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    configs.map((config) => {
                      const chapterInfo = getChapterInfo(config.targetId!);
                      return (
                        <TableRow key={config.id}>
                          <TableCell>
                            <Typography fontWeight="medium">
                              {chapterInfo.name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={chapterInfo.subjectName}
                              color="secondary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getConfigTypeLabel(config.configType)}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography fontWeight="medium">
                              {config.questionCount}道
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {getStrategyLabel(config.selectionStrategy)}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={config.enabled ? '已启用' : '已禁用'}
                              color={config.enabled ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {config.createdAt ? new Date(config.createdAt).toLocaleDateString() : '-'}
                          </TableCell>
                          <TableCell align="center">
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(config)}
                              color="primary"
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(config)}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* 配置对话框 */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingConfig ? '编辑章节配置' : '新增章节配置'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={12}>
              <Autocomplete
                options={subjects}
                getOptionLabel={(option) => option.name}
                value={selectedSubject}
                onChange={(event, newValue) => {
                  setSelectedSubject(newValue);
                  setFormData(prev => ({ ...prev, chapterId: 0 }));
                }}
                disabled={!!editingConfig}
                renderInput={(params) => (
                  <TextField {...params} label="科目" fullWidth />
                )}
              />
            </Grid>
            <Grid size={12}>
              <FormControl fullWidth disabled={!!editingConfig}>
                <InputLabel>章节</InputLabel>
                <Select
                  value={formData.chapterId}
                  label="章节"
                  onChange={(e) => handleFieldChange('chapterId', e.target.value)}
                >
                  <MenuItem value={0}>请选择章节</MenuItem>
                  {getFilteredChapters().map((chapter) => (
                    <MenuItem key={chapter.id} value={chapter.id}>
                      {chapter.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={12}>
              <FormControl fullWidth disabled={!!editingConfig}>
                <InputLabel>配置类型</InputLabel>
                <Select
                  value={formData.configType}
                  label="配置类型"
                  onChange={(e) => handleFieldChange('configType', e.target.value)}
                >
                  {configTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="题目数量"
                type="number"
                value={formData.questionCount}
                onChange={(e) => handleFieldChange('questionCount', parseInt(e.target.value) || 1)}
                inputProps={{ min: 1, max: 100 }}
                helperText="建议设置为5-50道题目"
              />
            </Grid>
            <Grid size={12}>
              <FormControl fullWidth>
                <InputLabel>抽题策略</InputLabel>
                <Select
                  value={formData.selectionStrategy}
                  label="抽题策略"
                  onChange={(e) => handleFieldChange('selectionStrategy', e.target.value)}
                >
                  {strategyOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? '保存中...' : '保存'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChapterConfigTab;
