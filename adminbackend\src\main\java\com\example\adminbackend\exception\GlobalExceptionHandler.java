package com.example.adminbackend.exception;

import com.example.adminbackend.dto.ApiResponse;
import com.example.adminbackend.dto.ErrorDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    // ==================== 业务异常处理 ====================

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Void>> handleBusinessException(
            BusinessException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("业务异常 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI(), ex);

        ApiResponse<Void> response = ApiResponse.<Void>error(
                ex.getErrorCodeValue(),
                ex.getMessage(),
                ex.getErrorDetails()
        ).withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(getHttpStatus(ex.getErrorCodeValue())).body(response);
    }

    // ==================== 参数验证异常处理 ====================

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationExceptions(
            MethodArgumentNotValidException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("参数验证失败 [{}]: 请求路径: {}", requestId, request.getRequestURI());

        List<ErrorDetail> errorDetails = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            Object rejectedValue = ((FieldError) error).getRejectedValue();

            errorDetails.add(ErrorDetail.fieldError(fieldName, rejectedValue, errorMessage));
        });

        ApiResponse<Object> response = ApiResponse.validationError("参数验证失败", errorDetails)
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("缺少请求参数 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        List<ErrorDetail> errorDetails = List.of(
                ErrorDetail.fieldError(ex.getParameterName(), null, "缺少必需的请求参数")
        );

        ApiResponse<Object> response = ApiResponse.validationError("缺少必需的请求参数: " + ex.getParameterName(), errorDetails)
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodArgumentTypeMismatch(
            MethodArgumentTypeMismatchException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("参数类型不匹配 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        List<ErrorDetail> errorDetails = List.of(
                ErrorDetail.fieldError(ex.getName(), ex.getValue(),
                        String.format("参数类型不匹配，期望类型: %s", ex.getRequiredType().getSimpleName()))
        );

        ApiResponse<Object> response = ApiResponse.validationError("参数类型不匹配", errorDetails)
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("JSON解析失败 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.error(400, "请求体格式错误，请检查JSON格式")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    // ==================== 认证授权异常处理 ====================

    /**
     * 处理用户名不存在异常
     */
    @ExceptionHandler(UsernameNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleUsernameNotFoundException(
            UsernameNotFoundException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("用户不存在 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.notFound("用户不存在")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理认证失败异常
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadCredentialsException(
            BadCredentialsException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("认证失败 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.unauthorized("用户名或密码错误")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    /**
     * 处理访问拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(
            AccessDeniedException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("访问被拒绝 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.forbidden("没有权限访问此资源")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    // ==================== HTTP方法异常处理 ====================

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpRequestMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("请求方法不支持 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.error(405, "请求方法不支持: " + ex.getMethod())
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    // ==================== 文件上传异常处理 ====================

    /**
     * 处理文件大小超出限制异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<Object>> handleMaxUploadSizeExceeded(
            MaxUploadSizeExceededException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("文件大小超出限制 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.error(413, "文件大小超出限制")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }

    // ==================== 迁移异常处理 ====================

    /**
     * 处理数据迁移异常
     */
    @ExceptionHandler(com.example.adminbackend.exception.MigrationException.class)
    public ResponseEntity<ApiResponse<Object>> handleMigrationException(
            com.example.adminbackend.exception.MigrationException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.error("数据迁移异常 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI(), ex);

        ApiResponse<Object> response = ApiResponse.error(500, ex.getMessage())
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    // ==================== 数据库异常处理 ====================

    /**
     * 处理数据完整性违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataIntegrityViolation(
            DataIntegrityViolationException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.error("数据完整性违反 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI(), ex);

        String message = "数据操作失败，请检查数据完整性";
        if (ex.getMessage().contains("Duplicate entry")) {
            message = "数据重复，该记录已存在";
        } else if (ex.getMessage().contains("foreign key constraint")) {
            message = "数据关联错误，请检查相关数据";
        }

        ApiResponse<Object> response = ApiResponse.error(409, message)
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    /**
     * 处理重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateKey(
            DuplicateKeyException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.warn("重复键异常 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI());

        ApiResponse<Object> response = ApiResponse.error(409, "数据重复，该记录已存在")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    /**
     * 处理数据访问异常
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataAccessException(
            DataAccessException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.error("数据访问异常 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI(), ex);

        ApiResponse<Object> response = ApiResponse.internalError("数据库操作失败")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    // ==================== 通用异常处理 ====================

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.error("运行时异常 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI(), ex);

        ApiResponse<Object> response = ApiResponse.internalError("系统内部错误: " + ex.getMessage())
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleAllExceptions(
            Exception ex, HttpServletRequest request) {

        String requestId = generateRequestId();
        log.error("未处理异常 [{}]: {}, 请求路径: {}", requestId, ex.getMessage(), request.getRequestURI(), ex);

        ApiResponse<Object> response = ApiResponse.internalError("系统发生未知错误")
                .withRequestInfo(request.getRequestURI(), requestId);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    // ==================== 辅助方法 ====================

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 根据错误码获取HTTP状态码
     */
    private HttpStatus getHttpStatus(int errorCode) {
        if (errorCode >= 200 && errorCode < 300) {
            return HttpStatus.OK;
        } else if (errorCode >= 400 && errorCode < 500) {
            return HttpStatus.valueOf(errorCode);
        } else if (errorCode >= 500) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        } else {
            // 自定义错误码映射
            if (errorCode >= 1000 && errorCode < 2000) {
                return HttpStatus.BAD_REQUEST;
            } else if (errorCode >= 2000 && errorCode < 3000) {
                return HttpStatus.UNAUTHORIZED;
            } else if (errorCode >= 3000 && errorCode < 6000) {
                return HttpStatus.BAD_REQUEST;
            } else if (errorCode >= 7000 && errorCode < 8000) {
                return HttpStatus.INTERNAL_SERVER_ERROR;
            } else {
                return HttpStatus.INTERNAL_SERVER_ERROR;
            }
        }
    }
}