import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  Snackbar,
  TextField,
  Grid,
  Tabs,
  Tab,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Code as CodeIcon,
  Assessment as AssessmentIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import MigrationProgress from '../components/migration/MigrationProgress';
import MigrationResult from '../components/migration/MigrationResult';
import { migrationAPI } from '../services/api';
import {
  MigrationProgress as MigrationProgressType,
  MigrationResult as MigrationResultType,
  MigrationStatus,
} from '../types/migration';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`migration-tabpanel-${index}`}
      aria-labelledby={`migration-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}



const MigrationPage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [jsonData, setJsonData] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileUploading, setFileUploading] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState<MigrationProgressType>({
    status: MigrationStatus.IDLE,
    processedCount: 0,
    totalCount: 0,
    errorCount: 0,
  });
  const [migrationResult, setMigrationResult] = useState<MigrationResultType | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' as any });

  // 轮询迁移进度
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (migrationProgress.status === MigrationStatus.PROCESSING ||
        migrationProgress.status === MigrationStatus.UPLOADING ||
        migrationProgress.status === MigrationStatus.VALIDATING) {
      interval = setInterval(async () => {
        try {
          const response = await migrationAPI.getMigrationProgress();
          if (response.success && response.data) {
            setMigrationProgress(response.data);
            
            // 如果迁移完成，获取结果
            if (response.data.status === MigrationStatus.COMPLETED ||
                response.data.status === MigrationStatus.FAILED) {
              setMigrationResult(response.data.result || null);
            }
          }
        } catch (error) {
          console.error('获取迁移进度失败:', error);
        }
      }, 2000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [migrationProgress.status]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      if (!file.name.toLowerCase().endsWith('.json')) {
        showSnackbar('请选择JSON文件', 'error');
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    setFileUploading(true);
    setMigrationProgress({
      status: MigrationStatus.UPLOADING,
      processedCount: 0,
      totalCount: 0,
      errorCount: 0,
    });

    try {
      // 读取文件内容
      const fileContent = await readFileAsText(selectedFile);

      // 使用JSON字符串迁移接口
      const response = await migrationAPI.migrateFromJsonString(fileContent);

      if (response.success) {
        setMigrationResult(response.data);
        setMigrationProgress({
          status: MigrationStatus.COMPLETED,
          processedCount: response.data.totalCount,
          totalCount: response.data.totalCount,
          errorCount: response.data.errorCount,
        });
        showSnackbar('文件迁移成功完成', 'success');
      } else {
        throw new Error(response.message || '迁移失败');
      }
    } catch (error: any) {
      setMigrationProgress({ status: MigrationStatus.FAILED, processedCount: 0, totalCount: 0, errorCount: 0 });
      showSnackbar('文件迁移失败: ' + error.message, 'error');
    } finally {
      setFileUploading(false);
    }
  };

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = (e) => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsText(file, 'utf-8');
    });
  };



  // 预处理JSON数据，支持单个对象或多个连续对象格式
  const preprocessJsonData = (rawJsonData: string): string => {
    if (!rawJsonData || !rawJsonData.trim()) {
      throw new Error('JSON数据不能为空');
    }

    const cleaned = rawJsonData.trim();

    if (!cleaned.startsWith('{')) {
      throw new Error('JSON数据必须以"{"开始（支持单个或多个连续的JSON对象）');
    }

    return cleaned; // 直接返回，让后端处理多个对象的解析
  };

  // 简单验证JSON格式，支持单个或多个连续对象
  const validateJsonFormat = (jsonString: string): { valid: boolean; error?: string; dataCount?: number } => {
    try {
      // 简单检查是否以{开始
      const trimmed = jsonString.trim();
      if (!trimmed.startsWith('{')) {
        return { valid: false, error: 'JSON数据必须以"{"开始' };
      }

      // 基本的括号匹配检查
      let braceCount = 0;
      for (let i = 0; i < trimmed.length; i++) {
        if (trimmed[i] === '{') braceCount++;
        if (trimmed[i] === '}') braceCount--;
      }

      if (braceCount !== 0) {
        return { valid: false, error: 'JSON对象括号不匹配' };
      }

      return { valid: true, dataCount: 1 }; // 前端无法准确计算数量，由后端处理
    } catch (error: any) {
      return { valid: false, error: `JSON格式错误: ${error.message}` };
    }
  };

  // 调试JSON数据
  const handleDebugJson = async () => {
    if (!jsonData.trim()) {
      showSnackbar('请输入JSON数据', 'warning');
      return;
    }

    try {
      const response = await migrationAPI.debugJsonFormat(jsonData);
      if (response.success) {
        const debugInfo = response.data;
        let message = `JSON调试信息:\n`;
        message += `===================\n`;
        message += `数据长度: ${debugInfo.dataLength} 字符\n`;
        message += `是否为空: ${debugInfo.isEmpty}\n`;
        message += `===================\n`;
        message += `以'{'开始: ${debugInfo.startsWithBrace}\n`;
        message += `以'}'结束: ${debugInfo.endsWithBrace}\n`;

        if (debugInfo.startsWithBrace) {
          message += `JSON类型: 对象格式 ✓\n`;
          if (debugInfo.hasContentAfterObject) {
            message += `对象后有额外内容: ${debugInfo.contentAfterObject}\n`;
          }
        } else {
          message += `JSON类型: 不支持的格式 ✗\n`;
          message += `提示: 系统支持单个或多个连续的JSON对象格式，请使用 {...} 或 {...}{...}\n`;
        }
        message += `===================\n`;

        if (debugInfo.validationResult) {
          message += `验证结果: ${debugInfo.validationResult.valid ? '通过' : '失败'}\n`;
          message += `验证信息: ${debugInfo.validationResult.message}\n`;
          if (debugInfo.validationResult.dataCount) {
            message += `数据数量: ${debugInfo.validationResult.dataCount}\n`;
          }
        }

        if (debugInfo.validationError) {
          message += `验证错误: ${debugInfo.validationError}\n`;
        }

        alert(message);
      } else {
        throw new Error(response.message || '调试失败');
      }
    } catch (error: any) {
      showSnackbar('调试失败: ' + error.message, 'error');
    }
  };

  const handleJsonMigration = async () => {
    if (!jsonData.trim()) {
      showSnackbar('请输入JSON数据', 'warning');
      return;
    }

    setMigrationProgress({
      status: MigrationStatus.VALIDATING,
      processedCount: 0,
      totalCount: 0,
      errorCount: 0,
    });

    try {
      // 1. 预处理JSON数据
      const cleanedJsonData = preprocessJsonData(jsonData);

      // 2. 前端格式验证
      const validation = validateJsonFormat(cleanedJsonData);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      console.log(`JSON格式验证通过，共 ${validation.dataCount} 条数据`);

      // 3. 后端格式验证
      const validateResponse = await migrationAPI.validateJsonFormat(cleanedJsonData);
      if (!validateResponse.success || !validateResponse.data.valid) {
        throw new Error(validateResponse.data.error || 'JSON格式验证失败');
      }

      // 4. 执行迁移
      setMigrationProgress(prev => ({ ...prev, status: MigrationStatus.PROCESSING }));
      const response = await migrationAPI.migrateFromJsonString(cleanedJsonData);

      if (response.success) {
        setMigrationResult(response.data);
        setMigrationProgress({
          status: MigrationStatus.COMPLETED,
          processedCount: response.data.totalCount,
          totalCount: response.data.totalCount,
          errorCount: response.data.errorCount,
        });
        showSnackbar('JSON数据迁移成功完成', 'success');
      } else {
        throw new Error(response.message || '迁移失败');
      }
    } catch (error: any) {
      setMigrationProgress({ status: MigrationStatus.FAILED, processedCount: 0, totalCount: 0, errorCount: 0 });
      showSnackbar('JSON迁移失败: ' + error.message, 'error');
    }
  };

  const handleStopMigration = async () => {
    try {
      await migrationAPI.stopMigration();
      setMigrationProgress(prev => ({ ...prev, status: MigrationStatus.CANCELLED }));
      showSnackbar('迁移任务已停止', 'info');
    } catch (error: any) {
      showSnackbar('停止迁移失败: ' + error.message, 'error');
    }
  };



  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const isProcessing = migrationProgress.status === MigrationStatus.PROCESSING ||
                     migrationProgress.status === MigrationStatus.UPLOADING ||
                     migrationProgress.status === MigrationStatus.VALIDATING ||
                     fileUploading;

  return (
    <Layout>
      <Box>
        {/* 页面标题 */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            数据迁移
          </Typography>
          <Typography variant="body1" color="text.secondary">
            将外部题库数据导入到系统中，支持单个或多个连续的JSON对象格式
          </Typography>
          <Typography variant="body2" color="info.main" sx={{ mt: 1 }}>
            支持格式：单个对象 {`{...}`} 或多个连续对象 {`{...}{...}{...}`}
          </Typography>
        </Paper>

        {/* 操作按钮 */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center" justifyContent="flex-end">
            {isProcessing ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<StopIcon />}
                onClick={handleStopMigration}
              >
                停止迁移
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => window.location.reload()}
              >
                重置
              </Button>
            )}
          </Grid>
        </Paper>

        {/* 迁移方式选择 */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={currentTab} onChange={handleTabChange}>
            <Tab
              icon={<CloudUploadIcon />}
              label="文件上传"
              disabled={isProcessing}
            />
            <Tab
              icon={<CodeIcon />}
              label="JSON输入"
              disabled={isProcessing}
            />
          </Tabs>

          <TabPanel value={currentTab} index={0}>
            {/* 文件上传 */}
            <Box sx={{ textAlign: 'center', p: 3 }}>
              <input
                accept=".json"
                style={{ display: 'none' }}
                id="json-file-upload"
                type="file"
                onChange={handleFileSelect}
                disabled={isProcessing}
              />
              <label htmlFor="json-file-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  disabled={isProcessing}
                  sx={{ mb: 2 }}
                >
                  选择JSON文件
                </Button>
              </label>

              {selectedFile && (
                <Box sx={{ mt: 2, mb: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    已选择文件: {selectedFile.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    文件大小: {(selectedFile.size / 1024).toFixed(2)} KB
                  </Typography>
                  <Button
                    variant="text"
                    color="error"
                    onClick={handleFileRemove}
                    disabled={isProcessing}
                    sx={{ mt: 1 }}
                  >
                    移除文件
                  </Button>
                </Box>
              )}

              <Box sx={{ mt: 3 }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<PlayArrowIcon />}
                  onClick={handleFileUpload}
                  disabled={!selectedFile || isProcessing}
                >
                  {fileUploading ? '上传中...' : '开始迁移'}
                </Button>
              </Box>
            </Box>
          </TabPanel>

          <TabPanel value={currentTab} index={1}>
            {/* JSON输入 */}
            <TextField
              fullWidth
              multiline
              rows={12}
              label="JSON数据"
              placeholder="请输入JSON对象格式的题目数据，支持单个对象或多个连续对象，例如：{ &quot;type&quot;: &quot;SINGLE_CHOICE&quot;, &quot;id&quot;: 1, ... }"
              value={jsonData}
              onChange={(e) => setJsonData(e.target.value)}
              disabled={isProcessing}
              sx={{ mb: 3 }}
            />
            <Box sx={{ textAlign: 'center', display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<AssessmentIcon />}
                onClick={handleDebugJson}
                disabled={!jsonData.trim() || isProcessing}
              >
                调试JSON
              </Button>
              <Button
                variant="contained"
                size="large"
                startIcon={<PlayArrowIcon />}
                onClick={handleJsonMigration}
                disabled={!jsonData.trim() || isProcessing}
              >
                开始迁移
              </Button>
            </Box>
          </TabPanel>
        </Paper>

        {/* 迁移进度 */}
        {(isProcessing || migrationProgress.status !== MigrationStatus.IDLE) && (
          <Box sx={{ mb: 3 }}>
            <MigrationProgress progress={migrationProgress} />
          </Box>
        )}

        {/* 迁移结果 */}
        {migrationResult && (
          <MigrationResult
            result={migrationResult}
            onRetry={() => {
              setMigrationResult(null);
              setMigrationProgress({
                status: MigrationStatus.IDLE,
                processedCount: 0,
                totalCount: 0,
                errorCount: 0,
              });
            }}
          />
        )}



        {/* 消息提示 */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default MigrationPage;
