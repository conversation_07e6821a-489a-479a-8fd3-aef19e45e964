import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Alert,
  IconButton,
  Typography,
  LinearProgress,
} from '@mui/material';
import { Close, CloudUpload, VideoFile } from '@mui/icons-material';
import { curriculumAPI } from '../services/api';

interface VideoUploadDialogProps {
  open: boolean;
  onClose: () => void;
  onUpload: (videoUrl: string) => void;
}

const VideoUploadDialog: React.FC<VideoUploadDialogProps> = ({ open, onClose, onUpload }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('video/')) {
      setError('请选择视频文件');
      return;
    }

    // 检查文件大小 (100MB)
    if (file.size > 100 * 1024 * 1024) {
      setError('视频文件大小不能超过100MB');
      return;
    }

    setSelectedFile(file);
    setError('');
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('请选择视频文件');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setError('');

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // 上传到七牛云
      const result = await curriculumAPI.uploadVideo(selectedFile);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success) {
        setTimeout(() => {
          onUpload(result.url);
          handleClose();
        }, 500);
      } else {
        throw new Error(result.error || '上传失败');
      }
    } catch (err: any) {
      setError('视频上传失败: ' + (err.message || '未知错误'));
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setUploading(false);
    setUploadProgress(0);
    setError('');
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <VideoFile sx={{ mr: 1, color: 'primary.main' }} />
            上传视频
          </Box>
          <IconButton onClick={handleClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ py: 2 }}>
          {/* 文件选择 */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <input
              type="file"
              accept="video/*"
              onChange={handleFileSelect}
              ref={fileInputRef}
              style={{ display: 'none' }}
            />
            <Button
              variant="outlined"
              onClick={() => fileInputRef.current?.click()}
              startIcon={<CloudUpload />}
              sx={{ mb: 2 }}
              disabled={uploading}
            >
              选择视频文件
            </Button>
            <Typography variant="body2" color="text.secondary">
              支持 MP4、AVI、MOV、WMV、FLV、WebM 格式，文件大小不超过 100MB
            </Typography>
          </Box>

          {/* 选中的文件信息 */}
          {selectedFile && (
            <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                <VideoFile sx={{ mr: 1, color: 'primary.main' }} />
                {selectedFile.name} ({formatFileSize(selectedFile.size)})
              </Typography>
            </Box>
          )}

          {/* 上传进度 */}
          {uploading && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                上传进度: {uploadProgress}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
          )}

          {/* 错误提示 */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={uploading}>
          取消
        </Button>
        <Button 
          onClick={handleUpload} 
          variant="contained" 
          disabled={!selectedFile || uploading}
        >
          {uploading ? '上传中...' : '上传视频'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VideoUploadDialog;
