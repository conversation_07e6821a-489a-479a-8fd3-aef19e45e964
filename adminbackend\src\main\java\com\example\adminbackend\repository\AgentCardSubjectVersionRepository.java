package com.example.adminbackend.repository;

import com.example.adminbackend.model.AgentCardSubjectVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 代理卡学科版本数据访问层
 */
@Repository
public interface AgentCardSubjectVersionRepository extends JpaRepository<AgentCardSubjectVersion, Long> {

    /**
     * 根据学科版本ID查找所有代理卡学科版本记录
     */
    List<AgentCardSubjectVersion> findBySubjectVersionId(Long subjectVersionId);

    /**
     * 根据学科版本ID删除所有相关记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM AgentCardSubjectVersion acsv WHERE acsv.subjectVersion.id = :subjectVersionId")
    void deleteBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 根据代理卡学科ID查找所有版本记录
     */
    List<AgentCardSubjectVersion> findByAgentCardSubjectId(Long agentCardSubjectId);

    /**
     * 检查学科版本是否被代理卡使用
     */
    boolean existsBySubjectVersionId(Long subjectVersionId);

    /**
     * 统计学科版本的使用数量
     */
    @Query("SELECT COUNT(acsv) FROM AgentCardSubjectVersion acsv WHERE acsv.subjectVersion.id = :subjectVersionId")
    long countBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);
}
