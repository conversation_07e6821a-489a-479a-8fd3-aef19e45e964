package com.example.adminbackend.util;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.example.adminbackend.dto.migration.SourceQuestionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * JSON格式验证工具类
 */
@Slf4j
@Component
public class JsonValidationUtil {
    
    private final ObjectMapper objectMapper;
    
    public JsonValidationUtil(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    /**
     * 验证JSON格式并提供详细错误信息，支持多个连续的JSON对象
     */
    public JsonValidationResult validateJsonFormat(String jsonData) {
        if (jsonData == null || jsonData.trim().isEmpty()) {
            return JsonValidationResult.error("JSON数据不能为空");
        }

        try {
            // 解析多个JSON对象
            List<SourceQuestionData> questionList = parseMultipleJsonObjects(jsonData.trim());

            if (questionList.isEmpty()) {
                return JsonValidationResult.error("未找到有效的JSON对象");
            }

            // 验证每个题目的必需字段
            for (int i = 0; i < questionList.size(); i++) {
                String fieldError = validateRequiredFields(questionList.get(i), i + 1);
                if (fieldError != null) {
                    return JsonValidationResult.error(fieldError);
                }
            }

            return JsonValidationResult.success(questionList.size(),
                String.format("JSON格式验证通过，共解析到%d个题目", questionList.size()));

        } catch (JsonParseException e) {
            return JsonValidationResult.error(buildJsonParseError(e));
        } catch (JsonMappingException e) {
            return JsonValidationResult.error(buildJsonMappingError(e));
        } catch (JsonProcessingException e) {
            return JsonValidationResult.error("JSON处理错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("JSON验证过程中发生未知错误", e);
            return JsonValidationResult.error("JSON验证失败: " + e.getMessage());
        }
    }

    /**
     * 解析多个连续的JSON对象
     */
    public List<SourceQuestionData> parseMultipleJsonObjects(String jsonData) throws JsonProcessingException {
        List<SourceQuestionData> questionList = new ArrayList<>();
        String data = jsonData.trim();

        int currentIndex = 0;
        while (currentIndex < data.length()) {
            // 跳过空白字符
            while (currentIndex < data.length() && Character.isWhitespace(data.charAt(currentIndex))) {
                currentIndex++;
            }

            if (currentIndex >= data.length()) {
                break;
            }

            // 查找JSON对象的开始
            if (data.charAt(currentIndex) != '{') {
                throw new IllegalArgumentException("JSON数据必须以'{'开始，位置: " + currentIndex);
            }

            // 查找匹配的结束括号
            int braceCount = 0;
            int startIndex = currentIndex;
            boolean inString = false;
            boolean escaped = false;

            while (currentIndex < data.length()) {
                char c = data.charAt(currentIndex);

                if (escaped) {
                    escaped = false;
                } else if (c == '\\') {
                    escaped = true;
                } else if (c == '"') {
                    inString = !inString;
                } else if (!inString) {
                    if (c == '{') {
                        braceCount++;
                    } else if (c == '}') {
                        braceCount--;
                        if (braceCount == 0) {
                            // 找到完整的JSON对象
                            String jsonObject = data.substring(startIndex, currentIndex + 1);
                            SourceQuestionData question = objectMapper.readValue(jsonObject, SourceQuestionData.class);
                            questionList.add(question);
                            currentIndex++;
                            break;
                        }
                    }
                }
                currentIndex++;
            }

            if (braceCount > 0) {
                throw new IllegalArgumentException("JSON对象格式错误：缺少结束符号'}'");
            }
        }

        return questionList;
    }
    


    /**
     * 预处理单个JSON对象
     */
    private String preprocessJsonObject(String jsonData) {
        // 查找JSON对象的结束位置
        int lastBraceIndex = jsonData.lastIndexOf('}');
        if (lastBraceIndex == -1) {
            throw new IllegalArgumentException("JSON对象格式错误：缺少结束符号 '}'");
        }

        // 检查对象结束后是否有额外内容
        String afterObject = jsonData.substring(lastBraceIndex + 1).trim();
        if (!afterObject.isEmpty()) {
            log.warn("检测到JSON对象后有额外内容，将自动清理: {}",
                afterObject.length() > 100 ? afterObject.substring(0, 100) + "..." : afterObject);
            // 只保留到对象结束的部分
            return jsonData.substring(0, lastBraceIndex + 1);
        }

        return jsonData;
    }
    
    /**
     * 验证必需字段
     */
    private String validateRequiredFields(SourceQuestionData item, int index) {
        if (item.getType() == null || item.getType().trim().isEmpty()) {
            return String.format("第%d个题目缺少必需字段: type", index);
        }
        
        if (item.getId() == null) {
            return String.format("第%d个题目缺少必需字段: id", index);
        }
        
        if (item.getSubject() == null || item.getSubject().trim().isEmpty()) {
            return String.format("第%d个题目缺少必需字段: subject", index);
        }
        
        if (item.getDifficulty() == null || item.getDifficulty().trim().isEmpty()) {
            return String.format("第%d个题目缺少必需字段: difficulty", index);
        }
        
        if (item.getContent() == null || item.getContent().trim().isEmpty()) {
            return String.format("第%d个题目缺少必需字段: content", index);
        }
        
        if (item.getBasicInfo() == null) {
            return String.format("第%d个题目缺少必需字段: basicInfo", index);
        }
        
        // 验证basicInfo的子字段
        if (item.getBasicInfo().getSubject() == null) {
            return String.format("第%d个题目的basicInfo缺少必需字段: subject", index);
        }
        
        if (item.getBasicInfo().getChapter() == null) {
            return String.format("第%d个题目的basicInfo缺少必需字段: chapter", index);
        }
        
        if (item.getBasicInfo().getKnowledgePoints() == null || item.getBasicInfo().getKnowledgePoints().isEmpty()) {
            return String.format("第%d个题目的basicInfo缺少必需字段: knowledgePoints", index);
        }
        
        return null; // 验证通过
    }
    
    /**
     * 构建JSON解析错误信息
     */
    private String buildJsonParseError(JsonParseException e) {
        StringBuilder error = new StringBuilder("JSON语法错误");
        
        if (e.getLocation() != null) {
            error.append("，位置: 第").append(e.getLocation().getLineNr())
                 .append("行，第").append(e.getLocation().getColumnNr()).append("列");
        }
        
        error.append("。错误详情: ").append(e.getOriginalMessage());
        
        return error.toString();
    }
    
    /**
     * 构建JSON映射错误信息
     */
    private String buildJsonMappingError(JsonMappingException e) {
        StringBuilder error = new StringBuilder("JSON数据映射错误");
        
        if (e.getPath() != null && !e.getPath().isEmpty()) {
            error.append("，字段路径: ");
            for (int i = 0; i < e.getPath().size(); i++) {
                if (i > 0) error.append(".");
                error.append(e.getPath().get(i).getFieldName());
            }
        }
        
        error.append("。错误详情: ").append(e.getOriginalMessage());
        
        return error.toString();
    }
    
    /**
     * JSON验证结果
     */
    public static class JsonValidationResult {
        private final boolean valid;
        private final String message;
        private final Integer dataCount;
        
        private JsonValidationResult(boolean valid, String message, Integer dataCount) {
            this.valid = valid;
            this.message = message;
            this.dataCount = dataCount;
        }
        
        public static JsonValidationResult success(int dataCount, String message) {
            return new JsonValidationResult(true, message, dataCount);
        }
        
        public static JsonValidationResult error(String message) {
            return new JsonValidationResult(false, message, null);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
        
        public Integer getDataCount() {
            return dataCount;
        }
    }
}
