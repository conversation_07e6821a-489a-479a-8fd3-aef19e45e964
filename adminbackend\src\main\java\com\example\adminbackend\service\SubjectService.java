package com.example.adminbackend.service;

import com.example.adminbackend.model.Subject;
import com.example.adminbackend.dto.SubjectDTO;

import java.util.List;

/**
 * 科目服务接口
 */
public interface SubjectService {

    /**
     * 获取所有科目
     */
    List<Subject> getAllSubjects();

    /**
     * 根据ID获取科目
     */
    Subject getSubjectById(Long id);

    /**
     * 根据名称获取科目
     */
    Subject getSubjectByName(String name);

    /**
     * 创建科目
     */
    Subject createSubject(Subject subject);

    /**
     * 更新科目
     */
    Subject updateSubject(Long id, Subject subject);

    /**
     * 删除科目（简单删除，不处理关联版本）
     */
    void deleteSubject(Long id);

    /**
     * 级联删除科目及其所有版本、章节、知识点
     * @param id 科目ID
     * @return 删除的文件数量
     */
    int deleteSubjectCascade(Long id);

    /**
     * 检查科目名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 根据关键词搜索科目
     */
    List<Subject> searchSubjects(String keyword);

    /**
     * 获取有版本的科目列表，返回DTO
     */
    List<SubjectDTO> getSubjectsWithVersions();

    /**
     * 获取所有科目，返回DTO（避免懒加载问题）
     */
    List<SubjectDTO> getAllSubjectsAsDTO();

    /**
     * 根据ID获取科目，返回DTO（避免懒加载问题）
     */
    SubjectDTO getSubjectByIdAsDTO(Long id);
}
