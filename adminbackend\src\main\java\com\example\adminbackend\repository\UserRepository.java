package com.example.adminbackend.repository;

import com.example.adminbackend.model.Role;
import com.example.adminbackend.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByPhone(String phone);
    
    boolean existsByUsername(String username);
    
    boolean existsByPhone(String phone);
    
    List<User> findByRole(Role role);
    
    Page<User> findByRole(Role role, Pageable pageable);
    
    // 查找创建者为指定用户的所有用户
    List<User> findByCreator(String creator);
    
    // 查找创建者为指定用户且角色为指定角色的所有用户
    List<User> findByCreatorAndRole(String creator, Role role);
    
    // 分页查找创建者为指定用户且角色为指定角色的所有用户
    Page<User> findByCreatorAndRole(String creator, Role role, Pageable pageable);
    
    // 根据电话号码和角色查找用户
    List<User> findByPhoneAndRole(String phone, Role role);
} 