import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Psychology,
  Refresh,
  Search,
} from '@mui/icons-material';
import { PracticeConfigAPI, PracticeConfig } from '../../services/practiceConfigApi';
import CurriculumDataService, { SimpleSubject, SimpleChapter, SimpleKnowledgePoint } from '../../services/curriculumDataService';

interface KnowledgePointConfigTabProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

// 使用CurriculumDataService中的类型
type Subject = SimpleSubject;
type Chapter = SimpleChapter;
type KnowledgePoint = SimpleKnowledgePoint;

interface ConfigForm {
  knowledgePointId: number;
  configType: 'KNOWLEDGE_POINT_PRACTICE' | 'CHAPTER_TEST';
  questionCount: number;
  selectionStrategy: 'RANDOM' | 'DIFFICULTY_BALANCED' | 'ERROR_PRIORITY' | 'TYPE_BALANCED';
}

const KnowledgePointConfigTab: React.FC<KnowledgePointConfigTabProps> = ({ onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [knowledgePoints, setKnowledgePoints] = useState<KnowledgePoint[]>([]);
  const [configs, setConfigs] = useState<PracticeConfig[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingConfig, setEditingConfig] = useState<PracticeConfig | null>(null);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [selectedChapter, setSelectedChapter] = useState<Chapter | null>(null);
  const [searchText, setSearchText] = useState('');
  const [formData, setFormData] = useState<ConfigForm>({
    knowledgePointId: 0,
    configType: 'KNOWLEDGE_POINT_PRACTICE',
    questionCount: 10,
    selectionStrategy: 'RANDOM',
  });

  // 策略选项
  const strategyOptions = [
    { value: 'RANDOM', label: '随机选择' },
    { value: 'DIFFICULTY_BALANCED', label: '难度均衡' },
    { value: 'ERROR_PRIORITY', label: '错题优先' },
    { value: 'TYPE_BALANCED', label: '题型均衡' },
  ];

  // 配置类型选项
  const configTypeOptions = [
    { value: 'KNOWLEDGE_POINT_PRACTICE', label: '知识点练习' },
    { value: 'CHAPTER_TEST', label: '章节测试' },
  ];

  // 加载基础数据
  const loadBaseData = async () => {
    try {
      // 加载科目
      const subjects = await CurriculumDataService.getSubjects();
      setSubjects(subjects);

      // 加载章节
      const chapters = await CurriculumDataService.getChapters();
      setChapters(chapters);

      // 加载知识点
      const knowledgePoints = await CurriculumDataService.getKnowledgePoints();
      setKnowledgePoints(knowledgePoints);
    } catch (error: any) {
      console.error('加载基础数据失败:', error);
      onError('加载基础数据失败: ' + (error.message || '未知错误'));
    }
  };

  // 加载知识点配置
  const loadKnowledgePointConfigs = async () => {
    try {
      setLoading(true);
      const allConfigs = await PracticeConfigAPI.getAllEnabledConfigs();
      const knowledgePointConfigs = allConfigs.filter(config => config.scopeType === 'KNOWLEDGE_POINT');
      setConfigs(knowledgePointConfigs);
    } catch (error: any) {
      console.error('加载知识点配置失败:', error);
      onError('加载知识点配置失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取知识点信息
  const getKnowledgePointInfo = (knowledgePointId: number) => {
    const kp = knowledgePoints.find(k => k.id === knowledgePointId);
    return kp || { 
      id: knowledgePointId, 
      name: `知识点${knowledgePointId}`, 
      chapterName: '未知章节',
      subjectName: '未知科目'
    };
  };

  // 获取策略标签
  const getStrategyLabel = (strategy: string) => {
    const option = strategyOptions.find(opt => opt.value === strategy);
    return option?.label || strategy;
  };

  // 获取配置类型标签
  const getConfigTypeLabel = (configType: string) => {
    const option = configTypeOptions.find(opt => opt.value === configType);
    return option?.label || configType;
  };

  // 获取过滤后的章节列表
  const getFilteredChapters = () => {
    if (!selectedSubject) return chapters;
    return chapters.filter(chapter => chapter.subjectId === selectedSubject.id);
  };

  // 获取过滤后的知识点列表
  const getFilteredKnowledgePoints = () => {
    let filtered = knowledgePoints;
    
    if (selectedChapter) {
      filtered = filtered.filter(kp => kp.chapterId === selectedChapter.id);
    } else if (selectedSubject) {
      const subjectChapterIds = chapters
        .filter(c => c.subjectId === selectedSubject.id)
        .map(c => c.id);
      filtered = filtered.filter(kp => subjectChapterIds.includes(kp.chapterId));
    }

    if (searchText) {
      filtered = filtered.filter(kp => 
        kp.name.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    return filtered;
  };

  // 打开新增对话框
  const handleAdd = () => {
    setEditingConfig(null);
    setSelectedSubject(null);
    setSelectedChapter(null);
    setFormData({
      knowledgePointId: 0,
      configType: 'KNOWLEDGE_POINT_PRACTICE',
      questionCount: 10,
      selectionStrategy: 'RANDOM',
    });
    setOpenDialog(true);
  };

  // 打开编辑对话框
  const handleEdit = (config: PracticeConfig) => {
    setEditingConfig(config);
    const kpInfo = getKnowledgePointInfo(config.targetId!);
    const chapter = chapters.find(c => c.name === kpInfo.chapterName);
    const subject = subjects.find(s => s.name === kpInfo.subjectName);
    
    setSelectedSubject(subject || null);
    setSelectedChapter(chapter || null);
    setFormData({
      knowledgePointId: config.targetId || 0,
      configType: config.configType,
      questionCount: config.questionCount,
      selectionStrategy: config.selectionStrategy,
    });
    setOpenDialog(true);
  };

  // 删除配置
  const handleDelete = async (config: PracticeConfig) => {
    if (!window.confirm('确定要删除这个配置吗？')) {
      return;
    }

    try {
      await PracticeConfigAPI.deleteConfig(config.id!);
      onSuccess('配置删除成功');
      await loadKnowledgePointConfigs();
    } catch (error: any) {
      console.error('删除配置失败:', error);
      onError('删除配置失败: ' + (error.message || '未知错误'));
    }
  };

  // 保存配置
  const handleSave = async () => {
    if (formData.knowledgePointId === 0) {
      onError('请选择知识点');
      return;
    }

    try {
      setSaving(true);
      
      if (editingConfig) {
        // 更新配置
        await PracticeConfigAPI.updateConfig(editingConfig.id!, {
          questionCount: formData.questionCount,
          selectionStrategy: formData.selectionStrategy,
        });
        onSuccess('配置更新成功');
      } else {
        // 创建新配置
        await PracticeConfigAPI.createOrUpdateKnowledgePointConfig(
          formData.knowledgePointId,
          formData.configType,
          formData.questionCount,
          formData.selectionStrategy
        );
        onSuccess('配置创建成功');
      }

      setOpenDialog(false);
      await loadKnowledgePointConfigs();
    } catch (error: any) {
      console.error('保存配置失败:', error);
      onError('保存配置失败: ' + (error.message || '未知错误'));
    } finally {
      setSaving(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof ConfigForm, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  useEffect(() => {
    loadBaseData();
    loadKnowledgePointConfigs();
  }, []);

  return (
    <Box>
      {/* 页面说明 */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          知识点配置具有最高优先级，会覆盖章节配置、科目配置和全局配置。
          每个知识点可以设置独特的题目数量和抽题策略。
        </Typography>
      </Alert>

      {/* 操作栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Psychology sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" fontWeight="bold">
            知识点配置管理
          </Typography>
        </Box>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={loadKnowledgePointConfigs}
            disabled={loading}
            sx={{ mr: 1 }}
          >
            刷新
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAdd}
          >
            新增配置
          </Button>
        </Box>
      </Box>

      {/* 配置列表 */}
      <Card>
        <CardContent sx={{ p: 0 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>知识点</TableCell>
                    <TableCell>章节</TableCell>
                    <TableCell>科目</TableCell>
                    <TableCell>配置类型</TableCell>
                    <TableCell>题目数量</TableCell>
                    <TableCell>抽题策略</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>创建时间</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {configs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} align="center">
                        <Typography color="text.secondary">
                          暂无知识点配置
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    configs.map((config) => {
                      const kpInfo = getKnowledgePointInfo(config.targetId!);
                      return (
                        <TableRow key={config.id}>
                          <TableCell>
                            <Typography fontWeight="medium">
                              {kpInfo.name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {kpInfo.chapterName}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={kpInfo.subjectName}
                              color="secondary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getConfigTypeLabel(config.configType)}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography fontWeight="medium">
                              {config.questionCount}道
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {getStrategyLabel(config.selectionStrategy)}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={config.enabled ? '已启用' : '已禁用'}
                              color={config.enabled ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {config.createdAt ? new Date(config.createdAt).toLocaleDateString() : '-'}
                          </TableCell>
                          <TableCell align="center">
                            <IconButton
                              size="small"
                              onClick={() => handleEdit(config)}
                              color="primary"
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(config)}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* 配置对话框 */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingConfig ? '编辑知识点配置' : '新增知识点配置'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={12}>
              <Autocomplete
                options={subjects}
                getOptionLabel={(option) => option.name}
                value={selectedSubject}
                onChange={(event, newValue) => {
                  setSelectedSubject(newValue);
                  setSelectedChapter(null);
                  setFormData(prev => ({ ...prev, knowledgePointId: 0 }));
                }}
                disabled={!!editingConfig}
                renderInput={(params) => (
                  <TextField {...params} label="科目" fullWidth />
                )}
              />
            </Grid>
            <Grid size={12}>
              <Autocomplete
                options={getFilteredChapters()}
                getOptionLabel={(option) => option.name}
                value={selectedChapter}
                onChange={(event, newValue) => {
                  setSelectedChapter(newValue);
                  setFormData(prev => ({ ...prev, knowledgePointId: 0 }));
                }}
                disabled={!!editingConfig || !selectedSubject}
                renderInput={(params) => (
                  <TextField {...params} label="章节" fullWidth />
                )}
              />
            </Grid>
            <Grid size={12}>
              <Autocomplete
                options={getFilteredKnowledgePoints()}
                getOptionLabel={(option) => option.name}
                value={knowledgePoints.find(kp => kp.id === formData.knowledgePointId) || null}
                onChange={(event, newValue) => {
                  setFormData(prev => ({ ...prev, knowledgePointId: newValue?.id || 0 }));
                }}
                disabled={!!editingConfig}
                renderInput={(params) => (
                  <TextField {...params} label="知识点" fullWidth />
                )}
              />
            </Grid>
            <Grid size={12}>
              <FormControl fullWidth disabled={!!editingConfig}>
                <InputLabel>配置类型</InputLabel>
                <Select
                  value={formData.configType}
                  label="配置类型"
                  onChange={(e) => handleFieldChange('configType', e.target.value)}
                >
                  {configTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="题目数量"
                type="number"
                value={formData.questionCount}
                onChange={(e) => handleFieldChange('questionCount', parseInt(e.target.value) || 1)}
                inputProps={{ min: 1, max: 100 }}
                helperText="建议设置为5-30道题目"
              />
            </Grid>
            <Grid size={12}>
              <FormControl fullWidth>
                <InputLabel>抽题策略</InputLabel>
                <Select
                  value={formData.selectionStrategy}
                  label="抽题策略"
                  onChange={(e) => handleFieldChange('selectionStrategy', e.target.value)}
                >
                  {strategyOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? '保存中...' : '保存'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KnowledgePointConfigTab;
