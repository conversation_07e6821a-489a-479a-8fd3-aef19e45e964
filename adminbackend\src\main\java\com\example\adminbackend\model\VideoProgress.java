package com.example.adminbackend.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 视频观看进度实体类
 * 记录学生观看视频的进度
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "video_progress", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"student_id", "video_id"}, name = "uk_student_video")
})
public class VideoProgress {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    private Student student;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "video_id", nullable = false)
    private Video video;
    
    @Column(name = "progress_seconds", nullable = false)
    private Integer progressSeconds;
    
    @Column(name = "is_completed", nullable = false)
    private Boolean isCompleted;
    
    @Column(name = "last_position")
    private Integer lastPosition;
    
    @Column(name = "last_watched_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastWatchedAt;
    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
        lastWatchedAt = new Date();
        if (progressSeconds == null) {
            progressSeconds = 0;
        }
        if (isCompleted == null) {
            isCompleted = false;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
        lastWatchedAt = new Date();
    }
} 