package com.example.adminbackend.model;

import com.example.adminbackend.service.QuestionService;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 题目实体类
 * 对应questions表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "questions")
@Slf4j
public class Question {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "question_type", nullable = false)
    private QuestionType questionType;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "body", nullable = false, columnDefinition = "JSON")
    private Map<String, Object> body;  // 题目和答案的完整JSON数据，直接映射为Map对象

    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "knowledge_point_id", nullable = false)
    private Long knowledgePointId;

    @Builder.Default
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }
    
    /**
     * 获取题目标题
     * 从body字段中解析出题目标题，支持新旧两种数据格式
     *
     * @return 题目标题
     */
    public String getTitle() {
        if (body == null) {
            return "";
        }

        try {
            // 新格式：尝试从content字段获取HTML内容
            Object contentObj = body.get("content");
            if (contentObj != null) {
                // 如果content是HTML字符串（新格式）
                if (contentObj instanceof String) {
                    String htmlContent = (String) contentObj;
                    // 简单提取HTML中的文本内容作为标题
                    return extractTextFromHtml(htmlContent);
                }
                // 如果content是ContentBlock数组（旧格式）
                else if (contentObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> contentList = (List<Object>) contentObj;
                    if (!contentList.isEmpty() && contentList.get(0) instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> firstContent = (Map<String, Object>) contentList.get(0);
                        Object value = firstContent.get("value");
                        if (value instanceof String) {
                            return (String) value;
                        }
                    }
                }
            }

            // 兼容旧格式：尝试从stem字段获取题目标题
            Object stemObj = body.get("stem");
            if (stemObj != null) {
                // 如果stem是数组，尝试获取第一个元素的content
                if (stemObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> stemList = (List<Object>) stemObj;
                    if (!stemList.isEmpty() && stemList.get(0) instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> firstStem = (Map<String, Object>) stemList.get(0);
                        Object content = firstStem.get("content");
                        if (content instanceof String) {
                            return (String) content;
                        }
                    }
                }
                // 如果stem是字符串，直接返回
                else if (stemObj instanceof String) {
                    return (String) stemObj;
                }
            }

            // 如果没有找到标题，返回空字符串
            return "";
        } catch (Exception e) {
            log.error("解析题目标题失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 从HTML内容中提取纯文本
     *
     * @param htmlContent HTML内容
     * @return 提取的文本内容
     */
    private String extractTextFromHtml(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return "";
        }

        // 简单的HTML标签移除（生产环境建议使用专业的HTML解析库）
        String text = htmlContent.replaceAll("<[^>]+>", "");
        // 移除多余的空白字符
        text = text.replaceAll("\\s+", " ").trim();

        // 限制标题长度
        if (text.length() > 100) {
            text = text.substring(0, 100) + "...";
        }

        return text;
    }
    

    
    /**
     * 获取正确答案（待重构）
     * 临时方法，等待重构完成后重新实现
     *
     * @param questionService QuestionService实例
     * @return 正确答案的字符串表示
     */
    public String getAnswer(QuestionService questionService) {
        // 临时实现，待重构
        return "待重构";
    }

    /**
     * 判断是否为简单题型
     *
     * @return 是否为简单题型
     */
    public boolean isSimpleQuestion() {
        return questionType == QuestionType.SINGLE_CHOICE ||
               questionType == QuestionType.MULTIPLE_CHOICE ||
               questionType == QuestionType.FILL_IN_BLANK ||
               questionType == QuestionType.TRUE_FALSE;
    }

    /**
     * 判断是否为嵌套题型
     *
     * @return 是否为嵌套题型
     */
    public boolean isNestedQuestion() {
        return questionType == QuestionType.READING_COMPREHENSION ||
               questionType == QuestionType.LISTENING ||
               questionType == QuestionType.CLOZE_TEST;
    }

    /**
     * 判断是否使用HTML格式
     * 通过检查body中的content字段类型来判断
     *
     * @return 是否使用HTML格式
     */
    public boolean isHtmlFormat() {
        if (body == null) {
            return false;
        }

        try {
            Object contentObj = body.get("content");
            if (contentObj != null) {
                // 如果content是字符串，则为HTML格式
                return contentObj instanceof String;
            }
            return false;
        } catch (Exception e) {
            log.debug("检查HTML格式时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取题目科目
     * 用于JSON序列化，会自动包含在API响应中
     *
     * @return 题目科目
     */
    @JsonProperty("subject")
    public String getSubject() {
        if (body == null) {
            return null;
        }

        try {
            Object subjectObj = body.get("subject");
            if (subjectObj instanceof String) {
                return (String) subjectObj;
            }
            return null;
        } catch (Exception e) {
            log.debug("解析题目科目失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取题目难度
     * 用于JSON序列化，会自动包含在API响应中
     *
     * @return 题目难度
     */
    @JsonProperty("difficulty")
    public String getDifficulty() {
        if (body == null) {
            return null;
        }

        try {
            Object difficultyObj = body.get("difficulty");
            if (difficultyObj instanceof String) {
                return (String) difficultyObj;
            }
            return null;
        } catch (Exception e) {
            log.debug("解析题目难度失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 题目类型枚举
     */
    public enum QuestionType {
        SINGLE_CHOICE,           // 单选题
        MULTIPLE_CHOICE,         // 多选题
        FILL_IN_BLANK,          // 填空题
        TRUE_FALSE,             // 判断题
        MATCHING,               // 匹配题
        READING_COMPREHENSION,   // 阅读理解
        CLOZE_TEST,             // 完形填空
        LISTENING               // 听力题
    }

    /**
     * 科目枚举
     */
    public enum Subject {
        ENGLISH,    // 英语
        MATH,       // 数学
        PHYSICS,    // 物理
        CHEMISTRY   // 化学
    }

    /**
     * 难度枚举
     */
    public enum Difficulty {
        EASY,       // 简单
        MEDIUM,     // 中等
        HARD        // 困难
    }
}