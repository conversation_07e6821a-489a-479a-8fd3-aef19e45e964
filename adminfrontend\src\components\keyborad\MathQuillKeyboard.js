/**
 * MathQuill自定义键盘组件
 * 实现双面板布局设计：左侧数学符号面板(6x4)，右侧数字运算面板(5x4)
 * 
 * 设计特点：
 * 1. 双面板独立布局：45:55比例分配
 * 2. 扁平化设计风格
 * 3. 双色按键系统：白色数字键，灰色功能键
 * 4. 响应式适配
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import './MathQuillKeyboard.css';

// 完整的数学符号配置 - 按类别组织，支持滑动显示
// 符合MathLive API标准的符号定义
const MATH_SYMBOLS_CATEGORIES = {
  // 基础符号
  basic: [
    { type: 'variable', insert: 'a', label: 'a', class: 'key-gray', tooltip: '变量 a' },
    { type: 'variable', insert: 'x', label: 'x', class: 'key-gray', tooltip: '变量 x' },
    { type: 'variable', insert: 'y', label: 'y', class: 'key-gray', tooltip: '变量 y' },
    { type: 'variable', insert: 'z', label: 'z', class: 'key-gray', tooltip: '变量 z' },
    { type: 'comparison', insert: '=', label: '=', class: 'key-gray', tooltip: '等于' },
    { type: 'comparison', insert: '\\neq', label: '≠', class: 'key-gray', tooltip: '不等于' },
    { type: 'special', insert: '\\pm', label: '±', class: 'key-gray', tooltip: '正负号' },
    { type: 'special', insert: '\\cdot', label: '·', class: 'key-gray', tooltip: '点乘' },
    { type: 'special', insert: '\\cdots', label: '⋯', class: 'key-gray', tooltip: '省略号' },
    { type: 'special', insert: '%', label: '%', class: 'key-gray', tooltip: '百分号' }
  ],

  // 幂次和根号
  powers: [
    { type: 'power', insert: '#?^2', label: 'x²', class: 'key-gray', tooltip: '平方' }, // 🔧 底数是占位符
    { type: 'power', insert: '#?^{#?}', label: '<span class="math-display-container"><span class="math-base">▢</span><sup class="math-sup">▢</sup></span>', class: 'key-gray math-display', tooltip: '上标' },
    { type: 'power', insert: '#?_{#?}', label: '<span class="math-display-container"><span class="math-base">▢</span><sub class="math-sub">▢</sub></span>', class: 'key-gray math-display', tooltip: '下标' },
    { type: 'power', insert: '#?^{#?}_{#?}', label: '<span class="math-display-container"><span class="math-base">▢</span><sup class="math-sup">▢</sup><sub class="math-sub">▢</sub></span>', class: 'key-gray math-display', tooltip: '上下标' },
    { type: 'function', insert: '\\sqrt{#?}', label: '√▢', class: 'key-gray', tooltip: '平方根' },
    { type: 'function', insert: '\\sqrt[#?]{#?}', label: 'ⁿ√▢', class: 'key-gray', tooltip: 'n次根' },
    { type: 'special', insert: '\\dot{#?}', label: 'ẋ', class: 'key-gray', tooltip: '点导数' },
    { type: 'special', insert: '#?^\\circ', label: '°', class: 'key-gray', tooltip: '度数' } // 🔧 底数是占位符
  ],

  // 比较符号
  comparison: [
    { type: 'comparison', insert: '>', label: '>', class: 'key-gray', tooltip: '大于' },
    { type: 'comparison', insert: '\\geq', label: '≥', class: 'key-gray', tooltip: '大于等于' },
    { type: 'comparison', insert: '<', label: '<', class: 'key-gray', tooltip: '小于' },
    { type: 'comparison', insert: '\\leq', label: '≤', class: 'key-gray', tooltip: '小于等于' }
  ],

  // 括号
  brackets: [
    { type: 'bracket', latex: '(', label: '(', className: 'key-gray' },
    { type: 'bracket', latex: ')', label: ')', className: 'key-gray' },
    { type: 'bracket', latex: '[', label: '[', className: 'key-gray' },
    { type: 'bracket', latex: ']', label: ']', className: 'key-gray' },
    { type: 'bracket', latex: '\\{', label: '{', className: 'key-gray' },
    { type: 'bracket', latex: '\\}', label: '}', className: 'key-gray' },
    { type: 'bracket', latex: '|', label: '|', className: 'key-gray' },
    { type: 'bracket', latex: '\\left(\\right)', label: '()', template: '\\left(\\right)', className: 'key-gray' }
  ],

  // 集合和逻辑
  sets: [
    { type: 'set', latex: '\\cap', label: '∩', className: 'key-gray' },
    { type: 'set', latex: '\\cup', label: '∪', className: 'key-gray' },
    { type: 'set', latex: '\\in', label: '∈', className: 'key-gray' },
    { type: 'set', latex: '\\notin', label: '∉', className: 'key-gray' },
    { type: 'set', latex: '\\emptyset', label: 'Ø', className: 'key-gray' },
    { type: 'logic', latex: '\\wedge', label: '∧', className: 'key-gray' },
    { type: 'logic', latex: '\\vee', label: '∨', className: 'key-gray' },
    { type: 'logic', latex: '\\neg', label: '¬', className: 'key-gray' },
    { type: 'logic', latex: '\\forall', label: '∀', className: 'key-gray' },
    { type: 'logic', latex: '\\exists', label: '∃', className: 'key-gray' }
  ],

  // 几何
  geometry: [
    { type: 'geometry', latex: '\\angle', label: '∠', className: 'key-gray' },
    { type: 'special', latex: '\\pi', label: 'π', className: 'key-gray' },
    { type: 'geometry', latex: '//', label: '//', className: 'key-gray' }, // 简化为普通字符
    { type: 'geometry', latex: '\\perp', label: '⊥', className: 'key-gray' },
    { type: 'geometry', latex: '\\vec{#?}', label: '→', template: '\\vec{#?}', className: 'key-gray' } // 🔧 向量内容是占位符
  ],

  // 函数
  functions: [
    { type: 'function', latex: '\\frac{#?}{#?}', label: '▢/▢', template: '\\frac{#?}{#?}', className: 'key-gray' },
    { type: 'function', latex: '\\sin', label: 'sin', className: 'key-gray' },
    { type: 'function', latex: '\\cos', label: 'cos', className: 'key-gray' },
    { type: 'function', latex: '\\tan', label: 'tan', className: 'key-gray' },
    { type: 'function', latex: '\\cot', label: 'cot', className: 'key-gray' },
    { type: 'function', latex: '\\log', label: 'log', className: 'key-gray' },
    { type: 'function', latex: '\\ln', label: 'ln', className: 'key-gray' },
    { type: 'function', latex: '\\log', label: 'lg', className: 'key-gray' }, // 简化lg为log
    { type: 'special', latex: '\\infty', label: '∞', className: 'key-gray' }
  ],

  // 希腊字母
  greek: [
    { type: 'greek', latex: '\\alpha', label: 'α', className: 'key-gray' },
    { type: 'greek', latex: '\\beta', label: 'β', className: 'key-gray' },
    { type: 'greek', latex: '\\gamma', label: 'γ', className: 'key-gray' }
  ],

  // 标点和序号
  punctuation: [
    { type: 'punctuation', latex: ',', label: ',', className: 'key-gray' },
    { type: 'punctuation', latex: ';', label: ';', className: 'key-gray' },
    { type: 'punctuation', latex: ':', label: ':', className: 'key-gray' },
    { type: 'text', latex: '\\text{①}', label: '①', className: 'key-gray' },
    { type: 'text', latex: '\\text{②}', label: '②', className: 'key-gray' },
    { type: 'text', latex: '\\text{③}', label: '③', className: 'key-gray' },
    { type: 'text', latex: '\\text{④}', label: '④', className: 'key-gray' },
    { type: 'text', latex: '\\text{⑤}', label: '⑤', className: 'key-gray' },
    { type: 'text', latex: '\\text{⑥}', label: '⑥', className: 'key-gray' },
    { type: 'text', latex: '\\text{⑦}', label: '⑦', className: 'key-gray' },
    { type: 'text', latex: '\\text{⑧}', label: '⑧', className: 'key-gray' },
    { type: 'text', latex: '\\text{⑨}', label: '⑨', className: 'key-gray' }
  ]
};

// 将所有符号合并为一个数组，用于滑动显示
const ALL_MATH_SYMBOLS = Object.values(MATH_SYMBOLS_CATEGORIES).flat();

// 英语键盘布局配置 - 标准QWERTY布局
const ENGLISH_KEYBOARD_LAYOUT = [
  // 第1行: qwertyuiop
  [
    { type: 'letter', insert: 'q', label: 'q', class: 'key-white', tooltip: '字母q' },
    { type: 'letter', insert: 'w', label: 'w', class: 'key-white', tooltip: '字母w' },
    { type: 'letter', insert: 'e', label: 'e', class: 'key-white', tooltip: '字母e' },
    { type: 'letter', insert: 'r', label: 'r', class: 'key-white', tooltip: '字母r' },
    { type: 'letter', insert: 't', label: 't', class: 'key-white', tooltip: '字母t' },
    { type: 'letter', insert: 'y', label: 'y', class: 'key-white', tooltip: '字母y' },
    { type: 'letter', insert: 'u', label: 'u', class: 'key-white', tooltip: '字母u' },
    { type: 'letter', insert: 'i', label: 'i', class: 'key-white', tooltip: '字母i' },
    { type: 'letter', insert: 'o', label: 'o', class: 'key-white', tooltip: '字母o' },
    { type: 'letter', insert: 'p', label: 'p', class: 'key-white', tooltip: '字母p' }
  ],
  // 第2行: asdfghjkl
  [
    { type: 'letter', insert: 'a', label: 'a', class: 'key-white', tooltip: '字母a' },
    { type: 'letter', insert: 's', label: 's', class: 'key-white', tooltip: '字母s' },
    { type: 'letter', insert: 'd', label: 'd', class: 'key-white', tooltip: '字母d' },
    { type: 'letter', insert: 'f', label: 'f', class: 'key-white', tooltip: '字母f' },
    { type: 'letter', insert: 'g', label: 'g', class: 'key-white', tooltip: '字母g' },
    { type: 'letter', insert: 'h', label: 'h', class: 'key-white', tooltip: '字母h' },
    { type: 'letter', insert: 'j', label: 'j', class: 'key-white', tooltip: '字母j' },
    { type: 'letter', insert: 'k', label: 'k', class: 'key-white', tooltip: '字母k' },
    { type: 'letter', insert: 'l', label: 'l', class: 'key-white', tooltip: '字母l' }
  ],
  // 第3行: shift + zxcvbnm + backspace
  [
    { type: 'shift', command: ['toggleShift'], label: '⇧', class: 'key-gray key-wide', tooltip: '大小写切换' },
    { type: 'letter', insert: 'z', label: 'z', class: 'key-white', tooltip: '字母z' },
    { type: 'letter', insert: 'x', label: 'x', class: 'key-white', tooltip: '字母x' },
    { type: 'letter', insert: 'c', label: 'c', class: 'key-white', tooltip: '字母c' },
    { type: 'letter', insert: 'v', label: 'v', class: 'key-white', tooltip: '字母v' },
    { type: 'letter', insert: 'b', label: 'b', class: 'key-white', tooltip: '字母b' },
    { type: 'letter', insert: 'n', label: 'n', class: 'key-white', tooltip: '字母n' },
    { type: 'letter', insert: 'm', label: 'm', class: 'key-white', tooltip: '字母m' },
    { type: 'navigation', command: ['deleteBackward'], label: '⌫', class: 'key-gray key-wide', tooltip: '删除' }
  ],
  // 第4行: 符号/123 + 空格 + =
  [
    { type: 'mode', command: ['toggleToMath'], label: '符号/123', class: 'key-gray key-wide', tooltip: '切换到数学键盘' },
    { type: 'letter', insert: ' ', label: '空格', class: 'key-white key-space', tooltip: '空格' },
    { type: 'letter', insert: '=', label: '=', class: 'key-gray', tooltip: '等号' }
  ]
];

// 右侧数字运算面板配置 - 固定5行布局
const NUMERIC_LAYOUT = [
  // 第1行: 导航
  [
    { type: 'navigation', command: ['moveLeft'], label: '←', class: 'key-gray', tooltip: '向左移动' },
    { type: 'navigation', command: ['moveRight'], label: '→', class: 'key-gray', tooltip: '向右移动' },
    { type: 'navigation', command: ['moveUp'], label: '↑', class: 'key-gray', tooltip: '向上移动' },
    { type: 'navigation', command: ['deleteBackward'], label: '⌫', class: 'key-gray', tooltip: '删除' }
  ],
  // 第2行: 789÷
  [
    { type: 'number', insert: '7', label: '7', class: 'key-white', tooltip: '数字7' },
    { type: 'number', insert: '8', label: '8', class: 'key-white', tooltip: '数字8' },
    { type: 'number', insert: '9', label: '9', class: 'key-white', tooltip: '数字9' },
    { type: 'operator', insert: '\\div', label: '÷', class: 'key-gray', tooltip: '除法' }
  ],
  // 第3行: 456×
  [
    { type: 'number', insert: '4', label: '4', class: 'key-white', tooltip: '数字4' },
    { type: 'number', insert: '5', label: '5', class: 'key-white', tooltip: '数字5' },
    { type: 'number', insert: '6', label: '6', class: 'key-white', tooltip: '数字6' },
    { type: 'operator', insert: '\\times', label: '×', class: 'key-gray', tooltip: '乘法' }
  ],
  // 第4行: 123-
  [
    { type: 'number', insert: '1', label: '1', class: 'key-white', tooltip: '数字1' },
    { type: 'number', insert: '2', label: '2', class: 'key-white', tooltip: '数字2' },
    { type: 'number', insert: '3', label: '3', class: 'key-white', tooltip: '数字3' },
    { type: 'operator', insert: '-', label: '-', class: 'key-gray', tooltip: '减法' }
  ],
  // 第5行: abc0.+
  [
    { type: 'mode', command: ['toggleToEnglish'], label: 'abc', class: 'key-gray', tooltip: '切换到英语键盘' },
    { type: 'number', insert: '0', label: '0', class: 'key-white', tooltip: '数字0' },
    { type: 'number', insert: '.', label: '.', class: 'key-white', tooltip: '小数点' },
    { type: 'operator', insert: '+', label: '+', class: 'key-gray', tooltip: '加法' }
  ]
];

// 单个按键组件
const KeyButton = ({ keyData, onKeyPress, className = '' }) => {
  const handleClick = () => {
    onKeyPress(keyData);
  };

  const buttonClass = `
    keyboard-key
    ${keyData.class || keyData.className || 'key-gray'}
    ${className}
  `.trim();

  return (
    <button
      type="button"
      className={buttonClass}
      onClick={handleClick}
      data-key-type={keyData.type}
      data-latex={keyData.latex}
      data-command={keyData.command}
    >
      <span
        className="key-label"
        dangerouslySetInnerHTML={{ __html: keyData.label }}
      />
    </button>
  );
};

// 左侧数学符号面板组件 - 支持上下滑动
const MathPanel = ({ onKeyPress }) => {
  const scrollContainerRef = useRef(null);

  // 将所有符号按行排列，每行4个
  const getAllSymbolRows = () => {
    const rows = [];
    for (let i = 0; i < ALL_MATH_SYMBOLS.length; i += 4) {
      const row = ALL_MATH_SYMBOLS.slice(i, i + 4);
      // 如果最后一行不足4个，用空占位符填充
      while (row.length < 4) {
        row.push(null);
      }
      rows.push(row);
    }
    return rows;
  };

  const allSymbolRows = getAllSymbolRows();

  return (
    <div className="math-panel">
      {/* 可滚动的符号容器 */}
      <div
        className="symbol-scroll-container"
        ref={scrollContainerRef}
      >
        <div className="panel-grid math-grid">
          {allSymbolRows.map((row, rowIndex) => (
            <div key={rowIndex} className="keyboard-row">
              {row.map((keyData, colIndex) => (
                keyData ? (
                  <KeyButton
                    key={`${rowIndex}-${colIndex}`}
                    keyData={keyData}
                    onKeyPress={onKeyPress}
                  />
                ) : (
                  <div key={`empty-${rowIndex}-${colIndex}`} className="keyboard-key-placeholder" />
                )
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// 右侧数字运算面板组件 - 固定布局
const NumericPanel = ({ onKeyPress }) => {
  return (
    <div className="numeric-panel">
      <div className="panel-grid numeric-grid">
        {NUMERIC_LAYOUT.map((row, rowIndex) => (
          <div key={rowIndex} className="keyboard-row">
            {row.map((keyData, colIndex) => (
              <KeyButton
                key={`${rowIndex}-${colIndex}`}
                keyData={keyData}
                onKeyPress={onKeyPress}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

// 英语键盘面板组件
const EnglishKeyboardPanel = ({ onKeyPress, isShiftPressed = false }) => {
  // 根据Shift状态调整字母大小写
  const getAdjustedLayout = () => {
    return ENGLISH_KEYBOARD_LAYOUT.map(row =>
      row.map(key => {
        if (key.type === 'letter' && key.insert !== ' ' && key.insert !== '=') {
          return {
            ...key,
            insert: isShiftPressed ? key.insert.toUpperCase() : key.insert.toLowerCase(),
            label: isShiftPressed ? key.label.toUpperCase() : key.label.toLowerCase()
          };
        }
        return key;
      })
    );
  };

  const adjustedLayout = getAdjustedLayout();

  return (
    <div className="english-keyboard-panel">
      <div className="panel-grid english-grid">
        {adjustedLayout.map((row, rowIndex) => (
          <div key={rowIndex} className="keyboard-row">
            {row.map((keyData, colIndex) => (
              <KeyButton
                key={`${rowIndex}-${colIndex}`}
                keyData={keyData}
                onKeyPress={onKeyPress}
                className={keyData.type === 'shift' && isShiftPressed ? 'shift-active' : ''}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

// 主键盘容器组件
const MathQuillKeyboard = React.memo(({
  visible = false,
  onKeyPress,
  onClose,
  targetMathField = null,
  className = '',
  autoHandle = false // 新增：是否自动处理MathField输入
}) => {
  const keyboardRef = useRef(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [keyboardMode, setKeyboardMode] = useState('math'); // 'math' 或 'english'
  const [isShiftPressed, setIsShiftPressed] = useState(false);

  // 处理按键点击
  const handleKeyPress = useCallback((keyData) => {
    // 处理键盘模式切换
    if (keyData.type === 'mode') {
      const commandName = Array.isArray(keyData.command) ? keyData.command[0] : keyData.command;
      switch (commandName) {
        case 'toggleToEnglish':
          setKeyboardMode('english');
          setIsShiftPressed(false); // 重置Shift状态
          return;
        case 'toggleToMath':
          setKeyboardMode('math');
          return;
        default:
          break;
      }
    }

    // 处理Shift切换
    if (keyData.type === 'shift') {
      setIsShiftPressed(!isShiftPressed);
      return;
    }

    // 总是调用外部传入的onKeyPress处理器
    if (onKeyPress) {
      onKeyPress(keyData);
    }

    // 只有在autoHandle为true且有targetMathField时才内部处理
    if (autoHandle && targetMathField) {
      handleMathFieldInput(targetMathField, keyData);
    }
  }, [onKeyPress, autoHandle, targetMathField, isShiftPressed]);

  // 处理MathField输入
  const handleMathFieldInput = (mathField, keyData) => {
    try {
      switch (keyData.type) {
        case 'number':
          // 数字直接输入
          mathField.typedText(keyData.latex);
          break;

        case 'operator':
          // 运算符处理
          if (keyData.latex === '\\times') {
            mathField.write('\\times');
          } else if (keyData.latex === '\\div') {
            mathField.write('\\div');
          } else {
            mathField.typedText(keyData.latex);
          }
          break;

        case 'variable':
          // 变量使用typedText，模拟用户输入
          mathField.typedText(keyData.latex);
          break;

        case 'letter':
          // 字母输入处理
          try {
            // 对于字母，使用executeCommand的insert方法更可靠
            mathField.executeCommand(['insert', keyData.insert]);
          } catch (error) {
            // 如果executeCommand失败，回退到typedText
            mathField.typedText(keyData.insert);
          }
          break;

        case 'comparison':
          // 比较符号使用write
          mathField.write(keyData.latex);
          break;

        case 'special':
          // 特殊符号使用write或cmd
          if (keyData.template) {
            mathField.write(keyData.template);
          } else if (keyData.latex.startsWith('\\')) {
            // LaTeX命令使用cmd
            mathField.cmd(keyData.latex);
          } else {
            mathField.write(keyData.latex);
          }
          break;

        case 'power':
          // 幂次处理
          if (keyData.template) {
            mathField.write(keyData.template);
          } else if (keyData.latex === '^2') {
            mathField.write('^2');
          } else {
            mathField.write(keyData.latex);
          }
          break;

        case 'function':
          // 函数处理 - 使用cmd方法
          if (keyData.template) {
            mathField.write(keyData.template);
          } else if (keyData.latex.startsWith('\\')) {
            // LaTeX函数命令使用cmd
            mathField.cmd(keyData.latex);
          } else {
            mathField.write(keyData.latex);
          }
          break;

        case 'bracket':
          // 括号处理
          if (keyData.template) {
            mathField.write(keyData.template);
          } else if (keyData.latex.startsWith('\\')) {
            // LaTeX括号命令使用cmd
            mathField.cmd(keyData.latex);
          } else {
            // 简单括号使用typedText
            mathField.typedText(keyData.latex);
          }
          break;

        case 'set':
          // 集合符号 - 使用cmd方法
          if (keyData.latex.startsWith('\\')) {
            mathField.cmd(keyData.latex);
          } else {
            mathField.write(keyData.latex);
          }
          break;

        case 'logic':
          // 逻辑符号 - 使用cmd方法
          if (keyData.latex.startsWith('\\')) {
            mathField.cmd(keyData.latex);
          } else {
            mathField.write(keyData.latex);
          }
          break;

        case 'geometry':
          // 几何符号
          if (keyData.template) {
            mathField.write(keyData.template);
          } else if (keyData.latex.startsWith('\\')) {
            mathField.cmd(keyData.latex);
          } else {
            mathField.write(keyData.latex);
          }
          break;

        case 'greek':
        case 'text':
        case 'punctuation':
          // 这些类型现在由GlobalKeyboardManager处理
          // 避免重复处理导致的焦点问题
          break;

        case 'navigation':
          // 导航命令
          switch (keyData.command) {
            case 'moveLeft':
              mathField.keystroke('Left');
              break;
            case 'moveRight':
              mathField.keystroke('Right');
              break;
            case 'moveUp':
              mathField.keystroke('Up');
              break;
            case 'deleteBackward':
              mathField.keystroke('Backspace');
              break;
          }
          break;

        case 'mode':
          // 模式切换处理
          switch (keyData.command) {
            case 'toggleMode':
              // 切换到文本输入模式
              // 这里可以实现文本模式的逻辑
              break;
            default:
              // 未知模式切换
          }
          break;

        default:
          // 未知按键类型
      }
    } catch (error) {
      // MathField输入错误，忽略
    }
  };

  // 处理键盘显示/隐藏动画
  useEffect(() => {
    if (visible) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 300);
      return () => clearTimeout(timer);
    }
  }, [visible]);

  // 处理点击覆盖层关闭
  const handleOverlayClick = (event) => {
    // 只有当点击的是覆盖层本身（不是键盘内容）时才关闭
    if (event.target.classList.contains('keyboard-overlay')) {
      if (onClose) {
        onClose();
      }
    }
  };

  // 移除渲染时的调试日志，避免循环输出
  if (!visible) {
    return null;
  }

  const keyboardClass = `
    mathquill-keyboard 
    ${isAnimating ? 'animating' : ''} 
    ${className}
  `.trim();

  return (
    <div className="keyboard-overlay" onClick={handleOverlayClick}>
      <div
        ref={keyboardRef}
        className={keyboardClass}
        onClick={(e) => e.stopPropagation()} // 阻止事件冒泡到覆盖层
      >
        <div className="keyboard-container">
          {keyboardMode === 'math' ? (
            <>
              <MathPanel onKeyPress={handleKeyPress} />
              <div className="panel-separator" />
              <NumericPanel onKeyPress={handleKeyPress} />
            </>
          ) : (
            <EnglishKeyboardPanel
              onKeyPress={handleKeyPress}
              isShiftPressed={isShiftPressed}
            />
          )}
        </div>
        
        {/* 关闭按钮 */}
        <button 
          type="button" 
          className="keyboard-close-btn"
          onClick={onClose}
          aria-label="关闭键盘"
        >
          ×
        </button>
      </div>
    </div>
  );
});

// PropTypes定义
KeyButton.propTypes = {
  keyData: PropTypes.object.isRequired,
  onKeyPress: PropTypes.func.isRequired,
  className: PropTypes.string
};

MathPanel.propTypes = {
  onKeyPress: PropTypes.func.isRequired
};

NumericPanel.propTypes = {
  onKeyPress: PropTypes.func.isRequired
};

EnglishKeyboardPanel.propTypes = {
  onKeyPress: PropTypes.func.isRequired,
  isShiftPressed: PropTypes.bool
};

MathQuillKeyboard.propTypes = {
  visible: PropTypes.bool,
  onKeyPress: PropTypes.func,
  onClose: PropTypes.func,
  targetMathField: PropTypes.object,
  className: PropTypes.string,
  autoHandle: PropTypes.bool
};

// 键盘管理器Hook
export const useMathQuillKeyboard = () => {
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [targetMathField, setTargetMathField] = useState(null);
  const [onKeyPress, setOnKeyPress] = useState(null);

  const showKeyboard = (mathField = null, keyPressHandler = null) => {
    setTargetMathField(mathField);
    setOnKeyPress(() => keyPressHandler);
    setKeyboardVisible(true);
  };

  const hideKeyboard = () => {
    setKeyboardVisible(false);
    setTargetMathField(null);
    setOnKeyPress(null);
  };

  const toggleKeyboard = (mathField = null, keyPressHandler = null) => {
    if (keyboardVisible) {
      hideKeyboard();
    } else {
      showKeyboard(mathField, keyPressHandler);
    }
  };

  return {
    keyboardVisible,
    targetMathField,
    onKeyPress,
    showKeyboard,
    hideKeyboard,
    toggleKeyboard
  };
};

// 全局键盘控制器实现
class GlobalKeyboardControllerClass {
  constructor() {
    this.state = {
      visible: false,
      targetField: null,
      onKeyPress: null,
      listeners: new Set()
    };
  }

  show = (targetField = null, onKeyPress = null) => {
    this.state.visible = true;
    this.state.targetField = targetField;
    this.state.onKeyPress = onKeyPress;
    this.notifyListeners();
  };

  hide = () => {
    this.state.visible = false;
    this.state.targetField = null;
    this.state.onKeyPress = null;
    this.notifyListeners();
  };

  toggle = (targetField = null, onKeyPress = null) => {
    if (this.state.visible) {
      this.hide();
    } else {
      this.show(targetField, onKeyPress);
    }
  };

  subscribe = (listener) => {
    this.state.listeners.add(listener);
    return () => {
      this.state.listeners.delete(listener);
    };
  };

  getState = () => ({ ...this.state });

  notifyListeners = () => {
    this.state.listeners.forEach(listener => listener(this.getState()));
  };
}

// 创建全局实例
export const GlobalKeyboardController = new GlobalKeyboardControllerClass();

export default MathQuillKeyboard;
export { MATH_SYMBOLS_CATEGORIES, ALL_MATH_SYMBOLS, NUMERIC_LAYOUT, ENGLISH_KEYBOARD_LAYOUT };
