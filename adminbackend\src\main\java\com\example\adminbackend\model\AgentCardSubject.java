package com.example.adminbackend.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agent_card_subjects", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"agent_card_id", "subject_id"}, name = "uk_card_subject")
})
public class AgentCardSubject {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_card_id", nullable = false)
    private AgentCard agentCard;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_id", nullable = false)
    private Subject subject;
    
    @OneToMany(mappedBy = "agentCardSubject", cascade = CascadeType.ALL)
    private List<AgentCardSubjectVersion> versions;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }
    
    /**
     * 获取第一个学科版本
     * @return 学科版本，如果没有则返回null
     */
    public SubjectVersion getSubjectVersion() {
        if (versions != null && !versions.isEmpty()) {
            return versions.get(0).getSubjectVersion();
        }
        return null;
    }
} 