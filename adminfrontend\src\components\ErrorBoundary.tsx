import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  AlertTitle,
  Collapse,
  IconButton
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  BugReport as BugReportIcon
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
}

/**
 * 全局错误边界组件
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告到监控系统
    this.reportError(error, errorInfo);
  }

  /**
   * 报告错误到监控系统
   */
  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成错误监控服务，如Sentry、LogRocket等
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous'
      };

      // 发送到错误监控服务
      // errorMonitoringService.report(errorReport);
    } catch (reportError) {
      // 静默处理报告错误
    }
  };

  /**
   * 重新加载页面
   */
  private handleReload = () => {
    window.location.reload();
  };

  /**
   * 重置错误状态
   */
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    });
  };

  /**
   * 切换错误详情显示
   */
  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  /**
   * 复制错误信息
   */
  private copyErrorInfo = () => {
    const { error, errorInfo } = this.state;
    if (!error || !errorInfo) return;

    const errorText = `
错误信息: ${error.message}
错误堆栈: ${error.stack}
组件堆栈: ${errorInfo.componentStack}
时间: ${new Date().toISOString()}
页面: ${window.location.href}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      alert('错误信息已复制到剪贴板');
    }).catch(() => {
      console.error('复制失败');
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, showDetails } = this.state;

      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          padding={3}
          bgcolor="background.default"
        >
          <Paper
            elevation={3}
            sx={{
              padding: 4,
              maxWidth: 600,
              width: '100%',
              textAlign: 'center'
            }}
          >
            <BugReportIcon
              sx={{
                fontSize: 64,
                color: 'error.main',
                marginBottom: 2
              }}
            />
            
            <Typography variant="h4" gutterBottom color="error">
              页面出现错误
            </Typography>
            
            <Typography variant="body1" color="text.secondary" paragraph>
              很抱歉，页面遇到了一个意外错误。我们已经记录了这个问题，请尝试刷新页面或联系技术支持。
            </Typography>

            <Alert severity="error" sx={{ marginBottom: 3, textAlign: 'left' }}>
              <AlertTitle>错误详情</AlertTitle>
              {error?.message || '未知错误'}
            </Alert>

            <Box display="flex" gap={2} justifyContent="center" marginBottom={2}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={this.handleReload}
              >
                刷新页面
              </Button>
              
              <Button
                variant="outlined"
                onClick={this.handleReset}
              >
                重试
              </Button>
            </Box>

            <Box>
              <Button
                variant="text"
                size="small"
                onClick={this.toggleDetails}
                endIcon={showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              >
                {showDetails ? '隐藏' : '显示'}技术详情
              </Button>
            </Box>

            <Collapse in={showDetails}>
              <Box
                sx={{
                  marginTop: 2,
                  padding: 2,
                  backgroundColor: 'grey.100',
                  borderRadius: 1,
                  textAlign: 'left'
                }}
              >
                <Typography variant="subtitle2" gutterBottom>
                  错误堆栈:
                </Typography>
                <Typography
                  variant="body2"
                  component="pre"
                  sx={{
                    fontSize: '0.75rem',
                    overflow: 'auto',
                    maxHeight: 200,
                    marginBottom: 2
                  }}
                >
                  {error?.stack}
                </Typography>

                {errorInfo && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      组件堆栈:
                    </Typography>
                    <Typography
                      variant="body2"
                      component="pre"
                      sx={{
                        fontSize: '0.75rem',
                        overflow: 'auto',
                        maxHeight: 200,
                        marginBottom: 2
                      }}
                    >
                      {errorInfo.componentStack}
                    </Typography>
                  </>
                )}

                <Button
                  variant="outlined"
                  size="small"
                  onClick={this.copyErrorInfo}
                >
                  复制错误信息
                </Button>
              </Box>
            </Collapse>
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
