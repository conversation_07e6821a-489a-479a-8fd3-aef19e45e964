package com.example.adminbackend.service.impl;

import com.example.adminbackend.dto.UserDTO;
import com.example.adminbackend.repository.UserRepository;
import com.example.adminbackend.service.UserService;
import com.example.adminbackend.model.Role;
import com.example.adminbackend.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class UserServiceImpl implements UserService, UserDetailsService {

    private final UserRepository userRepository;
    private PasswordEncoder passwordEncoder;

    @Autowired
    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Autowired
    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 首先尝试通过手机号查找用户（支持手机号登录）
        return userRepository.findByPhone(username)
                .or(() -> userRepository.findByUsername(username))
                .orElseThrow(() -> new UsernameNotFoundException("用户名或手机号不存在: " + username));
    }

    @Override
    public UserDetails loadUserByPhone(String phone) throws UsernameNotFoundException {
        return userRepository.findByPhone(phone)
                .orElseThrow(() -> new UsernameNotFoundException("手机号不存在: " + phone));
    }

    @Override
    @Transactional
    public User createUser(UserDTO userDTO) {
        // 确保username字段有值，如果没有则使用phone
        if (userDTO.getUsername() == null || userDTO.getUsername().isEmpty()) {
            userDTO.setUsername(userDTO.getPhone());
        }
        
        User user = User.builder()
                .username(userDTO.getUsername())
                .password(passwordEncoder.encode(userDTO.getPassword()))
                .fullName(userDTO.getFullName())
                .phone(userDTO.getPhone())
                .role(userDTO.getRole())
                .accountType(userDTO.getAccountType())
                .enabled(userDTO.isEnabled())
                .validFrom(userDTO.getValidFrom())
                .validTo(userDTO.getValidTo())
                .creator(userDTO.getCreator())
                .supervisorAccounts(userDTO.getSupervisorAccounts())
                .build();
        
        return userRepository.save(user);
    }

    @Override
    @Transactional
    public User updateUser(Long id, UserDTO userDTO) {
        User existingUser = getUserById(id);
        
        // 检查用户名唯一性
        if (userDTO.getUsername() != null && !userDTO.getUsername().isEmpty() && 
            !userDTO.getUsername().equals(existingUser.getUsername())) {
            // 如果用户名已更改，检查新用户名是否已被使用
            if (userRepository.existsByUsername(userDTO.getUsername())) {
                throw new RuntimeException("用户名已被使用: " + userDTO.getUsername());
            }
            existingUser.setUsername(userDTO.getUsername());
        }
        
        // 检查电话号码和角色的唯一性
        if (userDTO.getPhone() != null && userDTO.getRole() != null && 
            (!userDTO.getPhone().equals(existingUser.getPhone()) || userDTO.getRole() != existingUser.getRole())) {
            // 如果电话号码或角色已更改，检查新组合是否已被使用
            List<User> usersWithSamePhoneAndRole = userRepository.findByPhoneAndRole(userDTO.getPhone(), userDTO.getRole());
            if (!usersWithSamePhoneAndRole.isEmpty() && !usersWithSamePhoneAndRole.get(0).getId().equals(id)) {
                throw new RuntimeException("该角色下已存在相同手机号的用户");
            }
        }
        
        // 只有在提供了非空值时才更新对应的字段
        if (userDTO.getFullName() != null) {
            existingUser.setFullName(userDTO.getFullName());
        }
        
        if (userDTO.getPhone() != null) {
            existingUser.setPhone(userDTO.getPhone());
        }
        
        if (userDTO.getRole() != null) {
            existingUser.setRole(userDTO.getRole());
        }
        
        if (userDTO.getAccountType() != null) {
            existingUser.setAccountType(userDTO.getAccountType());
        }
        
        // enabled 是基本类型，不需要检查 null
        existingUser.setEnabled(userDTO.isEnabled());
        
        if (userDTO.getValidFrom() != null) {
            existingUser.setValidFrom(userDTO.getValidFrom());
        }
        
        if (userDTO.getValidTo() != null) {
            existingUser.setValidTo(userDTO.getValidTo());
        }
        
        if (userDTO.getSupervisorAccounts() != null) {
            existingUser.setSupervisorAccounts(userDTO.getSupervisorAccounts());
        }
        
        // 如果提供了新密码，则更新密码
        if (userDTO.getPassword() != null && !userDTO.getPassword().isEmpty()) {
            existingUser.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }
        
        return userRepository.save(existingUser);
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        User user = getUserById(id);
        userRepository.delete(user);
    }

    @Override
    public User getUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("未找到用户: " + id));
    }

    @Override
    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("未找到用户: " + username));
    }

    @Override
    public User getUserByPhone(String phone) {
        return userRepository.findByPhone(phone)
                .orElseThrow(() -> new RuntimeException("未找到用户: " + phone));
    }

    @Override
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    @Override
    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    public List<User> getUsersByRole(Role role) {
        return userRepository.findByRole(role);
    }

    @Override
    public Page<User> getUsersByRole(Role role, Pageable pageable) {
        return userRepository.findByRole(role, pageable);
    }

    @Override
    public List<User> getUsersByCreator(String creator) {
        return userRepository.findByCreator(creator);
    }

    @Override
    public List<User> getUsersByCreatorAndRole(String creator, Role role) {
        return userRepository.findByCreatorAndRole(creator, role);
    }

    @Override
    public Page<User> getUsersByCreatorAndRole(String creator, Role role, Pageable pageable) {
        return userRepository.findByCreatorAndRole(creator, role, pageable);
    }

    @Override
    public boolean hasPermissionToManageRole(User currentUser, Role targetRole) {
        if (currentUser.getRole() == Role.SUPER_ADMIN) {
            // 超级管理员可以管理所有角色
            return true;
        } else if (currentUser.getRole() == Role.ADMIN) {
            // 管理员只能管理督学
            return targetRole == Role.SUPERVISOR;
        }
        // 其他角色没有管理权限
        return false;
    }
} 