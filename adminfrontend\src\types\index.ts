// 角色类型定义 - 与后端 Role 枚举保持一致
export type UserRole = 'SUPER_ADMIN' | 'ADMIN' | 'DEALER' | 'AGENT' | 'SUPERVISOR';

// 用户类型定义
export interface User {
  id: number;
  username: string;
  fullName: string;
  phone: string;
  role: UserRole;
  enabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 登录响应类型 - 与后端 JwtResponse 保持一致
export interface LoginResponse {
  token: string;
  type?: string; // Bearer
  id: number;
  username: string;
  fullName: string;
  phone: string;
  role: UserRole;
}

// 分页响应类型
export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// API错误类型
export interface ApiError {
  message: string;
  status: number;
  timestamp: string;
}

// 用户表单数据类型
export interface UserFormData {
  username: string;
  fullName: string;
  phone: string;
  password?: string;
  role: UserRole;
  enabled: boolean;
}

// 题目类型枚举 - 与后端 QuestionType 枚举保持一致
export type QuestionType =
  | 'SINGLE_CHOICE'           // 单选题
  | 'MULTIPLE_CHOICE'         // 多选题
  | 'FILL_IN_BLANK'          // 填空题
  | 'TRUE_FALSE'             // 判断题
  | 'MATCHING'               // 匹配题
  | 'READING_COMPREHENSION'   // 阅读理解
  | 'CLOZE_TEST'             // 完形填空
  | 'LISTENING';             // 听力题

// 科目枚举 - 与后端 Subject 枚举保持一致
export type SubjectEnum =
  | 'ENGLISH'    // 英语
  | 'MATH'       // 数学
  | 'PHYSICS'    // 物理
  | 'CHEMISTRY'; // 化学

// 难度枚举 - 与后端 Difficulty 枚举保持一致
export type Difficulty =
  | 'EASY'       // 简单
  | 'MEDIUM'     // 中等
  | 'HARD';      // 困难

// 科目类型定义
export interface Subject {
  id: number;
  name: string;
  description?: string;
  versions?: SubjectVersion[];
  createdAt?: string;
  updatedAt?: string;
}

// 科目版本类型定义
export interface SubjectVersion {
  id: number;
  name: string;
  description?: string;
  schoolLevel?: string;
  subject?: Subject;
  createdAt?: string;
  updatedAt?: string;
}

// 章节类型定义
export interface Chapter {
  id: number;
  name: string;
  orderIndex: number;
  subjectVersion?: SubjectVersion;
  subjectVersionId?: number;
  subjectVersionName?: string; // 学科版本名称
  schoolLevel?: string; // 年级学期信息
  subjectName?: string; // 学科名称
  knowledgePoints?: KnowledgePoint[];
  createdAt?: string;
  updatedAt?: string;
}

// 知识点类型定义
export interface KnowledgePoint {
  id: number;
  name: string;
  orderIndex: number;
  coverImageUrl?: string;
  description?: string;
  enabled?: boolean;
  chapter?: Chapter;
  chapterId?: number;
  videoCollectionId?: number;
  videoCollectionName?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 知识点层级信息类型定义
export interface KnowledgePointHierarchyInfo {
  knowledgePointId: number;
  knowledgePointName: string;
  chapterId: number;
  chapterName: string;
  subjectVersionId: number;
  subjectVersionName: string;
  subjectId: number;
  subjectName: string;
}

// 题目类型定义
export interface Question {
  id: number;
  questionType: QuestionType;
  body: QuestionBody;
  knowledgePointId: number;
  enabled: boolean;
  subject?: string; // 从body中提取的科目信息
  difficulty?: string; // 从body中提取的难度信息
  createdAt?: string;
  updatedAt?: string;
}

// 题目内容结构 - 基于数据模型解释文件的JSON格式
export interface QuestionBody {
  type: QuestionType;
  id: string;
  subject: SubjectEnum;
  difficulty: Difficulty;
  tags: string[];
  content: string;  // HTML格式的题目内容
  answer: any;      // 答案内容，根据题型不同格式不同
  explanation: string; // HTML格式的解析内容

  // 选择题特有字段
  options?: string[]; // HTML格式的选项

  // 嵌套题型特有字段
  material?: string;  // HTML格式的材料内容
  subQuestions?: SubQuestion[]; // 子题目数组
}

// 子题目结构（用于阅读理解、听力、完形填空等）
export interface SubQuestion {
  id: string;
  type?: QuestionType;
  content: string;
  options?: string[];
  answer: any;
  explanation?: string;
}

// 题目创建请求类型
export interface QuestionCreateRequest {
  knowledgePointId: number;
  questionType: QuestionType;
  body: QuestionBody;
  enabled?: boolean;
}

// 题目更新请求类型
export interface QuestionUpdateRequest {
  knowledgePointId?: number;
  questionType?: QuestionType;
  body?: QuestionBody;
  enabled?: boolean;
}

// 题目表单数据类型
export interface QuestionFormData {
  knowledgePointId: number;
  questionType: QuestionType;
  subject: SubjectEnum;
  difficulty: Difficulty;
  tags: string[];
  content: string;
  answer: any;
  explanation: string;
  options?: string[];
  material?: string;
  subQuestions?: SubQuestion[];
  enabled: boolean;
}

// 题目导入结果类型
export interface QuestionImportResult {
  total: number;
  success: number;
  fail: number;
  errors: string[];
}

// 题目统计信息类型
export interface QuestionStatistics {
  total: number;
  enabled: number;
  disabled: number;
  byType?: Record<QuestionType, number>;
  bySubject?: Record<SubjectEnum, number>;
  byDifficulty?: Record<Difficulty, number>;
}

// 导出迁移相关类型
export * from './migration';
