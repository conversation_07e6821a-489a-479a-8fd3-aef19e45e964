import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Typography,
  Skeleton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  School as SubjectIcon,
  MenuBook as VersionIcon,
  Book as ChapterIcon,
  Topic as KnowledgePointIcon
} from '@mui/icons-material';

// 知识点路径信息接口
export interface KnowledgePointPathInfo {
  knowledgePointId: number;
  knowledgePointName: string;
  chapterId: number;
  chapterName: string;
  subjectVersionId: number;
  subjectVersionName: string;
  subjectId: number;
  subjectName: string;
}

interface KnowledgePointPathProps {
  knowledgePointId: number;
  variant?: 'full' | 'compact' | 'minimal';
  showIcons?: boolean;
  maxWidth?: number;
  onPathLoad?: (pathInfo: KnowledgePointPathInfo | null) => void;
}

/**
 * 知识点路径显示组件
 * 显示完整的层级路径：科目 → 版本 → 章节 → 知识点
 */
const KnowledgePointPath: React.FC<KnowledgePointPathProps> = ({
  knowledgePointId,
  variant = 'full',
  showIcons = true,
  maxWidth,
  onPathLoad
}) => {
  const [pathInfo, setPathInfo] = useState<KnowledgePointPathInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 调用真实API获取知识点路径信息
  useEffect(() => {
    const fetchKnowledgePointPath = async () => {
      try {
        setLoading(true);
        setError(null);

        // 调用真实API获取知识点层级信息
        const { curriculumAPI } = await import('../services/api');
        const hierarchyInfo = await curriculumAPI.getKnowledgePointHierarchy(knowledgePointId);

        const pathInfo: KnowledgePointPathInfo = {
          knowledgePointId: hierarchyInfo.knowledgePointId,
          knowledgePointName: hierarchyInfo.knowledgePointName,
          chapterId: hierarchyInfo.chapterId,
          chapterName: hierarchyInfo.chapterName,
          subjectVersionId: hierarchyInfo.subjectVersionId,
          subjectVersionName: hierarchyInfo.subjectVersionName,
          subjectId: hierarchyInfo.subjectId,
          subjectName: hierarchyInfo.subjectName
        };

        setPathInfo(pathInfo);
        onPathLoad?.(pathInfo);
      } catch (err) {
        const errorMessage = '获取知识点路径失败';
        setError(errorMessage);
        onPathLoad?.(null);
        console.error('获取知识点路径失败:', err);
      } finally {
        setLoading(false);
      }
    };

    if (knowledgePointId) {
      fetchKnowledgePointPath();
    } else {
      setLoading(false);
      setPathInfo(null);
      onPathLoad?.(null);
    }
  }, [knowledgePointId, onPathLoad]);

  // 加载状态
  if (loading) {
    return (
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        <Skeleton variant="rectangular" width={60} height={24} />
        <Typography variant="body2" color="text.secondary">→</Typography>
        <Skeleton variant="rectangular" width={80} height={24} />
        <Typography variant="body2" color="text.secondary">→</Typography>
        <Skeleton variant="rectangular" width={70} height={24} />
        <Typography variant="body2" color="text.secondary">→</Typography>
        <Skeleton variant="rectangular" width={90} height={24} />
      </Box>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Alert severity="error" sx={{ py: 0.5 }}>
        {error}
      </Alert>
    );
  }

  // 无数据状态
  if (!pathInfo) {
    return (
      <Typography variant="body2" color="text.secondary">
        未知知识点
      </Typography>
    );
  }

  // 渲染不同变体
  const renderFullPath = () => (
    <Box 
      sx={{ 
        display: 'flex', 
        gap: 1, 
        alignItems: 'center',
        flexWrap: 'wrap',
        maxWidth: maxWidth ? `${maxWidth}px` : 'none'
      }}
    >
      <Tooltip title="科目">
        <Chip
          icon={showIcons ? <SubjectIcon fontSize="small" /> : undefined}
          label={pathInfo.subjectName}
          size="small"
          color="primary"
          variant="outlined"
        />
      </Tooltip>
      
      <Typography variant="body2" color="text.secondary">→</Typography>
      
      <Tooltip title="版本">
        <Chip
          icon={showIcons ? <VersionIcon fontSize="small" /> : undefined}
          label={pathInfo.subjectVersionName}
          size="small"
          color="secondary"
          variant="outlined"
        />
      </Tooltip>
      
      <Typography variant="body2" color="text.secondary">→</Typography>
      
      <Tooltip title="章节">
        <Chip
          icon={showIcons ? <ChapterIcon fontSize="small" /> : undefined}
          label={pathInfo.chapterName}
          size="small"
          color="info"
          variant="outlined"
        />
      </Tooltip>
      
      <Typography variant="body2" color="text.secondary">→</Typography>
      
      <Tooltip title="知识点">
        <Chip
          icon={showIcons ? <KnowledgePointIcon fontSize="small" /> : undefined}
          label={pathInfo.knowledgePointName}
          size="small"
          color="success"
          variant="filled"
        />
      </Tooltip>
    </Box>
  );

  const renderCompactPath = () => (
    <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
      <Typography variant="body2" color="text.secondary">
        {pathInfo.subjectName} / {pathInfo.subjectVersionName} / {pathInfo.chapterName}
      </Typography>
      <Typography variant="body2" color="text.secondary">→</Typography>
      <Chip
        label={pathInfo.knowledgePointName}
        size="small"
        color="success"
        variant="filled"
      />
    </Box>
  );

  const renderMinimalPath = () => (
    <Tooltip title={`${pathInfo.subjectName} → ${pathInfo.subjectVersionName} → ${pathInfo.chapterName} → ${pathInfo.knowledgePointName}`}>
      <Chip
        label={pathInfo.knowledgePointName}
        size="small"
        color="success"
        variant="outlined"
      />
    </Tooltip>
  );

  // 根据变体渲染
  switch (variant) {
    case 'compact':
      return renderCompactPath();
    case 'minimal':
      return renderMinimalPath();
    case 'full':
    default:
      return renderFullPath();
  }
};

export default KnowledgePointPath;
