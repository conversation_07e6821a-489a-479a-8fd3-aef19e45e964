import { curriculumAPI } from '../services/api';
import { PendingFile } from '../components/WangEditor';

// 文件上传结果接口
export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

// 批量上传文件并返回URL映射
export const uploadPendingFiles = async (
  files: PendingFile[],
  onProgress?: (current: number, total: number) => void
): Promise<Map<string, UploadResult>> => {
  const results = new Map<string, UploadResult>();
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    onProgress?.(i + 1, files.length);
    
    try {
      let result;
      if (file.type === 'image') {
        result = await curriculumAPI.uploadImage(file.file);
      } else if (file.type === 'audio') {
        result = await curriculumAPI.uploadAudio(file.file);
      } else {
        throw new Error(`不支持的文件类型: ${file.type}`);
      }
      
      if (result.success && result.url) {
        results.set(file.id, {
          success: true,
          url: result.url
        });
      } else {
        results.set(file.id, {
          success: false,
          error: result.message || '上传失败'
        });
      }
    } catch (error) {
      results.set(file.id, {
        success: false,
        error: error instanceof Error ? error.message : '上传异常'
      });
    }
  }
  
  return results;
};

// 替换HTML内容中的临时URL为真实URL
export const replaceTemporaryUrls = (
  html: string,
  uploadResults: Map<string, UploadResult>
): string => {
  let processedHtml = html;

  console.log('开始替换临时URL，原始HTML:', html);
  console.log('上传结果:', uploadResults);

  // 替换图片和音频的临时URL
  uploadResults.forEach((result, fileId) => {
    if (result.success && result.url) {
      console.log(`处理文件 ${fileId}，新URL: ${result.url}`);

      // 替换图片的临时URL
      if (processedHtml.includes(`pending_image_${fileId}`)) {
        console.log(`找到图片标记: pending_image_${fileId}`);

        // 更精确的图片标签匹配和替换
        const imgRegex = new RegExp(
          `<img([^>]*alt="pending_image_${fileId}"[^>]*)>`,
          'g'
        );

        processedHtml = processedHtml.replace(imgRegex, (match, attributes) => {
          console.log('匹配到的img标签:', match);

          // 替换所有blob URL为真实URL
          let newAttributes = attributes
            .replace(/src="blob:[^"]*"/g, `src="${result.url}"`)
            .replace(/data-href="blob:[^"]*"/g, `data-href="${result.url}"`);

          const newImg = `<img${newAttributes}>`;
          console.log('替换后的img标签:', newImg);
          return newImg;
        });
      }

      // 替换音频的待上传标记
      if (processedHtml.includes(`pending_audio_${fileId}`)) {
        console.log(`找到音频标记: pending_audio_${fileId}`);

        // 匹配实际的音频HTML结构并替换
        const audioTextRegex = new RegExp(
          `\\(待上传: pending_audio_${fileId}\\)`,
          'g'
        );

        processedHtml = processedHtml.replace(audioTextRegex,
          `<a href="${result.url}" target="_blank" class="audio-link">(点击播放)</a>`
        );

        console.log(`音频URL替换完成: ${result.url}`);
      }
    }
  });

  console.log('替换完成，最终HTML:', processedHtml);
  return processedHtml;
};

// 清理临时URL
export const cleanupTempUrls = (files: PendingFile[]): void => {
  files.forEach(file => {
    try {
      URL.revokeObjectURL(file.tempUrl);
    } catch (error) {
      console.warn('清理临时URL失败:', error);
    }
  });
};

// 验证文件类型和大小
export const validateFile = (file: File, type: 'image' | 'audio'): string | null => {
  if (type === 'image') {
    if (!file.type.startsWith('image/')) {
      return '请选择有效的图片文件';
    }
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return '图片文件大小不能超过10MB';
    }
  } else if (type === 'audio') {
    const allowedTypes = [
      'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/wave', 'audio/x-wav',
      'audio/ogg', 'audio/mp4', 'audio/m4a', 'audio/aac', 'audio/x-aac',
      'audio/flac', 'audio/webm'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      const fileName = file.name.toLowerCase();
      const allowedExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac', '.webm'];
      const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
      
      if (!hasValidExtension) {
        return '请选择有效的音频文件 (MP3, WAV, OGG, M4A, AAC, FLAC, WebM)';
      }
    }
    
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return '音频文件大小不能超过50MB';
    }
  }
  
  return null;
};
