package com.example.adminbackend.service;

import com.example.adminbackend.config.QiniuConfig;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 七牛云上传服务
 */
@Service
@Slf4j
public class QiniuUploadService {
    
    @Autowired
    private Auth auth;

    @Autowired
    private UploadManager uploadManager;

    @Autowired
    private BucketManager bucketManager;

    @Autowired
    private QiniuConfig qiniuConfig;
    
    /**
     * 允许的图片类型
     */
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    /**
     * 允许的视频类型
     */
    private static final List<String> ALLOWED_VIDEO_TYPES = Arrays.asList(
        "video/mp4", "video/avi", "video/mov", "video/wmv", "video/flv", "video/webm"
    );

    /**
     * 最大图片文件大小 (5MB)
     */
    private static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024;

    /**
     * 最大视频文件大小 (100MB)
     */
    private static final long MAX_VIDEO_SIZE = 100 * 1024 * 1024;
    
    /**
     * 上传图片文件
     * 
     * @param file 图片文件
     * @return 图片访问URL
     * @throws Exception 上传异常
     */
    public String uploadImage(MultipartFile file) throws Exception {
        // 验证文件
        validateImageFile(file);

        // 生成唯一文件名
        String fileName = generateFileName(file.getOriginalFilename());

        // 生成上传凭证
        String token = auth.uploadToken(qiniuConfig.getBucketName());

        log.info("开始上传图片: {}, 大小: {} bytes, 存储空间: {}, 区域: {}",
            fileName, file.getSize(), qiniuConfig.getBucketName(), qiniuConfig.getRegion());
        log.info("生成的文件名: {}", fileName);
        log.info("上传凭证长度: {}", token != null ? token.length() : "null");

        // 执行上传
        Response response = uploadManager.put(file.getBytes(), fileName, token);

        log.info("七牛云响应状态: {}, 响应码: {}", response.isOK(), response.statusCode);
        if (response.bodyString() != null) {
            log.info("七牛云响应内容: {}", response.bodyString());
        }

        if (response.isOK()) {
            String imageUrl = buildImageUrl(fileName);
            log.info("图片上传成功: {}", imageUrl);
            return imageUrl;
        } else {
            log.error("图片上传失败 - 状态码: {}, 错误信息: {}", response.statusCode, response.error);
            if (response.bodyString() != null) {
                log.error("详细错误信息: {}", response.bodyString());
            }
            throw new RuntimeException("上传失败: " + response.error + " (状态码: " + response.statusCode + ")");
        }
    }
    
    /**
     * 验证图片文件
     * 
     * @param file 文件
     */
    public void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_IMAGE_TYPES.contains(contentType.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型，仅支持: JPG, PNG, GIF, WebP");
        }
        
        // 检查文件大小
        if (file.getSize() > MAX_IMAGE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过5MB");
        }
        
        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
    }
    
    /**
     * 生成唯一文件名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    private String generateFileName(String originalFilename) {
        return generateFileName(originalFilename, qiniuConfig.getUploadPaths().getImage());
    }

    /**
     * 生成指定类型的文件名
     *
     * @param originalFilename 原始文件名
     * @param pathPrefix 路径前缀
     * @return 唯一文件名
     */
    private String generateFileName(String originalFilename, String pathPrefix) {
        String extension = getFileExtension(originalFilename);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 按日期分目录存储
        String dateDir = java.time.LocalDate.now().toString().replace("-", "/");

        return String.format("%s/%s/%s_%s.%s", pathPrefix, dateDir, timestamp, uuid, extension);
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "jpg";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }
    
    /**
     * 构建图片访问URL
     *
     * @param fileName 文件名
     * @return 完整URL
     */
    private String buildImageUrl(String fileName) {
        String domain = qiniuConfig.getDomain();
        if (!domain.startsWith("http")) {
            domain = "http://" + domain;  // 静态资源使用HTTP
        }
        return domain + "/" + fileName;
    }
    
    /**
     * 获取上传凭证
     *
     * @return 上传凭证
     */
    public String getUploadToken() {
        return auth.uploadToken(qiniuConfig.getBucketName());
    }

    /**
     * 测试七牛云配置
     *
     * @return 配置信息
     */
    public String testQiniuConfig() {
        StringBuilder sb = new StringBuilder();
        sb.append("七牛云配置信息:\n");
        sb.append("AccessKey: ").append(qiniuConfig.getAccessKey() != null ?
            qiniuConfig.getAccessKey().substring(0, 10) + "..." : "null").append("\n");
        sb.append("SecretKey: ").append(qiniuConfig.getSecretKey() != null ?
            qiniuConfig.getSecretKey().substring(0, 10) + "..." : "null").append("\n");
        sb.append("BucketName: ").append(qiniuConfig.getBucketName()).append("\n");
        sb.append("Domain: ").append(qiniuConfig.getDomain()).append("\n");
        sb.append("Region: ").append(qiniuConfig.getRegion()).append("\n");
        sb.append("ImagePath: ").append(qiniuConfig.getUploadPaths().getImage()).append("\n");

        try {
            String token = auth.uploadToken(qiniuConfig.getBucketName());
            sb.append("Token生成: 成功 (长度: ").append(token.length()).append(")\n");
        } catch (Exception e) {
            sb.append("Token生成: 失败 - ").append(e.getMessage()).append("\n");
        }

        return sb.toString();
    }
    
    /**
     * 获取上传凭证（带过期时间）
     * 
     * @param expires 过期时间（秒）
     * @return 上传凭证
     */
    public String getUploadToken(long expires) {
        return auth.uploadToken(qiniuConfig.getBucketName(), null, expires, null);
    }
    
    /**
     * 上传视频文件
     *
     * @param file 视频文件
     * @return 视频访问URL
     * @throws Exception 上传异常
     */
    public String uploadVideo(MultipartFile file) throws Exception {
        // 验证文件
        validateVideoFile(file);

        // 生成唯一文件名
        String fileName = generateVideoFileName(file.getOriginalFilename());

        // 生成上传凭证
        String token = auth.uploadToken(qiniuConfig.getBucketName());

        log.info("开始上传视频: {}, 大小: {} bytes", fileName, file.getSize());

        // 执行上传
        Response response = uploadManager.put(file.getBytes(), fileName, token);

        if (response.isOK()) {
            String videoUrl = buildVideoUrl(fileName);
            log.info("视频上传成功: {}", videoUrl);
            return videoUrl;
        } else {
            log.error("视频上传失败: {}", response.error);
            throw new RuntimeException("上传失败: " + response.error);
        }
    }

    /**
     * 验证视频文件
     *
     * @param file 文件
     */
    public void validateVideoFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_VIDEO_TYPES.contains(contentType.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型，仅支持: MP4, AVI, MOV, WMV, FLV, WebM");
        }

        // 检查文件大小
        if (file.getSize() > MAX_VIDEO_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过100MB");
        }

        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
    }

    /**
     * 生成视频文件名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    private String generateVideoFileName(String originalFilename) {
        return generateFileName(originalFilename, qiniuConfig.getUploadPaths().getVideo());
    }

    /**
     * 构建视频访问URL
     *
     * @param fileName 文件名
     * @return 完整URL
     */
    private String buildVideoUrl(String fileName) {
        String domain = qiniuConfig.getDomain();
        if (!domain.startsWith("http")) {
            domain = "http://" + domain;  // 静态资源使用HTTP
        }
        return domain + "/" + fileName;
    }

    /**
     * 检查文件是否为有效图片
     *
     * @param file 文件
     * @return 是否有效
     */
    public boolean isValidImage(MultipartFile file) {
        try {
            validateImageFile(file);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查文件是否为有效视频
     *
     * @param file 文件
     * @return 是否有效
     */
    public boolean isValidVideo(MultipartFile file) {
        try {
            validateVideoFile(file);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 上传音频文件
     *
     * @param file 音频文件
     * @return 音频访问URL
     * @throws Exception 上传异常
     */
    public String uploadAudio(MultipartFile file) throws Exception {
        return uploadFile(file, qiniuConfig.getUploadPaths().getAudio(), "音频");
    }

    /**
     * 通用文件上传方法
     *
     * @param file 文件
     * @param pathPrefix 路径前缀
     * @param fileType 文件类型描述
     * @return 文件访问URL
     * @throws Exception 上传异常
     */
    private String uploadFile(MultipartFile file, String pathPrefix, String fileType) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 生成唯一文件名
        String fileName = generateFileName(file.getOriginalFilename(), pathPrefix);

        // 生成上传凭证
        String token = auth.uploadToken(qiniuConfig.getBucketName());

        log.info("开始上传{}: {}, 大小: {} bytes", fileType, fileName, file.getSize());

        // 执行上传
        Response response = uploadManager.put(file.getBytes(), fileName, token);

        if (response.isOK()) {
            String fileUrl = buildFileUrl(fileName);
            log.info("{}上传成功: {}", fileType, fileUrl);
            return fileUrl;
        } else {
            log.error("{}上传失败: {}", fileType, response.error);
            throw new RuntimeException("上传失败: " + response.error);
        }
    }

    /**
     * 构建文件访问URL
     *
     * @param fileName 文件名
     * @return 完整URL
     */
    private String buildFileUrl(String fileName) {
        String domain = qiniuConfig.getDomain();
        if (!domain.startsWith("http")) {
            domain = "http://" + domain;  // 静态资源使用HTTP
        }
        return domain + "/" + fileName;
    }

    // ==================== 文件删除方法 ====================

    /**
     * 删除七牛云文件
     *
     * @param fileUrl 文件完整URL
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileUrl) {
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            log.warn("文件URL为空，跳过删除");
            return false;
        }

        try {
            // 从URL中提取文件key
            String fileKey = extractFileKeyFromUrl(fileUrl);
            if (fileKey == null) {
                log.warn("无法从URL中提取文件key: {}", fileUrl);
                return false;
            }

            log.info("开始删除七牛云文件: {}", fileKey);

            // 执行删除操作
            bucketManager.delete(qiniuConfig.getBucketName(), fileKey);

            log.info("七牛云文件删除成功: {}", fileKey);
            return true;

        } catch (QiniuException e) {
            if (e.code() == 612) {
                log.warn("文件不存在，可能已被删除: {}", fileUrl);
                return true; // 文件不存在也算删除成功
            } else {
                log.error("删除七牛云文件失败: {}, 错误码: {}, 错误信息: {}",
                    fileUrl, e.code(), e.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("删除七牛云文件异常: {}", fileUrl, e);
            return false;
        }
    }

    /**
     * 从文件URL中提取文件key
     *
     * @param fileUrl 文件完整URL
     * @return 文件key
     */
    private String extractFileKeyFromUrl(String fileUrl) {
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            return null;
        }

        try {
            String domain = qiniuConfig.getDomain();

            // 处理不同的URL格式
            String[] possiblePrefixes = {
                "https://" + domain + "/",
                "http://" + domain + "/",
                domain + "/"
            };

            for (String prefix : possiblePrefixes) {
                if (fileUrl.startsWith(prefix)) {
                    return fileUrl.substring(prefix.length());
                }
            }

            // 如果URL不包含域名，可能直接就是key
            if (!fileUrl.startsWith("http")) {
                return fileUrl;
            }

            log.warn("无法识别的文件URL格式: {}", fileUrl);
            return null;

        } catch (Exception e) {
            log.error("提取文件key失败: {}", fileUrl, e);
            return null;
        }
    }

    /**
     * 批量删除七牛云文件
     *
     * @param fileUrls 文件URL列表
     * @return 删除成功的数量
     */
    public int deleteFiles(List<String> fileUrls) {
        if (fileUrls == null || fileUrls.isEmpty()) {
            log.warn("文件URL列表为空，跳过批量删除");
            return 0;
        }

        int successCount = 0;
        for (String fileUrl : fileUrls) {
            if (deleteFile(fileUrl)) {
                successCount++;
            }
        }

        log.info("批量删除文件完成，成功: {}, 总数: {}", successCount, fileUrls.size());
        return successCount;
    }
}
