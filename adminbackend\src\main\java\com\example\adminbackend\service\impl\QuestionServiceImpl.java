package com.example.adminbackend.service.impl;

import com.example.adminbackend.dto.QuestionCreateRequest;
import com.example.adminbackend.dto.QuestionFormCreateRequest;
import com.example.adminbackend.dto.QuestionUpdateRequest;
import com.example.adminbackend.dto.BatchImportResult;
import com.example.adminbackend.dto.KnowledgePointHierarchyInfo;
import com.example.adminbackend.model.Question;
import com.example.adminbackend.repository.QuestionRepository;
import com.example.adminbackend.repository.StudentAnswerRepository;
import com.example.adminbackend.service.QuestionService;
import com.example.adminbackend.service.KnowledgePointService;
import com.example.adminbackend.validator.QuestionBodyValidator;
import com.example.adminbackend.validator.ValidationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.beans.factory.annotation.Value;

import jakarta.persistence.EntityManager;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 题目服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuestionServiceImpl implements QuestionService {

    private final QuestionRepository questionRepository;
    private final StudentAnswerRepository studentAnswerRepository;
    private final QuestionBodyValidator questionBodyValidator;
    private final KnowledgePointService knowledgePointService;
    private final EntityManager entityManager;

    @Value("${question.import.batch-size:100}")
    private int batchSize;

    // ==================== 查询方法 ====================

    @Override
    @Transactional(readOnly = true)
    public Page<Question> getQuestions(Long knowledgePointId, Question.QuestionType questionType,
                                     Question.Subject subject, String difficulty, Boolean enabled, String search, Pageable pageable) {
        log.info("查询题目列表 - 参数: knowledgePointId={}, questionType={}, subject={}, difficulty={}, enabled={}, search={}",
                knowledgePointId, questionType, subject, difficulty, enabled, search);

        try {
            // 标准化参数，将空字符串转换为null
            String normalizedDifficulty = (difficulty != null && difficulty.trim().isEmpty()) ? null : difficulty;
            String normalizedSearch = (search != null && search.trim().isEmpty()) ? null : search;

            // 先尝试简单查询，避免复杂的JSON查询导致错误
            if (knowledgePointId == null && questionType == null && subject == null &&
                normalizedDifficulty == null && enabled == null && normalizedSearch == null) {
                // 如果没有任何过滤条件，直接返回所有数据
                log.info("使用简单查询获取所有题目");
                Page<Question> result = questionRepository.findAll(pageable);
                log.info("查询结果: 总数={}, 当前页数量={}", result.getTotalElements(), result.getContent().size());
                return result;
            }

            // 使用复合条件查询
            String questionTypeStr = questionType != null ? questionType.name() : null;
            String subjectStr = subject != null ? subject.name() : null;

            log.info("使用复合条件查询 - 参数: questionType={}, subject={}, difficulty={}, enabled={}, search={}",
                    questionTypeStr, subjectStr, normalizedDifficulty, enabled, normalizedSearch);

            // 创建不包含排序的Pageable对象，因为SQL中已经硬编码了排序
            Pageable unsortedPageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());

            Page<Question> result = questionRepository.findByComplexConditionsWithSearch(
                knowledgePointId,
                questionTypeStr,
                subjectStr,
                normalizedDifficulty,
                enabled,
                normalizedSearch,
                unsortedPageable
            );
            log.info("查询结果: 总数={}, 当前页数量={}", result.getTotalElements(), result.getContent().size());
            return result;
        } catch (Exception e) {
            log.error("查询题目列表失败", e);
            // 如果复杂查询失败，回退到简单查询
            log.info("回退到简单查询");
            return questionRepository.findAll(pageable);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Question> getQuestionsByKnowledgePoint(Long knowledgePointId) {
        return questionRepository.findByKnowledgePointIdAndEnabledTrue(knowledgePointId);
    }

    @Override
    @Transactional(readOnly = true)
    public Question getQuestionById(Long id) {
        return questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在: " + id));
    }

    // ==================== 管理方法 ====================

    @Override
    @Transactional
    public Question createQuestion(QuestionCreateRequest request) {
        // 使用新的完整验证器验证题目JSON格式
        ValidationResult validationResult = questionBodyValidator.validateQuestionBody(request.getBody());
        if (!validationResult.isValid()) {
            throw new RuntimeException("题目数据验证失败: " + validationResult.getFirstErrorMessage());
        }

        Question question = Question.builder()
                .id(null) // 强制ID为null，确保数据库生成新ID
                .knowledgePointId(request.getKnowledgePointId())
                .questionType(request.getQuestionType())
                .body(request.getBody())
                .enabled(request.getEnabled())
                .build();

        return questionRepository.save(question);
    }

    @Override
    @Transactional
    public Question createQuestionFromForm(QuestionFormCreateRequest request) {
        log.info("专门为前端表单创建题目，知识点ID: {}", request.getKnowledgePointId());

        // 构建题目body数据
        Map<String, Object> body = buildQuestionBodyFromForm(request);

        // 创建Question对象
        Question question = Question.builder()
                .id(null) // 强制ID为null，确保数据库生成新ID
                .knowledgePointId(request.getKnowledgePointId())
                .questionType(request.getQuestionType())
                .body(body)
                .enabled(request.getEnabled())
                .build();

        return questionRepository.save(question);
    }

    /**
     * 从前端表单请求构建题目body数据
     * 自动从知识点ID获取科目信息
     */
    private Map<String, Object> buildQuestionBodyFromForm(QuestionFormCreateRequest request) {
        Map<String, Object> body = new HashMap<>();

        // 基础字段
        body.put("type", request.getQuestionType().name());
        body.put("id", String.valueOf(System.currentTimeMillis())); // 自动生成ID防重复
        body.put("content", request.getContent());

        // 处理答案字段 - 支持不同类型的答案格式
        Object answer = request.getAnswer();
        if (answer != null) {
            body.put("answer", answer);
        }

        body.put("explanation", request.getExplanation());
        body.put("difficulty", request.getDifficulty());
        body.put("tags", request.getTags() != null ? request.getTags() : new ArrayList<>());

        // 根据知识点ID自动获取科目信息
        try {
            KnowledgePointHierarchyInfo hierarchyInfo = knowledgePointService.getKnowledgePointHierarchy(request.getKnowledgePointId());
            String subjectName = hierarchyInfo.getSubjectName();

            // 将中文科目名称映射为英文枚举值
            String subjectEnum = mapSubjectNameToEnum(subjectName);
            body.put("subject", subjectEnum);

            log.info("自动设置科目信息: {} -> {}", subjectName, subjectEnum);
        } catch (Exception e) {
            log.error("获取知识点层级信息失败，使用默认科目", e);
            body.put("subject", "ENGLISH"); // 默认值
        }

        // 选择题选项
        if (request.getOptions() != null && !request.getOptions().isEmpty()) {
            body.put("options", request.getOptions());
        }

        // 复合题材料和子题目
        if (request.getMaterial() != null && !request.getMaterial().trim().isEmpty()) {
            body.put("material", request.getMaterial());
        }
        if (request.getSubQuestions() != null && !request.getSubQuestions().isEmpty()) {
            body.put("subQuestions", request.getSubQuestions());
        }

        return body;
    }

    /**
     * 将中文科目名称映射为英文枚举值
     */
    private String mapSubjectNameToEnum(String subjectName) {
        if (subjectName == null) {
            return "ENGLISH";
        }

        switch (subjectName) {
            case "英语":
                return "ENGLISH";
            case "数学":
                return "MATH";
            case "物理":
                return "PHYSICS";
            case "化学":
                return "CHEMISTRY";
            default:
                log.warn("未知的科目名称: {}，使用默认值ENGLISH", subjectName);
                return "ENGLISH";
        }
    }

    @Override
    @Transactional
    public List<Question> createQuestions(List<QuestionCreateRequest> requests) {
        List<Question> questions = requests.stream()
                .map(this::createQuestion)
                .collect(Collectors.toList());
        return questions;
    }

    @Override
    @Transactional
    public Question updateQuestion(Long id, QuestionUpdateRequest request) {
        Question question = getQuestionById(id);

        if (request.getKnowledgePointId() != null) {
            question.setKnowledgePointId(request.getKnowledgePointId());
        }
        if (request.getQuestionType() != null) {
            question.setQuestionType(request.getQuestionType());
        }
        if (request.getBody() != null) {
            // 使用新的完整验证器验证题目JSON格式
            ValidationResult validationResult = questionBodyValidator.validateQuestionBody(request.getBody());
            if (!validationResult.isValid()) {
                throw new RuntimeException("题目数据验证失败: " + validationResult.getFirstErrorMessage());
            }
            question.setBody(request.getBody());
        }
        if (request.getEnabled() != null) {
            question.setEnabled(request.getEnabled());
        }

        return questionRepository.save(question);
    }

    @Override
    @Transactional
    public Question toggleEnabled(Long id, Boolean enabled) {
        Question question = getQuestionById(id);
        question.setEnabled(enabled);
        return questionRepository.save(question);
    }

    @Override
    @Transactional
    public void deleteQuestion(Long id) {
        log.info("开始删除题目: {}", id);

        if (!questionRepository.existsById(id)) {
            throw new RuntimeException("题目不存在: " + id);
        }

        // 检查是否有相关的学生答题记录
        long answerCount = studentAnswerRepository.countByQuestionId(id);
        if (answerCount > 0) {
            log.warn("题目 {} 存在 {} 条学生答题记录，将先删除这些记录", id, answerCount);
            // 先删除相关的学生答题记录
            studentAnswerRepository.deleteByQuestionId(id);
            log.info("已删除题目 {} 的 {} 条学生答题记录", id, answerCount);
        }

        // 删除题目
        questionRepository.deleteById(id);
        log.info("题目删除完成: {}", id);
    }

    @Override
    @Transactional
    public void deleteQuestions(List<Long> ids) {
        log.info("开始批量删除题目: {}", ids);

        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除题目列表为空");
            return;
        }

        // 检查并删除相关的学生答题记录
        for (Long id : ids) {
            long answerCount = studentAnswerRepository.countByQuestionId(id);
            if (answerCount > 0) {
                log.warn("题目 {} 存在 {} 条学生答题记录", id, answerCount);
            }
        }

        // 批量删除学生答题记录
        studentAnswerRepository.deleteByQuestionIds(ids);
        log.info("已删除题目列表 {} 的所有学生答题记录", ids);

        // 批量删除题目
        questionRepository.deleteAllById(ids);
        log.info("批量删除题目完成: {}", ids);
    }

    // ==================== 导入导出方法 ====================

    @Override
    @Transactional
    public Map<String, Object> importQuestions(Long knowledgePointId, List<Map<String, Object>> questionBodies) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始题目批量导入，数据量: {}, 知识点ID: {}", questionBodies.size(), knowledgePointId);

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        AtomicInteger updateCount = new AtomicInteger(0);
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        try {
            // 分批处理数据
            List<List<Map<String, Object>>> batches = partitionData(questionBodies, batchSize);
            log.info("数据分批完成，共 {} 批，每批最多 {} 条", batches.size(), batchSize);

            for (int batchIndex = 0; batchIndex < batches.size(); batchIndex++) {
                List<Map<String, Object>> batch = batches.get(batchIndex);
                BatchImportResult batchResult = processBatch(batch, knowledgePointId, batchIndex + 1);

                successCount.addAndGet(batchResult.getSuccessCount());
                failCount.addAndGet(batchResult.getFailCount());
                updateCount.addAndGet(batchResult.getUpdateCount());
                errors.addAll(batchResult.getErrors());
                warnings.addAll(batchResult.getWarnings());
            }

            LocalDateTime endTime = LocalDateTime.now();
            long durationMs = java.time.Duration.between(startTime, endTime).toMillis();

            log.info("题目批量导入完成 - 总计: {}, 成功: {}, 更新: {}, 失败: {}, 耗时: {}ms",
                questionBodies.size(), successCount.get(), updateCount.get(), failCount.get(), durationMs);

            Map<String, Object> result = new HashMap<>();
            result.put("total", questionBodies.size());
            result.put("success", successCount.get());
            result.put("fail", failCount.get());
            result.put("update", updateCount.get());
            result.put("errors", errors);
            result.put("warnings", warnings);
            result.put("durationMs", durationMs);

            return result;

        } catch (Exception e) {
            log.error("题目批量导入发生异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("total", questionBodies.size());
            result.put("success", 0);
            result.put("fail", questionBodies.size());
            result.put("errors", List.of("导入过程发生异常: " + e.getMessage()));
            return result;
        }
    }

    @Override
    public List<Map<String, Object>> exportQuestions(Long knowledgePointId, Question.QuestionType questionType,
                                                    Question.Subject subject) {
        log.info("开始导出题目，筛选条件 - 知识点ID: {}, 题型: {}, 科目: {}", knowledgePointId, questionType, subject);

        List<Question> questions;

        // 根据知识点ID筛选
        if (knowledgePointId != null) {
            questions = questionRepository.findByKnowledgePointIdAndEnabledTrue(knowledgePointId);
        } else {
            questions = questionRepository.findByEnabledTrue();
        }

        // 应用题型和科目筛选
        Stream<Question> questionStream = questions.stream();

        if (questionType != null) {
            questionStream = questionStream.filter(q -> {
                Map<String, Object> body = q.getBody();
                String type = (String) body.get("type");
                return questionType.name().equals(type);
            });
        }

        if (subject != null) {
            questionStream = questionStream.filter(q -> {
                Map<String, Object> body = q.getBody();
                String subjectStr = (String) body.get("subject");
                return subject.name().equals(subjectStr);
            });
        }

        List<Map<String, Object>> result = questionStream
                .map(Question::getBody)
                .collect(Collectors.toList());

        log.info("导出完成，共导出 {} 道题目", result.size());
        return result;
    }

    // ==================== 统计和验证方法 ====================

    @Override
    public Map<String, Object> getQuestionStatistics(Long knowledgePointId) {
        Map<String, Object> statistics = new HashMap<>();
        
        if (knowledgePointId != null) {
            long total = questionRepository.countByKnowledgePointId(knowledgePointId);
            long enabled = questionRepository.countByKnowledgePointIdAndEnabledTrue(knowledgePointId);
            
            statistics.put("total", total);
            statistics.put("enabled", enabled);
            statistics.put("disabled", total - enabled);
        } else {
            long total = questionRepository.count();
            long enabled = questionRepository.countByEnabledTrue();
            
            statistics.put("total", total);
            statistics.put("enabled", enabled);
            statistics.put("disabled", total - enabled);
        }
        
        return statistics;
    }

    @Override
    public Map<String, Object> validateQuestionBody(Map<String, Object> questionBody) {
        // 使用新的完整验证器
        ValidationResult validationResult = questionBodyValidator.validateQuestionBody(questionBody);
        return validationResult.toMap();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 数据分批
     */
    private <T> List<List<T>> partitionData(List<T> data, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < data.size(); i += batchSize) {
            int end = Math.min(i + batchSize, data.size());
            batches.add(data.subList(i, end));
        }
        return batches;
    }

    /**
     * 处理单个批次
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private BatchImportResult processBatch(List<Map<String, Object>> batch, Long knowledgePointId, int batchNumber) {
        BatchImportResult result = BatchImportResult.builder().build();
        List<Question> questionsToSave = new ArrayList<>();

        LocalDateTime batchStartTime = LocalDateTime.now();
        log.info("开始处理批次 {}, 数据量: {}", batchNumber, batch.size());

        for (int i = 0; i < batch.size(); i++) {
            try {
                Map<String, Object> body = batch.get(i);
                String questionId = (String) body.get("id");

                // 1. 数据验证
                ValidationResult validationResult = questionBodyValidator.validateQuestionBody(body);
                if (!validationResult.isValid()) {
                    result.addError(questionId, "数据验证失败: " + validationResult.getFirstErrorMessage());
                    result.incrementFail();
                    continue;
                }

                // 2. 构建Question对象
                String typeStr = (String) body.get("type");
                Question.QuestionType questionType = Question.QuestionType.valueOf(typeStr);

                Question question = Question.builder()
                        .knowledgePointId(knowledgePointId)
                        .questionType(questionType)
                        .body(body)
                        .enabled(true)
                        .build();

                // 3. 由于ID是自动生成的时间戳，基本不会重复，直接保存
                // 但仍保留检查逻辑以防万一
                Question existingQuestion = findExistingQuestionByBodyId(questionId);
                if (existingQuestion != null) {
                    log.warn("发现重复题目ID（极少情况），执行更新操作: 题目ID={}, 现有数据库ID={}",
                        questionId, existingQuestion.getId());

                    // 更新现有题目
                    existingQuestion.setBody(body);
                    existingQuestion.setQuestionType(questionType);
                    questionRepository.save(existingQuestion);

                    result.addSuccessId(questionId + " (更新)");
                    result.incrementUpdate();
                    result.addWarning(questionId, "题目ID重复，已更新");
                } else {
                    // 新题目，加入批量保存列表
                    questionsToSave.add(question);
                    result.addSuccessId(questionId);
                    result.incrementSuccess();
                }

            } catch (Exception e) {
                String questionId = "未知";
                try {
                    questionId = (String) batch.get(i).get("id");
                } catch (Exception ignored) {}

                log.error("处理题目数据失败，题目ID: {}", questionId, e);
                result.addError(questionId, e.getMessage());
                result.incrementFail();
            }
        }

        // 4. 批量保存新题目
        if (!questionsToSave.isEmpty()) {
            try {
                batchSaveQuestions(questionsToSave);
                log.info("批次 {} 保存完成，成功保存 {} 条新数据", batchNumber, questionsToSave.size());
            } catch (Exception e) {
                log.error("批次 {} 保存失败", batchNumber, e);
                questionsToSave.forEach(q -> {
                    String questionId = (String) q.getBody().get("id");
                    result.addError(questionId, "保存失败: " + e.getMessage());
                    result.incrementFail();
                    // 注意：这里不能调用decrementSuccess，因为BatchImportResult没有这个方法
                });
            }
        }

        LocalDateTime batchEndTime = LocalDateTime.now();
        long durationMs = java.time.Duration.between(batchStartTime, batchEndTime).toMillis();
        log.info("批次 {} 处理完成，成功: {}, 更新: {}, 失败: {}, 耗时: {}ms",
            batchNumber, result.getSuccessCount(), result.getUpdateCount(), result.getFailCount(), durationMs);

        return result;
    }

    /**
     * 批量保存题目
     */
    private void batchSaveQuestions(List<Question> questions) {
        for (int i = 0; i < questions.size(); i += 100) {
            List<Question> subBatch = questions.subList(i, Math.min(i + 100, questions.size()));
            questionRepository.saveAll(subBatch);

            // 每100条清理一次持久化上下文
            if (i % 100 == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
    }

    /**
     * 根据body.id查找现有题目
     */
    private Question findExistingQuestionByBodyId(String bodyId) {
        try {
            if (bodyId == null || bodyId.trim().isEmpty()) {
                return null;
            }

            List<Question> existingQuestions = questionRepository.findByBodyId(bodyId);

            if (!existingQuestions.isEmpty()) {
                if (existingQuestions.size() > 1) {
                    log.warn("发现多个相同body.id的题目: body.id={}, 数量={}, 将使用第一个",
                        bodyId, existingQuestions.size());
                }
                return existingQuestions.get(0);
            }

            return null;

        } catch (Exception e) {
            log.warn("检查重复题目时发生异常，将继续保存: {}", e.getMessage());
            return null;
        }
    }
}
