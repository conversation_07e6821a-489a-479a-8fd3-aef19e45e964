spring:
  application:
    name: aistrusys-backend
  main:
    allow-circular-references: true
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************************************
    username: rootay
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update # 禁用实体类自动更新数据库
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQLDialect
    open-in-view: false
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT配置
jwt:
  secret: aistrusysSecretKey123456789012345678901234567890
  expiration: 86400000  # 令牌过期时间，单位：毫秒（1天）

# 服务器配置
server:
  port: 9092
  servlet:
    context-path: /api

logging:
  level:
    root: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.transaction: DEBUG
    org.springframework.orm.jpa: DEBUG
    # 我们自己的包日志级别
    com.example.adminbackend: DEBUG
    com.example.adminbackend.service.migration: DEBUG
    com.example.adminbackend.service.migration.impl: DEBUG
    com.example.adminbackend.service.migration.HierarchyDataProcessor: DEBUG
    com.example.adminbackend.service.migration.QuestionDataConverter: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 七牛云配置
qiniu:
  access-key: "ldLSdTMCrlD-4hB95Q5r6xLVX0IQR6AELWr-rGrl"
  secret-key: "LQeQoZHEqj9FNoefKkVeDgL_29IhgtsEPI_PzT9K"
  bucket-name: "studya"
  domain: "stuvideo.studya.top"
  region: "huanan"
  upload-paths:
    image: "image"        # 图片上传路径
    video: "video"        # 视频上传路径
    audio: "audio"        # 音频上传路径


