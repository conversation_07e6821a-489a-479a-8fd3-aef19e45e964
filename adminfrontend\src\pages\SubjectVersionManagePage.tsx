import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Typography,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  LibraryBooks
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { curriculumAPI } from '../services/api';

// 类型定义
interface Subject {
  id: number;
  name: string;
  description?: string;
}

interface SubjectVersion {
  id: number;
  name: string;
  description?: string;
  schoolLevel: string;
  subjectId: number;
  subjectName: string;
  createdAt: string;
  updatedAt: string;
}

interface SubjectVersionForm {
  name: string;
  description: string;
  schoolLevel: string;
  subjectId: number;
}

const SubjectVersionManagePage: React.FC = () => {
  const { isSuperAdmin } = useAuth();
  const [subjectVersions, setSubjectVersions] = useState<SubjectVersion[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [versionToDelete, setVersionToDelete] = useState<SubjectVersion | null>(null);
  const [versionToEdit, setVersionToEdit] = useState<SubjectVersion | null>(null);

  // 表单状态
  const [form, setForm] = useState<SubjectVersionForm>({
    name: '',
    description: '',
    schoolLevel: '',
    subjectId: 0
  });

  // 年级和学期的分别状态
  const [selectedGrade, setSelectedGrade] = useState('');
  const [selectedSemester, setSelectedSemester] = useState('');

  // 年级选项
  const grades = [
    '小学一年级', '小学二年级', '小学三年级', '小学四年级', '小学五年级', '小学六年级',
    '七年级', '八年级', '九年级',
    '高一', '高二', '高三'
  ];

  // 学期选项
  const semesters = [
    { value: '上', label: '上学期' },
    { value: '下', label: '下学期' }
  ];

  // 组合年级和学期为 schoolLevel
  const combineGradeAndSemester = (grade: string, semester: string) => {
    if (grade && semester) {
      return `${grade}${semester}`;
    }
    return '';
  };

  // 解析 schoolLevel 为年级和学期
  const parseSchoolLevel = (schoolLevel: string) => {
    if (!schoolLevel) return { grade: '', semester: '' };

    // 检查是否以"上"或"下"结尾
    if (schoolLevel.endsWith('上')) {
      return {
        grade: schoolLevel.slice(0, -1),
        semester: '上'
      };
    } else if (schoolLevel.endsWith('下')) {
      return {
        grade: schoolLevel.slice(0, -1),
        semester: '下'
      };
    }

    // 如果没有学期信息，只返回年级
    return { grade: schoolLevel, semester: '' };
  };

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [versionsData, subjectsData] = await Promise.all([
        curriculumAPI.getAllSubjectVersions(),
        curriculumAPI.getSubjects()
      ]);
      setSubjectVersions(versionsData);
      setSubjects(subjectsData);
    } catch (err) {
      setError('加载数据失败');
      console.error('加载数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // 处理表单输入
  const handleInputChange = (field: keyof SubjectVersionForm, value: string | number) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理下拉选择
  const handleSelectChange = (event: SelectChangeEvent<string | number>) => {
    const { name, value } = event.target;
    handleInputChange(name as keyof SubjectVersionForm, value);
  };

  // 打开新增对话框
  const handleAdd = () => {
    setForm({
      name: '',
      description: '',
      schoolLevel: '',
      subjectId: 0
    });
    setSelectedGrade('');
    setSelectedSemester('');
    setDialogOpen(true);
  };

  // 打开编辑对话框
  const handleEdit = (version: SubjectVersion) => {
    // 解析年级和学期
    const { grade, semester } = parseSchoolLevel(version.schoolLevel);

    setVersionToEdit(version);
    setForm({
      name: version.name,
      description: version.description || '',
      schoolLevel: version.schoolLevel,
      subjectId: version.subjectId
    });
    setSelectedGrade(grade);
    setSelectedSemester(semester);
    setEditDialogOpen(true);
  };

  // 保存学科版本（用于新增和编辑）
  const handleSave = async () => {
    // 组合年级和学期
    const schoolLevel = combineGradeAndSemester(selectedGrade, selectedSemester);

    if (!form.name.trim() || !selectedGrade || !selectedSemester || !form.subjectId) {
      setError('请填写必填字段（包括年级和学期）');
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const formDataToSave = {
        ...form,
        schoolLevel: schoolLevel
      };
      await curriculumAPI.createSubjectVersion(formDataToSave);
      setSuccess('学科版本创建成功');
      setDialogOpen(false);
      await loadData();
    } catch (err) {
      setError('创建学科版本失败');
      console.error('保存学科版本失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 保存编辑的学科版本
  const handleEditSave = async () => {
    if (!versionToEdit) return;

    // 组合年级和学期
    const schoolLevel = combineGradeAndSemester(selectedGrade, selectedSemester);

    if (!form.name.trim() || !selectedGrade || !selectedSemester) {
      setError('请填写必填字段（版本名称、年级和学期）');
      return;
    }

    setLoading(true);
    setError(null);
    try {
      // 编辑时允许修改 name、description 和 schoolLevel，但不允许修改 subjectId
      const formDataToSave = {
        name: form.name,
        description: form.description, // 包含描述字段
        schoolLevel: schoolLevel,
        subjectId: versionToEdit.subjectId // 使用原有的 subjectId
      };

      await curriculumAPI.updateSubjectVersion(versionToEdit.id, formDataToSave);
      setSuccess('学科版本更新成功');
      setEditDialogOpen(false);
      setVersionToEdit(null);
      await loadData();
    } catch (err) {
      setError('更新学科版本失败');
      console.error('更新学科版本失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 确认删除
  const handleDeleteConfirm = (version: SubjectVersion) => {
    setVersionToDelete(version);
    setDeleteDialogOpen(true);
  };

  // 执行删除
  const handleDelete = async () => {
    if (!versionToDelete) return;

    setLoading(true);
    setError(null);
    try {
      const response = await curriculumAPI.deleteSubjectVersion(versionToDelete.id);
      if (response.deletedFilesCount > 0) {
        setSuccess(`学科版本删除成功，已同时删除 ${response.deletedFilesCount} 个文件`);
      } else {
        setSuccess('学科版本删除成功');
      }
      setDeleteDialogOpen(false);
      setVersionToDelete(null);
      await loadData();
    } catch (err) {
      setError('删除学科版本失败');
      console.error('删除学科版本失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 获取学科名称
  const getSubjectName = (subjectId: number) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject?.name || '未知学科';
  };

  return (
    <Layout>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LibraryBooks sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h4" fontWeight="bold">
              学科版本管理
            </Typography>
          </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
            sx={{ mr: 2 }}
            disabled={loading}
          >
            刷新
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            disabled={loading}
          >
            新增学科版本
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>版本名称</TableCell>
              <TableCell>所属学科</TableCell>
              <TableCell>年级水平</TableCell>
              <TableCell>描述</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subjectVersions.map((version) => (
              <TableRow key={version.id}>
                <TableCell>{version.id}</TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {version.name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={version.subjectName} 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip 
                    label={version.schoolLevel} 
                    size="small" 
                    color="secondary"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {version.description || '-'}
                  </Typography>
                </TableCell>
                <TableCell>
                  {new Date(version.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleEdit(version)}
                      disabled={loading}
                      color="primary"
                      title="编辑学科版本"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteConfirm(version)}
                      disabled={loading || !isSuperAdmin}
                      color="error"
                      title={!isSuperAdmin ? "只有超级管理员可以删除学科版本" : "删除学科版本"}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
            {subjectVersions.length === 0 && !loading && (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  <Typography color="text.secondary">
                    暂无学科版本数据
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 新增对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          新增学科版本
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>所属学科 *</InputLabel>
              <Select
                name="subjectId"
                value={form.subjectId || ''}
                onChange={handleSelectChange}
                label="所属学科 *"
              >
                {subjects.map((subject) => (
                  <MenuItem key={subject.id} value={subject.id}>
                    {subject.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="版本名称"
              value={form.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>年级 *</InputLabel>
              <Select
                value={selectedGrade}
                onChange={(e) => setSelectedGrade(e.target.value as string)}
                label="年级 *"
              >
                {grades.map((grade) => (
                  <MenuItem key={grade} value={grade}>
                    {grade}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>学期 *</InputLabel>
              <Select
                value={selectedSemester}
                onChange={(e) => setSelectedSemester(e.target.value as string)}
                label="学期 *"
              >
                {semesters.map((semester) => (
                  <MenuItem key={semester.value} value={semester.value}>
                    {semester.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="描述"
              value={form.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>取消</Button>
          <Button onClick={handleSave} variant="contained" disabled={loading}>
            创建
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2" fontWeight="bold">
              警告：级联删除操作
            </Typography>
          </Alert>
          <Typography>
            确定要删除学科版本 "{versionToDelete?.name}" 吗？
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            此操作将同时删除：
          </Typography>
          <Typography variant="body2" color="text.secondary" component="ul" sx={{ mt: 1, pl: 2 }}>
            <li>该版本下的所有章节</li>
            <li>所有章节下的知识点</li>
            <li>知识点关联的视频和图片文件</li>
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 1, fontWeight: 'bold' }}>
            此操作不可撤销，请谨慎操作！
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button onClick={handleDelete} color="error" disabled={loading}>
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          编辑学科版本
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {/* 所属学科（只读） */}
            <FormControl fullWidth sx={{ mb: 2 }} disabled>
              <InputLabel>所属学科</InputLabel>
              <Select
                value={form.subjectId || ''}
                label="所属学科"
              >
                {subjects.map((subject) => (
                  <MenuItem key={subject.id} value={subject.id}>
                    {subject.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* 版本名称 */}
            <TextField
              fullWidth
              label="版本名称"
              value={form.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            {/* 年级 */}
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>年级 *</InputLabel>
              <Select
                value={selectedGrade}
                onChange={(e) => setSelectedGrade(e.target.value as string)}
                label="年级 *"
              >
                {grades.map((grade) => (
                  <MenuItem key={grade} value={grade}>
                    {grade}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* 学期 */}
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>学期 *</InputLabel>
              <Select
                value={selectedSemester}
                onChange={(e) => setSelectedSemester(e.target.value as string)}
                label="学期 *"
              >
                {semesters.map((semester) => (
                  <MenuItem key={semester.value} value={semester.value}>
                    {semester.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* 描述 */}
            <TextField
              fullWidth
              label="描述"
              value={form.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>取消</Button>
          <Button onClick={handleEditSave} variant="contained" disabled={loading}>
            保存
          </Button>
        </DialogActions>
      </Dialog>
      </Box>
    </Layout>
  );
};

export default SubjectVersionManagePage;
