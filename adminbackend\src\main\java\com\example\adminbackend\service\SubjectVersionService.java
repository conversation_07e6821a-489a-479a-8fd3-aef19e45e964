package com.example.adminbackend.service;

import com.example.adminbackend.model.SubjectVersion;
import com.example.adminbackend.dto.SubjectVersionDTO;

import java.util.List;

/**
 * 科目版本服务接口
 */
public interface SubjectVersionService {

    /**
     * 获取所有科目版本
     */
    List<SubjectVersion> getAllSubjectVersions();

    /**
     * 根据ID获取科目版本
     */
    SubjectVersion getSubjectVersionById(Long id);

    /**
     * 根据科目ID获取所有版本
     */
    List<SubjectVersion> getVersionsBySubjectId(Long subjectId);

    /**
     * 根据科目ID和版本名称获取版本
     */
    SubjectVersion getVersionBySubjectIdAndName(Long subjectId, String name);

    /**
     * 根据学校级别获取版本
     */
    List<SubjectVersion> getVersionsBySchoolLevel(String schoolLevel);

    /**
     * 根据科目ID和学校级别获取版本
     */
    List<SubjectVersion> getVersionsBySubjectIdAndSchoolLevel(Long subjectId, String schoolLevel);

    /**
     * 创建科目版本
     */
    SubjectVersion createSubjectVersion(SubjectVersion subjectVersion);

    /**
     * 更新科目版本
     */
    SubjectVersion updateSubjectVersion(Long id, SubjectVersion subjectVersion);

    /**
     * 删除科目版本
     * @param id 科目版本ID
     * @return 删除的文件数量
     */
    int deleteSubjectVersion(Long id);

    /**
     * 检查科目下是否存在指定名称的版本
     */
    boolean existsBySubjectIdAndName(Long subjectId, String name);

    /**
     * 获取有章节的科目版本列表
     */
    List<SubjectVersion> getSubjectVersionsWithChapters();

    /**
     * 根据科目名称获取版本
     */
    List<SubjectVersion> getVersionsBySubjectName(String subjectName);

    /**
     * 获取有章节的科目版本列表，返回DTO（避免懒加载问题）
     */
    List<SubjectVersionDTO> getSubjectVersionsWithChaptersAsDTO();

    /**
     * 根据科目ID获取所有版本，返回DTO（避免懒加载问题）
     */
    List<SubjectVersionDTO> getVersionsBySubjectIdAsDTO(Long subjectId);

    /**
     * 根据ID获取科目版本，返回DTO（避免懒加载问题）
     */
    SubjectVersionDTO getSubjectVersionByIdAsDTO(Long id);

    /**
     * 获取所有科目版本，返回DTO（避免懒加载问题）
     */
    List<SubjectVersionDTO> getAllSubjectVersionsAsDTO();
}
