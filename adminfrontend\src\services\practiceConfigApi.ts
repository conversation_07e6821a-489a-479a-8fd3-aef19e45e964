import axios from 'axios';

// API基础URL
const API_BASE_URL = 'http://localhost:9092';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理通用错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error.response?.data || error);
  }
);

// 练习配置类型定义
export interface PracticeConfig {
  id?: number;
  configType: 'KNOWLEDGE_POINT_PRACTICE' | 'CHAPTER_TEST';
  scopeType: 'GLOBAL' | 'SUBJECT' | 'CHAPTER' | 'KNOWLEDGE_POINT';
  targetId?: number;
  questionCount: number;
  selectionStrategy: 'RANDOM' | 'DIFFICULTY_BALANCED' | 'ERROR_PRIORITY' | 'TYPE_BALANCED';
  enabled: boolean;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

// 分页响应类型
export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// API响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 练习配置API服务
export class PracticeConfigAPI {
  
  // ==================== 基础CRUD操作 ====================

  /**
   * 创建练习配置
   */
  static async createConfig(config: Omit<PracticeConfig, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>): Promise<PracticeConfig> {
    const response = await api.post<ApiResponse<PracticeConfig>>('/api/practice-configs', config);
    return response.data.data;
  }

  /**
   * 更新练习配置
   */
  static async updateConfig(configId: number, config: Partial<PracticeConfig>): Promise<PracticeConfig> {
    const response = await api.put<ApiResponse<PracticeConfig>>(`/api/practice-configs/${configId}`, config);
    return response.data.data;
  }

  /**
   * 获取配置详情
   */
  static async getConfig(configId: number): Promise<PracticeConfig> {
    const response = await api.get<ApiResponse<PracticeConfig>>(`/api/practice-configs/${configId}`);
    return response.data.data;
  }

  /**
   * 删除配置
   */
  static async deleteConfig(configId: number): Promise<void> {
    await api.delete(`/api/practice-configs/${configId}`);
  }

  /**
   * 启用/禁用配置
   */
  static async toggleConfig(configId: number, enabled: boolean): Promise<PracticeConfig> {
    const response = await api.patch<ApiResponse<PracticeConfig>>(`/api/practice-configs/${configId}/toggle`, null, {
      params: { enabled }
    });
    return response.data.data;
  }

  // ==================== 查询操作 ====================

  /**
   * 分页查询配置
   */
  static async getConfigs(params: {
    page?: number;
    size?: number;
    configType?: string;
    scopeType?: string;
  } = {}): Promise<PageResponse<PracticeConfig>> {
    const response = await api.get<ApiResponse<PageResponse<PracticeConfig>>>('/api/practice-configs', { params });
    return response.data.data;
  }

  /**
   * 获取所有启用的配置
   */
  static async getAllEnabledConfigs(configType?: string): Promise<PracticeConfig[]> {
    const response = await api.get<ApiResponse<PracticeConfig[]>>('/api/practice-configs/enabled', {
      params: configType ? { configType } : {}
    });
    return response.data.data;
  }

  // ==================== 快捷配置操作 ====================

  /**
   * 创建或更新全局配置
   */
  static async createOrUpdateGlobalConfig(
    configType: string,
    questionCount: number,
    strategy: string = 'RANDOM'
  ): Promise<PracticeConfig> {
    const response = await api.post<ApiResponse<PracticeConfig>>('/api/practice-configs/global', null, {
      params: { configType, questionCount, strategy }
    });
    return response.data.data;
  }

  /**
   * 创建或更新科目配置
   */
  static async createOrUpdateSubjectConfig(
    subjectId: number,
    configType: string,
    questionCount: number,
    strategy: string = 'RANDOM'
  ): Promise<PracticeConfig> {
    const response = await api.post<ApiResponse<PracticeConfig>>(`/api/practice-configs/subject/${subjectId}`, null, {
      params: { configType, questionCount, strategy }
    });
    return response.data.data;
  }

  /**
   * 创建或更新章节配置
   */
  static async createOrUpdateChapterConfig(
    chapterId: number,
    configType: string,
    questionCount: number,
    strategy: string = 'RANDOM'
  ): Promise<PracticeConfig> {
    const response = await api.post<ApiResponse<PracticeConfig>>(`/api/practice-configs/chapter/${chapterId}`, null, {
      params: { configType, questionCount, strategy }
    });
    return response.data.data;
  }

  /**
   * 创建或更新知识点配置
   */
  static async createOrUpdateKnowledgePointConfig(
    knowledgePointId: number,
    configType: string,
    questionCount: number,
    strategy: string = 'RANDOM'
  ): Promise<PracticeConfig> {
    const response = await api.post<ApiResponse<PracticeConfig>>(`/api/practice-configs/knowledge-point/${knowledgePointId}`, null, {
      params: { configType, questionCount, strategy }
    });
    return response.data.data;
  }

  // ==================== 统计和分析 ====================

  /**
   * 获取配置统计信息
   */
  static async getConfigStatistics(): Promise<any> {
    const response = await api.get<ApiResponse<any>>('/api/practice-configs/statistics');
    return response.data.data;
  }

  /**
   * 根据目标ID获取配置
   */
  static async getConfigsByTargetId(targetId: number): Promise<PracticeConfig[]> {
    const response = await api.get<ApiResponse<PracticeConfig[]>>(`/api/practice-configs/target/${targetId}`);
    return response.data.data;
  }

  // ==================== 批量操作 ====================

  /**
   * 批量创建配置
   */
  static async createConfigs(configs: Omit<PracticeConfig, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>[]): Promise<PracticeConfig[]> {
    const response = await api.post<ApiResponse<PracticeConfig[]>>('/api/practice-configs/batch', configs);
    return response.data.data;
  }

  /**
   * 批量删除配置
   */
  static async deleteConfigs(configIds: number[]): Promise<void> {
    await api.delete('/api/practice-configs/batch', { data: configIds });
  }

  /**
   * 批量启用/禁用配置
   */
  static async toggleConfigs(configIds: number[], enabled: boolean): Promise<void> {
    await api.patch('/api/practice-configs/batch/toggle', configIds, {
      params: { enabled }
    });
  }
}

export default PracticeConfigAPI;
