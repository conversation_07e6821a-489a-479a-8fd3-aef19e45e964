package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.Video;
import com.example.adminbackend.repository.VideoRepository;
import com.example.adminbackend.service.VideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 视频服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VideoServiceImpl implements VideoService {

    private final VideoRepository videoRepository;

    @Override
    public List<Video> getAllVideos() {
        log.info("获取所有视频");
        return videoRepository.findAllByOrderByCreatedAtDesc();
    }

    @Override
    public Video getVideoById(Long id) {
        log.info("根据ID获取视频: {}", id);
        return videoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("视频不存在: " + id));
    }

    @Override
    public Video getVideoByUrl(String videoUrl) {
        log.info("根据URL获取视频: {}", videoUrl);
        return videoRepository.findByVideoUrl(videoUrl)
                .orElseThrow(() -> new RuntimeException("视频不存在: " + videoUrl));
    }

    @Override
    public Video getVideoByTitle(String title) {
        log.info("根据标题获取视频: {}", title);
        return videoRepository.findByTitle(title)
                .orElseThrow(() -> new RuntimeException("视频不存在: " + title));
    }

    @Override
    @Transactional
    public Video createVideo(Video video) {
        log.info("创建视频: {}", video.getTitle());
        
        // 检查视频URL是否已存在
        if (videoRepository.existsByVideoUrl(video.getVideoUrl())) {
            throw new RuntimeException("视频URL已存在: " + video.getVideoUrl());
        }
        
        // 检查标题是否已存在
        if (videoRepository.existsByTitle(video.getTitle())) {
            throw new RuntimeException("视频标题已存在: " + video.getTitle());
        }
        
        return videoRepository.save(video);
    }

    @Override
    @Transactional
    public Video updateVideo(Long id, Video video) {
        log.info("更新视频: {}", id);
        
        Video existing = getVideoById(id);
        
        // 检查视频URL是否与其他视频冲突
        if (!existing.getVideoUrl().equals(video.getVideoUrl()) && 
            videoRepository.existsByVideoUrl(video.getVideoUrl())) {
            throw new RuntimeException("视频URL已存在: " + video.getVideoUrl());
        }
        
        // 检查标题是否与其他视频冲突
        if (!existing.getTitle().equals(video.getTitle()) && 
            videoRepository.existsByTitle(video.getTitle())) {
            throw new RuntimeException("视频标题已存在: " + video.getTitle());
        }
        
        existing.setTitle(video.getTitle());
        existing.setDescription(video.getDescription());
        existing.setVideoUrl(video.getVideoUrl());
        existing.setCoverImageUrl(video.getCoverImageUrl());
        existing.setDurationSeconds(video.getDurationSeconds());
        existing.setUpdatedAt(new Date());
        
        return videoRepository.save(existing);
    }

    @Override
    @Transactional
    public void deleteVideo(Long id) {
        log.info("删除视频: {}", id);
        
        Video video = getVideoById(id);
        videoRepository.delete(video);
    }

    @Override
    public boolean existsByVideoUrl(String videoUrl) {
        return videoRepository.existsByVideoUrl(videoUrl);
    }

    @Override
    public boolean existsByTitle(String title) {
        return videoRepository.existsByTitle(title);
    }

    @Override
    public List<Video> searchVideos(String keyword) {
        log.info("搜索视频: {}", keyword);
        return videoRepository.findByKeyword(keyword);
    }

    @Override
    @Transactional
    public Video findOrCreateVideoByUrl(String videoUrl) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("视频URL不能为空");
        }

        log.info("根据URL查找或创建视频: {}", videoUrl);

        // 先查找是否已存在
        return videoRepository.findByVideoUrl(videoUrl)
            .orElseGet(() -> {
                // 创建新视频
                String title = extractTitleFromUrl(videoUrl);

                Video newVideo = Video.builder()
                    .title(title)
                    .description("从知识点视频字段自动创建")
                    .videoUrl(videoUrl)
                    .coverImageUrl(null)
                    .durationSeconds(null)
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();

                Video savedVideo = videoRepository.save(newVideo);
                log.info("创建新视频: {} - {}", savedVideo.getTitle(), savedVideo.getVideoUrl());
                return savedVideo;
            });
    }

    @Override
    @Transactional
    public Video findOrCreateVideoByUrlAndTitle(String videoUrl, String title) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return null;
        }

        log.info("根据视频URL和标题查找或创建视频: URL={}, 标题={}", videoUrl, title);

        // 先查询是否已存在
        return videoRepository.findByVideoUrl(videoUrl)
            .orElseGet(() -> {
                // 创建新视频，使用提供的标题加时间戳
                String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                String videoTitle = title + "_" + timestamp;

                Video newVideo = Video.builder()
                    .title(videoTitle)
                    .description("知识点视频: " + title)
                    .videoUrl(videoUrl)
                    .coverImageUrl(null)
                    .durationSeconds(null)
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();

                Video savedVideo = videoRepository.save(newVideo);
                log.info("创建新视频: {} - {}", savedVideo.getTitle(), savedVideo.getVideoUrl());
                return savedVideo;
            });
    }
    
    /**
     * 从URL中提取标题
     */
    private String extractTitleFromUrl(String videoUrl) {
        try {
            // 简单的标题提取逻辑，可以根据需要改进
            if (videoUrl.contains("/")) {
                String fileName = videoUrl.substring(videoUrl.lastIndexOf("/") + 1);
                if (fileName.contains(".")) {
                    fileName = fileName.substring(0, fileName.lastIndexOf("."));
                }
                return "视频-" + fileName;
            } else {
                return "视频-" + System.currentTimeMillis();
            }
        } catch (Exception e) {
            return "视频-" + System.currentTimeMillis();
        }
    }
}
