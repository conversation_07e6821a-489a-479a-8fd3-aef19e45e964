import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  FormControlLabel,
  Radio,
  RadioGroup,
  Checkbox,
  FormGroup,
  TextField,
} from '@mui/material';
import { Question, QuestionType, SubjectEnum, Difficulty, SubQuestion } from '../types';
import UnifiedRenderer from './UnifiedRenderer';

interface QuestionRendererProps {
  question: Question;
  showAnswer?: boolean;
  showExplanation?: boolean;
  interactive?: boolean;
  onAnswerChange?: (answer: any) => void;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  showAnswer = false,
  showExplanation = false,
  interactive = false,
  onAnswerChange,
}) => {
  const [userAnswer, setUserAnswer] = React.useState<any>('');

  // 调试输出题目信息
  React.useEffect(() => {
    console.log('QuestionRenderer 题目渲染调试:', {
      题目ID: question.id,
      题目类型: question.body.type,
      学科: question.body.subject,
      题目内容预览: question.body.content?.substring(0, 100) + '...',
      选项数量: question.body.options?.length || 0,
      答案: question.body.answer,
      是否显示答案: showAnswer,
      是否显示解析: showExplanation,
      是否交互模式: interactive
    });
  }, [question, showAnswer, showExplanation, interactive]);

  // 处理答案变化
  const handleAnswerChange = (answer: any) => {
    setUserAnswer(answer);
    onAnswerChange?.(answer);
  };

  // 获取题型标签
  const getQuestionTypeLabel = (type: QuestionType): string => {
    const labels: Record<QuestionType, string> = {
      SINGLE_CHOICE: '单选题',
      MULTIPLE_CHOICE: '多选题',
      FILL_IN_BLANK: '填空题',
      TRUE_FALSE: '判断题',
      MATCHING: '匹配题',
      READING_COMPREHENSION: '阅读理解',
      CLOZE_TEST: '完形填空',
      LISTENING: '听力题',
    };
    return labels[type] || type;
  };

  // 获取科目标签
  const getSubjectLabel = (subject: SubjectEnum): string => {
    const labels: Record<SubjectEnum, string> = {
      ENGLISH: '英语',
      MATH: '数学',
      PHYSICS: '物理',
      CHEMISTRY: '化学',
    };
    return labels[subject] || subject;
  };

  // 获取难度标签和颜色
  const getDifficultyInfo = (difficulty: Difficulty) => {
    const info: Record<Difficulty, { label: string; color: 'success' | 'warning' | 'error' }> = {
      EASY: { label: '简单', color: 'success' },
      MEDIUM: { label: '中等', color: 'warning' },
      HARD: { label: '困难', color: 'error' },
    };
    return info[difficulty] || { label: difficulty, color: 'warning' };
  };

  // 判断是否为嵌套题型
  const isNestedQuestion = (type: QuestionType): boolean => {
    return ['READING_COMPREHENSION', 'LISTENING', 'CLOZE_TEST'].includes(type);
  };

  // 渲染HTML内容（统一使用MathJax处理所有内容）
  const renderHtmlContent = (content: string) => {
    // 生产环境可以移除此调试输出
    if (process.env.NODE_ENV === 'development') {
      console.log('QuestionRenderer HTML内容渲染:', {
        内容长度: content.length,
        渲染策略: '统一MathJax渲染'
      });
    }

    // 统一使用UnifiedRenderer处理所有内容
    // 自动识别和渲染HTML、LaTeX公式、化学公式和普通文本
    return (
      <UnifiedRenderer
        content={content}
        style={{
          lineHeight: 1.6,
          fontSize: '14px',
        }}
        onError={(error) => {
          console.error('QuestionRenderer 渲染错误:', error);
        }}
      />
    );
  };

  // 解析答案（处理JSON格式的答案）
  const parseAnswer = (answer: any) => {
    if (Array.isArray(answer)) {
      return answer;
    }

    if (typeof answer === 'string') {
      // 尝试解析JSON格式的答案
      try {
        const parsed = JSON.parse(answer);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } catch (e) {
        // 不是JSON格式，返回原值
      }
    }

    return answer;
  };

  // 渲染答案内容（统一使用MathJax渲染器处理所有内容）
  const renderAnswerContent = (answer: any) => {
    const answerText = String(answer);
    if (!answerText) return <span>-</span>;

    // 生产环境可以移除此调试输出
    if (process.env.NODE_ENV === 'development') {
      console.log('QuestionRenderer 答案渲染:', answerText.substring(0, 50));
    }

    // 统一使用UnifiedRenderer处理所有内容 - 简化策略
    // 自动识别和处理HTML、LaTeX、化学公式和普通文本的混合内容
    try {
      return (
        <UnifiedRenderer
          content={answerText}
          inline={true}
          style={{ fontSize: '14px' }}
          onError={(error) => {
            console.warn('答案渲染失败，降级为纯文本显示:', error);
          }}
        />
      );
    } catch (error) {
      console.warn('答案渲染异常，降级为纯文本显示:', error);
      return <span>{answerText}</span>;
    }
  };

  // 渲染单选题
  const renderSingleChoice = () => (
    <Box>
      {renderHtmlContent(question.body.content)}
      
      {question.body.options && (
        <RadioGroup
          value={interactive ? userAnswer : ''}
          onChange={(e) => interactive && handleAnswerChange(e.target.value)}
          sx={{ mt: 2 }}
        >
          {question.body.options.map((option, index) => (
            <FormControlLabel
              key={index}
              value={String.fromCharCode(65 + index)}
              control={<Radio size="small" />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" component="span">
                    {String.fromCharCode(65 + index)}.
                  </Typography>
                  <div>{renderHtmlContent(option)}</div>
                  {showAnswer && question.body.answer === String.fromCharCode(65 + index) && (
                    <Chip label="正确答案" size="small" color="success" />
                  )}
                </Box>
              }
              sx={{ alignItems: 'flex-start', mb: 1 }}
            />
          ))}
        </RadioGroup>
      )}
    </Box>
  );

  // 渲染多选题
  const renderMultipleChoice = () => (
    <Box>
      {renderHtmlContent(question.body.content)}
      
      {question.body.options && (
        <FormGroup sx={{ mt: 2 }}>
          {question.body.options.map((option, index) => {
            const optionKey = String.fromCharCode(65 + index);
            const parsedAnswer = parseAnswer(question.body.answer);
            const isCorrect = Array.isArray(parsedAnswer) &&
                             parsedAnswer.includes(optionKey);
            
            return (
              <FormControlLabel
                key={index}
                control={
                  <Checkbox
                    size="small"
                    checked={interactive ? (userAnswer || []).includes(optionKey) : false}
                    onChange={(e) => {
                      if (interactive) {
                        const currentAnswers = userAnswer || [];
                        const newAnswers = e.target.checked
                          ? [...currentAnswers, optionKey]
                          : currentAnswers.filter((a: string) => a !== optionKey);
                        handleAnswerChange(newAnswers);
                      }
                    }}
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" component="span">
                      {optionKey}.
                    </Typography>
                    <div>{renderHtmlContent(option)}</div>
                    {showAnswer && isCorrect && (
                      <Chip label="正确答案" size="small" color="success" />
                    )}
                  </Box>
                }
                sx={{ alignItems: 'flex-start', mb: 1 }}
              />
            );
          })}
        </FormGroup>
      )}
    </Box>
  );

  // 渲染填空题
  const renderFillInBlank = () => (
    <Box>
      {renderHtmlContent(question.body.content)}
      
      {interactive && (
        <TextField
          fullWidth
          multiline
          rows={2}
          placeholder="请输入答案"
          value={userAnswer}
          onChange={(e) => handleAnswerChange(e.target.value)}
          sx={{ mt: 2 }}
        />
      )}
      
      {showAnswer && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="success.main" fontWeight="bold">
            参考答案：
          </Typography>
          <Box>
            {(() => {
              const parsedAnswer = parseAnswer(question.body.answer);
              return Array.isArray(parsedAnswer)
                ? parsedAnswer.map((ans, idx) => (
                    <span key={idx}>
                      {renderAnswerContent(ans)}
                      {idx < parsedAnswer.length - 1 && '、'}
                    </span>
                  ))
                : renderAnswerContent(parsedAnswer);
            })()}
          </Box>
        </Box>
      )}
    </Box>
  );

  // 渲染判断题
  const renderTrueFalse = () => (
    <Box>
      {renderHtmlContent(question.body.content)}
      
      <RadioGroup
        value={interactive ? userAnswer : ''}
        onChange={(e) => interactive && handleAnswerChange(e.target.value === 'true')}
        sx={{ mt: 2 }}
      >
        <FormControlLabel
          value="true"
          control={<Radio size="small" />}
          label={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2">正确</Typography>
              {showAnswer && question.body.answer === true && (
                <Chip label="正确答案" size="small" color="success" />
              )}
            </Box>
          }
        />
        <FormControlLabel
          value="false"
          control={<Radio size="small" />}
          label={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2">错误</Typography>
              {showAnswer && question.body.answer === false && (
                <Chip label="正确答案" size="small" color="success" />
              )}
            </Box>
          }
        />
      </RadioGroup>
    </Box>
  );

  // 渲染匹配题
  const renderMatching = () => (
    <Box>
      {renderHtmlContent(question.body.content)}
      
      {interactive && (
        <TextField
          fullWidth
          multiline
          rows={3}
          placeholder="请输入匹配答案"
          value={userAnswer}
          onChange={(e) => handleAnswerChange(e.target.value)}
          sx={{ mt: 2 }}
        />
      )}
      
      {showAnswer && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="success.main" fontWeight="bold">
            参考答案：
          </Typography>
          <Box>
            {typeof question.body.answer === 'object'
              ? JSON.stringify(question.body.answer, null, 2)
              : renderAnswerContent(question.body.answer)}
          </Box>
        </Box>
      )}
    </Box>
  );

  // 渲染子题目
  const renderSubQuestion = (subQ: SubQuestion, index: number) => (
    <Paper key={subQ.id} sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
      <Typography variant="subtitle2" gutterBottom>
        {index + 1}. {subQ.content ? renderHtmlContent(subQ.content) : '[题目内容为空]'}
      </Typography>
      
      {subQ.options && (
        <RadioGroup sx={{ mt: 1 }}>
          {subQ.options.map((option, optIndex) => (
            <FormControlLabel
              key={optIndex}
              value={String.fromCharCode(65 + optIndex)}
              control={<Radio size="small" disabled={!interactive} />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" component="span">
                    {String.fromCharCode(65 + optIndex)}.
                  </Typography>
                  <div>{renderHtmlContent(option)}</div>
                  {showAnswer && subQ.answer === String.fromCharCode(65 + optIndex) && (
                    <Chip label="正确答案" size="small" color="success" />
                  )}
                </Box>
              }
              sx={{ alignItems: 'flex-start', mb: 0.5 }}
            />
          ))}
        </RadioGroup>
      )}
      
      {showExplanation && subQ.explanation && (
        <Box sx={{ mt: 2, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="body2" color="info.contrastText" component="div">
            <strong>解析：</strong>
            {renderHtmlContent(subQ.explanation)}
          </Typography>
        </Box>
      )}
    </Paper>
  );

  // 渲染嵌套题型
  const renderNestedQuestion = () => (
    <Box>
      {/* 材料内容 */}
      {question.body.material && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.100' }}>
          <Typography variant="subtitle1" gutterBottom fontWeight="bold">
            材料：
          </Typography>
          {renderHtmlContent(question.body.material)}
        </Paper>
      )}
      
      {/* 题目说明 */}
      {question.body.content && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom fontWeight="bold">
            题目要求：
          </Typography>
          {renderHtmlContent(question.body.content)}
        </Box>
      )}
      
      {/* 子题目 */}
      {question.body.subQuestions && question.body.subQuestions.length > 0 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom fontWeight="bold">
            题目：
          </Typography>
          {question.body.subQuestions.map((subQ, index) => 
            renderSubQuestion(subQ, index)
          )}
        </Box>
      )}
    </Box>
  );

  const difficultyInfo = getDifficultyInfo(question.body.difficulty);

  return (
    <Box>
      {/* 题目信息头部 */}
      <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
        <Chip
          label={getQuestionTypeLabel(question.body.type)}
          variant="outlined"
          size="small"
        />
        <Chip 
          label={getSubjectLabel(question.body.subject)} 
          color="primary" 
          variant="outlined" 
          size="small" 
        />
        <Chip 
          label={difficultyInfo.label} 
          color={difficultyInfo.color} 
          variant="outlined" 
          size="small" 
        />
        {question.body.tags && question.body.tags.map((tag, index) => (
          <Chip 
            key={index} 
            label={tag} 
            variant="outlined" 
            size="small" 
            sx={{ bgcolor: 'grey.100' }}
          />
        ))}
      </Box>

      {/* 题目内容 */}
      <Paper sx={{ p: 3, mb: 2 }}>
        {isNestedQuestion(question.body.type) ? (
          renderNestedQuestion()
        ) : (
          <>
            {question.body.type === 'SINGLE_CHOICE' && renderSingleChoice()}
            {question.body.type === 'MULTIPLE_CHOICE' && renderMultipleChoice()}
            {question.body.type === 'FILL_IN_BLANK' && renderFillInBlank()}
            {question.body.type === 'TRUE_FALSE' && renderTrueFalse()}
            {question.body.type === 'MATCHING' && renderMatching()}
          </>
        )}
      </Paper>

      {/* 答案显示 */}
      {showAnswer && !isNestedQuestion(question.body.type) && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'success.light' }}>
          <Typography variant="body2" color="success.contrastText" fontWeight="bold" component="div">
            正确答案：
            <Box component="span" sx={{ ml: 1 }}>
              {(() => {
                const parsedAnswer = parseAnswer(question.body.answer);
                return Array.isArray(parsedAnswer)
                  ? parsedAnswer.map((ans, idx) => (
                      <span key={idx}>
                        {renderAnswerContent(ans)}
                        {idx < parsedAnswer.length - 1 && ', '}
                      </span>
                    ))
                  : renderAnswerContent(parsedAnswer);
              })()}
            </Box>
          </Typography>
        </Paper>
      )}

      {/* 解析显示 */}
      {showExplanation && question.body.explanation && (
        <Paper sx={{ p: 2, bgcolor: 'info.light' }}>
          <Typography variant="body2" color="info.contrastText" component="div">
            <strong>解析：</strong>
            {renderHtmlContent(question.body.explanation)}
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default QuestionRenderer;
