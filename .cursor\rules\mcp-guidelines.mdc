---
description: 
globs: 
alwaysApply: true
---
# MCP开发指南

本文档定义了项目中两个MCP (Model Context Protocol) 的使用规则。这些规则必须在整个开发过程中严格遵循。

## Sequential Thinking MCP (序列性思考 MCP)

Sequential Thinking MCP用于规划开发步骤，确保执行过程彻底而有条理。

### 使用要求：

- 在实现任何功能前，必须先使用Sequential Thinking进行详细的步骤规划
- 每个开发任务必须分解为清晰的步骤，并按顺序执行
- 确保每个步骤都经过验证后再进行下一步
- 开发过程中出现问题时，应返回到Sequential Thinking进行重新评估

## Context7 MCP (上下文文档 MCP)

Context7 MCP确保使用最新的官方文档进行开发。

### 使用要求：

- 在引入任何新的库、框架或API前，**必须**首先查阅其最新官方文档
- 这一点极其重要，绝对不可以忽视
- 无论何时，都必须始终查阅最新文档，因为自训练以来，某些内容可能已经发生了变化
- 在实施方案前，必须基于最新文档验证方案的正确性

## MCP协作流程

1. 先用Context7获取最新文档和API信息
2. 再用Sequential Thinking规划实施步骤
3. Sequential Thinking确保编码路径正确
4. Context7确保使用的框架和API是最新的

## 项目结构参考

主项目文件在[README.md](mdc:README.md)中有详细说明，包括项目结构和MCP使用规则。

应用主模块位于[app/](mdc:app)目录，包含所有源代码和资源。

## 遵循这些规则的好处

- 确保开发基于最新文档和API
- 提供清晰的开发路径和步骤
- 减少因信息过时导致的错误
- 提高代码质量和可维护性

