import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  CircularProgress,
} from '@mui/material';
import {
  People,
  AdminPanelSettings,
  SupervisorAccount,
  TrendingUp,
  Business,
  Store,
  Quiz,
  CheckCircle,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { userAPI, questionAPI } from '../services/api';
import { User, QuestionStatistics } from '../types';
import { getRoleLabel } from '../utils/roleUtils';

interface DashboardStats {
  totalUsers: number;
  adminCount: number;
  dealerCount: number;
  agentCount: number;
  supervisorCount: number;
  activeUsers: number;
  questionStats?: QuestionStatistics;
}

const DashboardPage: React.FC = () => {
  const { user, isSuperAdmin, isAdmin } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  // 获取仪表板统计数据
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        // 获取用户统计数据
        const users = await userAPI.getAllUsers(isSuperAdmin); // 获取所有用户

        const totalUsers = users.length;
        const adminCount = users.filter((u: User) => u.role === 'ADMIN' || u.role === 'SUPER_ADMIN').length;
        const dealerCount = users.filter((u: User) => u.role === 'DEALER').length;
        const agentCount = users.filter((u: User) => u.role === 'AGENT').length;
        const supervisorCount = users.filter((u: User) => u.role === 'SUPERVISOR').length;
        const activeUsers = users.filter((u: User) => u.enabled).length;

        // 获取题库统计数据（仅管理员及以上可见）
        let questionStats: QuestionStatistics | undefined;
        if (isAdmin || isSuperAdmin) {
          try {
            questionStats = await questionAPI.getQuestionStatistics();
          } catch (error) {
            console.error('获取题库统计失败:', error);
          }
        }

        setStats({
          totalUsers,
          adminCount,
          dealerCount,
          agentCount,
          supervisorCount,
          activeUsers,
          questionStats
        });
      } catch (error) {
        console.error('获取仪表板数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, [isSuperAdmin, isAdmin]);

  const getStatsCards = () => {
    if (!stats) return [];

    const cards = [
      {
        title: '总用户数',
        value: stats.totalUsers.toString(),
        icon: <People />,
        color: '#1976d2',
        change: `${stats.activeUsers}个启用`,
      },
      {
        title: '管理员',
        value: stats.adminCount.toString(),
        icon: <AdminPanelSettings />,
        color: '#388e3c',
        change: '系统管理',
      },
      {
        title: '经销商',
        value: stats.dealerCount.toString(),
        icon: <Business />,
        color: '#0288d1',
        change: '经销业务',
      },
      {
        title: '代理商',
        value: stats.agentCount.toString(),
        icon: <Store />,
        color: '#f57c00',
        change: '代理业务',
      },
      {
        title: '督学',
        value: stats.supervisorCount.toString(),
        icon: <SupervisorAccount />,
        color: '#d32f2f',
        change: '业务督导',
      },
      {
        title: '活跃用户',
        value: stats.activeUsers.toString(),
        icon: <TrendingUp />,
        color: '#7b1fa2',
        change: `${Math.round((stats.activeUsers / stats.totalUsers) * 100)}%`,
      },
    ];

    // 添加题库统计卡片（仅管理员及以上可见）
    if ((isAdmin || isSuperAdmin) && stats.questionStats) {
      cards.push(
        {
          title: '题目总数',
          value: stats.questionStats.total.toString(),
          icon: <Quiz />,
          color: '#9c27b0',
          change: `${stats.questionStats.enabled}个启用`,
        },
        {
          title: '启用题目',
          value: stats.questionStats.enabled.toString(),
          icon: <CheckCircle />,
          color: '#4caf50',
          change: `${Math.round((stats.questionStats.enabled / stats.questionStats.total) * 100)}%`,
        }
      );
    }

    return cards;
  };

  return (
    <Layout>
      <Box>
        {/* 欢迎信息 */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Typography variant="h4" gutterBottom fontWeight="bold">
            欢迎回来，{user?.fullName || user?.username}！
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {user?.role ? getRoleLabel(user.role) : '未知角色'} |
            今天是 {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long'
            })}
          </Typography>
        </Paper>

        {/* 统计卡片 */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {loading ? (
            <Grid size={{ xs: 12 }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            </Grid>
          ) : (
            getStatsCards().map((stat, index) => (
              <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                <Card sx={{ borderRadius: 2, height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: stat.color, mr: 2 }}>
                        {stat.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h4" fontWeight="bold">
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {stat.title}
                        </Typography>
                      </Box>
                    </Box>
                    <Typography
                      variant="body2"
                      sx={{
                        color: 'text.secondary',
                        fontWeight: 'medium'
                      }}
                    >
                      {stat.change}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))
          )}
        </Grid>

        {/* 快速操作 */}
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                快速操作
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  • 查看用户列表
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • 添加新用户
                </Typography>
                {(isAdmin || isSuperAdmin) && (
                  <>
                    <Typography variant="body2" color="text.secondary">
                      • 题库管理
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • 导入题目
                    </Typography>
                  </>
                )}
                <Typography variant="body2" color="text.secondary">
                  • 系统设置
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • 数据导出
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                系统状态
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2" color="success.main">
                  ✓ 数据库连接正常
                </Typography>
                <Typography variant="body2" color="success.main">
                  ✓ API服务运行正常
                </Typography>
                <Typography variant="body2" color="success.main">
                  ✓ 缓存服务正常
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  最后更新: {new Date().toLocaleTimeString('zh-CN')}
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Layout>
  );
};

export default DashboardPage;
