package com.example.adminbackend.repository;

import com.example.adminbackend.model.PracticeConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 练习配置数据访问层
 */
@Repository
public interface PracticeConfigRepository extends JpaRepository<PracticeConfig, Long> {

    /**
     * 根据配置类型和适用范围查找配置
     */
    Optional<PracticeConfig> findByConfigTypeAndScopeTypeAndTargetIdAndEnabledTrue(
            PracticeConfig.ConfigType configType, 
            PracticeConfig.ScopeType scopeType, 
            Long targetId);

    /**
     * 根据配置类型查找全局配置
     */
    Optional<PracticeConfig> findByConfigTypeAndScopeTypeAndEnabledTrue(
            PracticeConfig.ConfigType configType, 
            PracticeConfig.ScopeType scopeType);

    /**
     * 根据配置类型和适用范围查找所有配置
     */
    List<PracticeConfig> findByConfigTypeAndScopeTypeAndEnabledTrueOrderByCreatedAtDesc(
            PracticeConfig.ConfigType configType,
            PracticeConfig.ScopeType scopeType);

    /**
     * 根据配置类型查找所有启用的配置
     */
    List<PracticeConfig> findByConfigTypeAndEnabledTrueOrderByCreatedAtDesc(
            PracticeConfig.ConfigType configType);

    /**
     * 获取所有启用的配置（不分页）
     */
    List<PracticeConfig> findByEnabledTrueOrderByCreatedAtDesc();

    /**
     * 分页查询配置
     */
    Page<PracticeConfig> findByEnabledTrueOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 根据配置类型分页查询
     */
    Page<PracticeConfig> findByConfigTypeAndEnabledTrueOrderByCreatedAtDesc(
            PracticeConfig.ConfigType configType, Pageable pageable);

    /**
     * 根据适用范围分页查询
     */
    Page<PracticeConfig> findByScopeTypeAndEnabledTrueOrderByCreatedAtDesc(
            PracticeConfig.ScopeType scopeType, Pageable pageable);

    /**
     * 检查是否存在相同的配置
     */
    boolean existsByConfigTypeAndScopeTypeAndTargetId(
            PracticeConfig.ConfigType configType, 
            PracticeConfig.ScopeType scopeType, 
            Long targetId);

    /**
     * 为知识点查找有效配置（按优先级排序）
     * 优先级：知识点 > 章节 > 科目 > 全局
     */
    @Query("""
        SELECT pc FROM PracticeConfig pc 
        WHERE pc.configType = :configType 
        AND pc.enabled = true 
        AND (
            (pc.scopeType = 'KNOWLEDGE_POINT' AND pc.targetId = :knowledgePointId) OR
            (pc.scopeType = 'CHAPTER' AND pc.targetId = :chapterId) OR
            (pc.scopeType = 'SUBJECT' AND pc.targetId = :subjectId) OR
            (pc.scopeType = 'GLOBAL' AND pc.targetId IS NULL)
        )
        ORDER BY 
            CASE pc.scopeType 
                WHEN 'KNOWLEDGE_POINT' THEN 1
                WHEN 'CHAPTER' THEN 2
                WHEN 'SUBJECT' THEN 3
                WHEN 'GLOBAL' THEN 4
            END,
            pc.createdAt DESC
        """)
    List<PracticeConfig> findEffectiveConfigsForKnowledgePoint(
            @Param("configType") PracticeConfig.ConfigType configType,
            @Param("knowledgePointId") Long knowledgePointId,
            @Param("chapterId") Long chapterId,
            @Param("subjectId") Long subjectId);

    /**
     * 根据目标ID查找配置
     */
    List<PracticeConfig> findByTargetIdAndEnabledTrueOrderByCreatedAtDesc(Long targetId);

    /**
     * 根据创建者查找配置
     */
    List<PracticeConfig> findByCreatedByAndEnabledTrueOrderByCreatedAtDesc(String createdBy);

    /**
     * 统计各类型配置数量
     */
    @Query("SELECT pc.configType, COUNT(pc) FROM PracticeConfig pc WHERE pc.enabled = true GROUP BY pc.configType")
    List<Object[]> countByConfigType();

    /**
     * 统计各范围配置数量
     */
    @Query("SELECT pc.scopeType, COUNT(pc) FROM PracticeConfig pc WHERE pc.enabled = true GROUP BY pc.scopeType")
    List<Object[]> countByScopeType();
}
