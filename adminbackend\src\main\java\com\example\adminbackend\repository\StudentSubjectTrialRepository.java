package com.example.adminbackend.repository;

import com.example.adminbackend.model.StudentSubjectTrial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 学生学科试用数据访问层
 */
@Repository
public interface StudentSubjectTrialRepository extends JpaRepository<StudentSubjectTrial, Long> {

    /**
     * 根据学科版本ID查找所有学生试用记录
     */
    List<StudentSubjectTrial> findBySubjectVersionId(Long subjectVersionId);

    /**
     * 根据学科版本ID删除所有相关记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM StudentSubjectTrial sst WHERE sst.subjectVersion.id = :subjectVersionId")
    void deleteBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 根据学生ID查找所有试用记录
     */
    List<StudentSubjectTrial> findByStudentId(Long studentId);

    /**
     * 根据学科ID查找所有试用记录
     */
    List<StudentSubjectTrial> findBySubjectId(Long subjectId);

    /**
     * 检查学科版本是否被学生试用
     */
    boolean existsBySubjectVersionId(Long subjectVersionId);

    /**
     * 统计学科版本的试用数量
     */
    @Query("SELECT COUNT(sst) FROM StudentSubjectTrial sst WHERE sst.subjectVersion.id = :subjectVersionId")
    long countBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 根据学生ID和学科ID查找试用记录
     */
    List<StudentSubjectTrial> findByStudentIdAndSubjectId(Long studentId, Long subjectId);

    /**
     * 根据状态查找试用记录
     */
    List<StudentSubjectTrial> findByStatus(String status);
}
