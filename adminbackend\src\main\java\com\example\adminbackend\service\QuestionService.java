package com.example.adminbackend.service;

import com.example.adminbackend.dto.QuestionCreateRequest;
import com.example.adminbackend.dto.QuestionFormCreateRequest;
import com.example.adminbackend.dto.QuestionUpdateRequest;
import com.example.adminbackend.model.Question;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 题目服务接口
 */
public interface QuestionService {

    // ==================== 查询方法 ====================

    /**
     * 分页查询题目
     */
    Page<Question> getQuestions(Long knowledgePointId, Question.QuestionType questionType,
                               Question.Subject subject, String difficulty, Boolean enabled, String search, Pageable pageable);

    /**
     * 根据知识点获取题目列表
     */
    List<Question> getQuestionsByKnowledgePoint(Long knowledgePointId);

    /**
     * 根据ID获取题目
     */
    Question getQuestionById(Long id);

    // ==================== 管理方法 ====================

    /**
     * 创建题目
     */
    Question createQuestion(QuestionCreateRequest request);

    /**
     * 专门为前端表单创建题目
     * 自动处理科目映射和数据构建
     */
    Question createQuestionFromForm(QuestionFormCreateRequest request);

    /**
     * 批量创建题目
     */
    List<Question> createQuestions(List<QuestionCreateRequest> requests);

    /**
     * 更新题目
     */
    Question updateQuestion(Long id, QuestionUpdateRequest request);

    /**
     * 切换题目启用状态
     */
    Question toggleEnabled(Long id, Boolean enabled);

    /**
     * 删除题目
     */
    void deleteQuestion(Long id);

    /**
     * 批量删除题目
     */
    void deleteQuestions(List<Long> ids);

    // ==================== 导入导出方法 ====================

    /**
     * 导入题目
     */
    Map<String, Object> importQuestions(Long knowledgePointId, List<Map<String, Object>> questionBodies);

    /**
     * 导出题目
     */
    List<Map<String, Object>> exportQuestions(Long knowledgePointId, Question.QuestionType questionType,
                                             Question.Subject subject);

    // ==================== 统计和验证方法 ====================

    /**
     * 获取题目统计信息
     */
    Map<String, Object> getQuestionStatistics(Long knowledgePointId);

    /**
     * 验证题目JSON格式
     */
    Map<String, Object> validateQuestionBody(Map<String, Object> questionBody);
}
