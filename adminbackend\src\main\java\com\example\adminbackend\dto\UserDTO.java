package com.example.adminbackend.dto;

import com.example.adminbackend.model.Role;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    
    private Long id;
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    private String password;
    
    private String fullName;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @NotNull(message = "角色不能为空")
    private Role role;
    
    private String accountType;
    
    @Builder.Default
    private boolean enabled = true;
    
    private Date validFrom;
    
    private Date validTo;
    
    private String creator;
    
    private Integer supervisorAccounts;
} 