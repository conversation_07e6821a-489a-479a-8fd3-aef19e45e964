import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
} from '@mui/material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  FileUpload as FileUploadIcon,
  FileDownload as FileDownloadIcon,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import QuestionForm from '../components/QuestionForm';
import QuestionRenderer from '../components/QuestionRenderer';
import QuestionImportExport from '../components/QuestionImportExport';
import QuestionTableImport from '../components/QuestionTableImport';
import KnowledgePointPath from '../components/KnowledgePointPath';
import UnifiedRenderer from '../components/UnifiedRenderer';
import ErrorDisplay, { ErrorInfo, createError, createSuccess } from '../components/ErrorDisplay';
import LoadingState, { TableLoadingState } from '../components/LoadingState';
import { useAuth } from '../contexts/AuthContext';
import { questionAPI } from '../services/api';
import {
  Question,
  QuestionType,
  SubjectEnum,
  Difficulty,
  PageResponse,
  QuestionImportResult
} from '../types';

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const QuestionsPage: React.FC = () => {
  const { user } = useAuth();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalElements, setTotalElements] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  
  // 搜索和筛选状态
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [questionTypeFilter, setQuestionTypeFilter] = useState<QuestionType | ''>('');
  const [subjectFilter, setSubjectFilter] = useState<SubjectEnum | ''>('');
  const [difficultyFilter, setDifficultyFilter] = useState<Difficulty | ''>('');
  const [statusFilter, setStatusFilter] = useState<'active' | 'inactive' | null>(null);
  
  // UI状态
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [openForm, setOpenForm] = useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [openImportExport, setOpenImportExport] = useState(false);
  const [openTableImport, setOpenTableImport] = useState(false);
  const [importExportMode, setImportExportMode] = useState<'import' | 'export'>('import');
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success'
  });

  // 错误状态管理
  const [errors, setErrors] = useState<ErrorInfo[]>([]);

  // 显示消息提示
  const showSnackbar = useCallback((message: string, severity: SnackbarState['severity'] = 'success') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  // 错误处理函数
  const addError = useCallback((error: ErrorInfo) => {
    setErrors(prev => [error, ...prev].slice(0, 10)); // 最多保留10个错误
  }, []);

  const removeError = useCallback((index: number) => {
    setErrors(prev => prev.filter((_, i) => i !== index));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // 加载题目数据
  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page,
        size: pageSize,
        questionType: questionTypeFilter || undefined,
        subject: subjectFilter || undefined,
        difficulty: difficultyFilter || undefined,
        enabled: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
        search: debouncedSearchTerm || undefined, // 使用防抖搜索参数
      };

      const response: PageResponse<Question> = await questionAPI.getQuestions(params);

      setQuestions(response.content);
      setTotalElements(response.totalElements);
    } catch (error) {
      const errorInfo = createError('获取题目列表失败', {
        details: error instanceof Error ? error.message : String(error),
        retryable: true,
        onRetry: fetchQuestions
      });
      addError(errorInfo);
      showSnackbar('获取题目列表失败', 'error');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, questionTypeFilter, subjectFilter, difficultyFilter, statusFilter, debouncedSearchTerm, showSnackbar]);

  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);

  // 搜索防抖处理
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms防抖

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // 当筛选条件变化时重置到第一页
  useEffect(() => {
    setPage(0);
  }, [questionTypeFilter, subjectFilter, difficultyFilter, statusFilter, debouncedSearchTerm]);

  // 移除客户端搜索过滤，直接使用服务端数据
  const filteredQuestions = questions;

  // 获取题目标题（纯文本，用于tooltip等）
  const getQuestionTitle = (question: Question): string => {
    if (!question.body.content) return '无标题';

    // 提取HTML中的文本内容，保留数学公式的可读形式
    let text = question.body.content
      // 处理标准LaTeX格式
      .replace(/\$\$([^$]+)\$\$/g, '[公式]') // 独立公式 $$...$$
      .replace(/\$([^$]+)\$/g, '[$1]') // 行内公式 $...$
      // 处理括号格式
      .replace(/\\\\?\(([^)]+)\\\\?\)/g, '[$1]') // 行内公式 \(...\)
      .replace(/\\\\?\[([^\]]+)\\\\?\]/g, '[公式]') // 独立公式 \[...\]
      // 处理LaTeX环境
      .replace(/\\begin\{[^}]+\}[\s\S]*?\\end\{[^}]+\}/g, '[公式]')
      // 移除HTML标签
      .replace(/<[^>]+>/g, '')
      // 清理多余空格
      .replace(/\s+/g, ' ')
      .trim();

    return text.length > 60 ? text.substring(0, 60) + '...' : text;
  };

  // 渲染题目标题（支持数学公式）
  const renderQuestionTitle = (question: Question) => {
    if (!question.body.content) return <span>无标题</span>;

    // 截取内容长度，避免表格行过高
    let content = question.body.content;
    if (content.length > 200) {
      content = content.substring(0, 200) + '...';
    }

    return (
      <Box sx={{
        maxWidth: '100%',
        overflow: 'hidden',
        '& .mathjax-renderer': {
          fontSize: '13px !important',
          lineHeight: '1.4 !important'
        }
      }}>
        <UnifiedRenderer
          content={content}
          maxLength={200}
          inline={true}
          style={{
            fontSize: '13px',
            lineHeight: '1.4',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
          onError={(error) => {
            // 静默处理渲染错误，避免控制台污染
          }}
        />
      </Box>
    );
  };

  // 获取题型标签
  const getQuestionTypeLabel = (type: QuestionType): string => {
    const labels: Record<QuestionType, string> = {
      SINGLE_CHOICE: '单选题',
      MULTIPLE_CHOICE: '多选题',
      FILL_IN_BLANK: '填空题',
      TRUE_FALSE: '判断题',
      MATCHING: '匹配题',
      READING_COMPREHENSION: '阅读理解',
      CLOZE_TEST: '完形填空',
      LISTENING: '听力题',
    };
    return labels[type] || type;
  };

  // 获取科目标签
  const getSubjectLabel = (subject: SubjectEnum): string => {
    const labels: Record<SubjectEnum, string> = {
      ENGLISH: '英语',
      MATH: '数学',
      PHYSICS: '物理',
      CHEMISTRY: '化学',
    };
    return labels[subject] || subject;
  };

  // 获取难度标签
  const getDifficultyLabel = (difficulty: Difficulty): string => {
    const labels: Record<Difficulty, string> = {
      EASY: '简单',
      MEDIUM: '中等',
      HARD: '困难',
    };
    return labels[difficulty] || difficulty;
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty: Difficulty): 'success' | 'warning' | 'error' => {
    const colors: Record<Difficulty, 'success' | 'warning' | 'error'> = {
      EASY: 'success',
      MEDIUM: 'warning',
      HARD: 'error',
    };
    return colors[difficulty] || 'warning';
  };

  // 处理启用/禁用
  const handleToggleEnabled = async (id: number, enabled: boolean) => {
    try {
      await questionAPI.toggleQuestionEnabled(id, enabled);
      showSnackbar(`题目已${enabled ? '启用' : '禁用'}`, 'success');
      fetchQuestions();
    } catch (error) {
      console.error('更新题目状态失败:', error);
      const errorInfo = createError('更新题目状态失败', {
        details: error instanceof Error ? error.message : String(error),
        retryable: true,
        onRetry: () => handleToggleEnabled(id, enabled)
      });
      addError(errorInfo);
      showSnackbar('更新题目状态失败', 'error');
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    if (!window.confirm('确定要删除这个题目吗？')) return;

    try {
      await questionAPI.deleteQuestion(id);
      showSnackbar('题目删除成功', 'success');
      fetchQuestions();
    } catch (error) {
      const errorInfo = createError('删除题目失败', {
        details: error instanceof Error ? error.message : String(error),
        code: 'DELETE_FAILED'
      });
      addError(errorInfo);
      showSnackbar('删除题目失败', 'error');
    }
  };

  // 处理预览
  const handlePreview = (question: Question) => {
    setSelectedQuestion(question);
    setOpenPreview(true);
  };

  // 处理编辑
  const handleEdit = (question: Question) => {
    setSelectedQuestion(question);
    setOpenForm(true);
  };

  // 处理表单提交
  const handleFormSubmit = () => {
    setOpenForm(false);
    setSelectedQuestion(null);
    fetchQuestions();
    const successMessage = selectedQuestion ? '题目更新成功' : '题目创建成功';
    showSnackbar(successMessage, 'success');

    // 添加成功消息到错误列表（用于显示详细的成功信息）
    const successInfo = createSuccess(successMessage, {
      details: selectedQuestion ? `题目ID: ${selectedQuestion.id}` : '新题目已添加到题库'
    });
    addError(successInfo);
  };

  // 处理表单取消
  const handleFormCancel = () => {
    setOpenForm(false);
    setSelectedQuestion(null);
  };

  // 处理导入成功
  const handleImportSuccess = () => {
    fetchQuestions();
    showSnackbar('题目导入成功', 'success');
  };

  // 打开导入对话框
  const handleOpenImport = () => {
    setImportExportMode('import');
    setOpenImportExport(true);
  };

  // 打开导出对话框
  const handleOpenExport = () => {
    setImportExportMode('export');
    setOpenImportExport(true);
  };

  // 重置筛选
  const handleResetFilters = () => {
    setQuestionTypeFilter('');
    setSubjectFilter('');
    setDifficultyFilter('');
    setStatusFilter(null);
    setSearchTerm('');
    setDebouncedSearchTerm('');
  };

  // 计算活跃筛选条件数量
  const getActiveFiltersCount = () => {
    let count = 0;
    if (questionTypeFilter) count++;
    if (subjectFilter) count++;
    if (difficultyFilter) count++;
    if (statusFilter) count++;
    if (debouncedSearchTerm) count++;
    return count;
  };

  // 数据表格列定义
  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
    },
    {
      field: 'title',
      headerName: '题目标题',
      flex: 1,
      minWidth: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Tooltip title={getQuestionTitle(params.row)}>
          <Box sx={{ width: '100%', py: 0.5 }}>
            {renderQuestionTitle(params.row)}
          </Box>
        </Tooltip>
      ),
    },
    {
      field: 'questionType',
      headerName: '题型',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={getQuestionTypeLabel(params.value)}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'subject',
      headerName: '科目',
      width: 100,
      renderCell: (params: GridRenderCellParams) => {
        const subject = params.row.subject; // 现在直接从row获取
        if (!subject) return <span>-</span>;
        return (
          <Chip
            label={getSubjectLabel(subject)}
            size="small"
            color="primary"
            variant="outlined"
          />
        );
      },
    },
    {
      field: 'difficulty',
      headerName: '难度',
      width: 100,
      renderCell: (params: GridRenderCellParams) => {
        const difficulty = params.row.difficulty; // 现在直接从row获取
        if (!difficulty) return <span>-</span>;
        return (
          <Chip
            label={getDifficultyLabel(difficulty)}
            size="small"
            color={getDifficultyColor(difficulty)}
            variant="outlined"
          />
        );
      },
    },
    {
      field: 'knowledgePoint',
      headerName: '知识点',
      flex: 1,
      minWidth: 300,
      renderCell: (params: GridRenderCellParams) => (
        <KnowledgePointPath
          knowledgePointId={params.row.knowledgePointId}
          variant="compact"
          maxWidth={280}
        />
      ),
    },
    {
      field: 'enabled',
      headerName: '状态',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? '启用' : '禁用'}
          size="small"
          color={params.value ? 'success' : 'default'}
          icon={params.value ? <CheckCircleIcon /> : <CancelIcon />}
        />
      ),
    },
    {
      field: 'actions',
      headerName: '操作',
      width: 200,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="预览">
            <IconButton
              size="small"
              onClick={() => handlePreview(params.row)}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="编辑">
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={params.row.enabled ? '禁用' : '启用'}>
            <IconButton
              size="small"
              onClick={() => handleToggleEnabled(params.row.id, !params.row.enabled)}
            >
              {params.row.enabled ? <CancelIcon fontSize="small" /> : <CheckCircleIcon fontSize="small" />}
            </IconButton>
          </Tooltip>
          <Tooltip title="删除">
            <IconButton
              size="small"
              color="error"
              onClick={() => handleDelete(params.row.id)}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Layout>
      <Box>
        {/* 错误显示区域 */}
        {errors.length > 0 && (
          <Box sx={{ mb: 3 }}>
            {errors.slice(0, 3).map((error, index) => (
              <ErrorDisplay
                key={index}
                error={error}
                onDismiss={() => removeError(index)}
                collapsible={true}
              />
            ))}
            {errors.length > 3 && (
              <Button
                variant="text"
                size="small"
                onClick={clearAllErrors}
                sx={{ mt: 1 }}
              >
                清除所有错误 ({errors.length})
              </Button>
            )}
          </Box>
        )}



        {/* 页面标题和操作栏 */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" fontWeight="bold">
              题库管理
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<FileUploadIcon />}
                onClick={() => setOpenTableImport(true)}
              >
                表格导入
              </Button>
              <Button
                variant="outlined"
                startIcon={<FileUploadIcon />}
                onClick={handleOpenImport}
              >
                JSON导入
              </Button>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleOpenExport}
              >
                导出题目
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setSelectedQuestion(null); // 确保清空选中的题目
                  setOpenForm(true);
                }}
              >
                添加题目
              </Button>
            </Box>
          </Box>

          {/* 搜索和筛选栏 */}
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
            <TextField
              placeholder="搜索题目..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ minWidth: 200 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: debouncedSearchTerm && (
                  <InputAdornment position="end">
                    <Chip
                      label="搜索中"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ height: 20 }}
                    />
                  </InputAdornment>
                ),
              }}
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>题型</InputLabel>
              <Select
                value={questionTypeFilter}
                label="题型"
                onChange={(e) => {
                  const value = e.target.value as QuestionType;
                  console.log('🎯 题型筛选变更:', value);
                  setQuestionTypeFilter(value);
                }}
              >
                <MenuItem value="">全部题型</MenuItem>
                <MenuItem value="SINGLE_CHOICE">单选题</MenuItem>
                <MenuItem value="MULTIPLE_CHOICE">多选题</MenuItem>
                <MenuItem value="FILL_IN_BLANK">填空题</MenuItem>
                <MenuItem value="TRUE_FALSE">判断题</MenuItem>
                <MenuItem value="MATCHING">匹配题</MenuItem>
                <MenuItem value="READING_COMPREHENSION">阅读理解</MenuItem>
                <MenuItem value="CLOZE_TEST">完形填空</MenuItem>
                <MenuItem value="LISTENING">听力题</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 100 }}>
              <InputLabel>科目</InputLabel>
              <Select
                value={subjectFilter}
                label="科目"
                onChange={(e) => {
                  const value = e.target.value as SubjectEnum;
                  console.log('📚 科目筛选变更:', value);
                  setSubjectFilter(value);
                }}
              >
                <MenuItem value="">全部科目</MenuItem>
                <MenuItem value="ENGLISH">英语</MenuItem>
                <MenuItem value="MATH">数学</MenuItem>
                <MenuItem value="PHYSICS">物理</MenuItem>
                <MenuItem value="CHEMISTRY">化学</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 100 }}>
              <InputLabel>难度</InputLabel>
              <Select
                value={difficultyFilter}
                label="难度"
                onChange={(e) => {
                  const value = e.target.value as Difficulty;
                  console.log('⭐ 难度筛选变更:', value);
                  setDifficultyFilter(value);
                }}
              >
                <MenuItem value="">全部难度</MenuItem>
                <MenuItem value="EASY">简单</MenuItem>
                <MenuItem value="MEDIUM">中等</MenuItem>
                <MenuItem value="HARD">困难</MenuItem>
              </Select>
            </FormControl>

            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={(e) => setFilterAnchorEl(e.currentTarget)}
            >
              状态筛选
              {statusFilter && (
                <Chip
                  label={1}
                  size="small"
                  color="primary"
                  sx={{ ml: 1, height: 16, minWidth: 16 }}
                />
              )}
            </Button>

            <Button
              variant="text"
              onClick={handleResetFilters}
              sx={{ minWidth: 'auto' }}
              disabled={getActiveFiltersCount() === 0}
            >
              重置
              {getActiveFiltersCount() > 0 && (
                <Chip
                  label={getActiveFiltersCount()}
                  size="small"
                  color="primary"
                  sx={{ ml: 1, height: 16, minWidth: 16 }}
                />
              )}
            </Button>
          </Box>

          {/* 筛选条件摘要 */}
          {getActiveFiltersCount() > 0 && (
            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                当前筛选:
              </Typography>
              {debouncedSearchTerm && (
                <Chip
                  label={`搜索: ${debouncedSearchTerm}`}
                  size="small"
                  onDelete={() => setSearchTerm('')}
                  color="primary"
                />
              )}
              {questionTypeFilter && (
                <Chip
                  label={`题型: ${getQuestionTypeLabel(questionTypeFilter)}`}
                  size="small"
                  onDelete={() => setQuestionTypeFilter('')}
                  color="primary"
                />
              )}
              {subjectFilter && (
                <Chip
                  label={`科目: ${getSubjectLabel(subjectFilter)}`}
                  size="small"
                  onDelete={() => setSubjectFilter('')}
                  color="primary"
                />
              )}
              {difficultyFilter && (
                <Chip
                  label={`难度: ${getDifficultyLabel(difficultyFilter)}`}
                  size="small"
                  onDelete={() => setDifficultyFilter('')}
                  color="primary"
                />
              )}
              {statusFilter && (
                <Chip
                  label={`状态: ${statusFilter === 'active' ? '已启用' : '已禁用'}`}
                  size="small"
                  onDelete={() => setStatusFilter(null)}
                  color="primary"
                />
              )}
            </Box>
          )}
        </Paper>

        {/* 题目列表 */}
        <Paper sx={{ borderRadius: 2, position: 'relative' }}>
          {loading && filteredQuestions.length === 0 ? (
            <TableLoadingState rows={pageSize} columns={6} />
          ) : (
            <DataGrid
              rows={filteredQuestions}
              columns={columns}
              loading={loading}
              paginationMode="server"
              rowCount={totalElements}
              paginationModel={{ page, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page);
                setPageSize(model.pageSize);
              }}
              pageSizeOptions={[5, 10, 25, 50]}
              disableRowSelectionOnClick
              autoHeight
              sx={{
                border: 'none',
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: '#fafafa',
                  borderBottom: '2px solid #e0e0e0',
                },
                '& .MuiDataGrid-loadingOverlay': {
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                },
              }}
              slots={{
                loadingOverlay: () => (
                  <LoadingState
                    type="dots"
                    message="正在加载题目数据..."
                    overlay={true}
                  />
                ),
                noRowsOverlay: () => (
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: 200,
                    gap: 2
                  }}>
                    <Typography variant="h6" color="text.secondary">
                      暂无题目数据
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {getActiveFiltersCount() > 0 ? '请尝试调整筛选条件' : '点击"添加题目"开始创建题目'}
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => setOpenForm(true)}
                    >
                      添加题目
                    </Button>
                  </Box>
                )
              }}
            />
          )}
        </Paper>
      </Box>

      {/* 状态筛选菜单 */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={() => setFilterAnchorEl(null)}
      >
        <MenuItem onClick={() => {
          console.log('🔄 状态筛选变更: null (全部状态)');
          setStatusFilter(null);
          setFilterAnchorEl(null);
        }}>
          全部状态
        </MenuItem>
        <MenuItem onClick={() => {
          console.log('✅ 状态筛选变更: active (已启用)');
          setStatusFilter('active');
          setFilterAnchorEl(null);
        }}>
          已启用
        </MenuItem>
        <MenuItem onClick={() => {
          console.log('❌ 状态筛选变更: inactive (已禁用)');
          setStatusFilter('inactive');
          setFilterAnchorEl(null);
        }}>
          已禁用
        </MenuItem>
      </Menu>

      {/* 题目表单对话框 */}
      <Dialog open={openForm} onClose={() => setOpenForm(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          {selectedQuestion ? '编辑题目' : '添加题目'}
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <QuestionForm
            question={selectedQuestion}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>

      {/* 题目预览对话框 */}
      <Dialog open={openPreview} onClose={() => setOpenPreview(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          题目预览
        </DialogTitle>
        <DialogContent>
          {selectedQuestion && (
            <QuestionRenderer
              question={selectedQuestion}
              showAnswer={true}
              showExplanation={true}
              interactive={false}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenPreview(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 导入导出对话框 */}
      <QuestionImportExport
        open={openImportExport}
        onClose={() => setOpenImportExport(false)}
        mode={importExportMode}
        onImportSuccess={handleImportSuccess}
      />

      {/* 表格导入对话框 */}
      <QuestionTableImport
        open={openTableImport}
        onClose={() => setOpenTableImport(false)}
        knowledgePointId={1} // TODO: 从当前选择的知识点获取
        onImportSuccess={handleImportSuccess}
      />

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default QuestionsPage;
