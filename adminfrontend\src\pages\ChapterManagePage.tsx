import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Chip,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  MenuBook,
  Refresh,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { curriculumAPI } from '../services/api';
import { Chapter, Subject, SubjectVersion } from '../types';

const ChapterManagePage: React.FC = () => {
  const { isSuperAdmin } = useAuth();
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [subjectVersions, setSubjectVersions] = useState<SubjectVersion[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingChapter, setEditingChapter] = useState<Chapter | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    orderIndex: 1,
    subjectVersionId: 0,
  });
  const [selectedSubjectId, setSelectedSubjectId] = useState<number>(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 加载章节列表
  const loadChapters = async () => {
    try {
      setLoading(true);
      const data = await curriculumAPI.getChapters();
      setChapters(data);
    } catch (error: any) {
      console.error('加载章节列表失败:', error);
      setError('加载章节列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 加载学科列表
  const loadSubjects = async () => {
    try {
      const data = await curriculumAPI.getSubjects();
      setSubjects(data);
    } catch (error: any) {
      console.error('加载学科列表失败:', error);
    }
  };

  // 加载学科版本
  const loadSubjectVersions = async (subjectId: number) => {
    try {
      const data = await curriculumAPI.getSubjectVersions(subjectId);
      setSubjectVersions(data);
    } catch (error: any) {
      console.error('加载学科版本失败:', error);
    }
  };

  useEffect(() => {
    loadChapters();
    loadSubjects();
  }, []);

  // 当选择学科时加载版本
  useEffect(() => {
    if (selectedSubjectId > 0) {
      loadSubjectVersions(selectedSubjectId);
    } else {
      setSubjectVersions([]);
    }
  }, [selectedSubjectId]);

  // 打开新增对话框
  const handleAdd = () => {
    setEditingChapter(null);
    setFormData({ name: '', orderIndex: 1, subjectVersionId: 0 });
    setSelectedSubjectId(0);
    setOpenDialog(true);
  };

  // 打开编辑对话框
  const handleEdit = (chapter: Chapter) => {
    setEditingChapter(chapter);
    setFormData({
      name: chapter.name,
      orderIndex: chapter.orderIndex,
      subjectVersionId: chapter.subjectVersionId || 0,
    });
    
    // 找到对应的学科ID
    const subjectVersion = subjects.find(s => 
      s.versions?.some(v => v.id === chapter.subjectVersionId)
    );
    if (subjectVersion) {
      setSelectedSubjectId(subjectVersion.id);
    }
    
    setOpenDialog(true);
  };

  // 保存章节
  const handleSave = async () => {
    if (!formData.name.trim()) {
      setError('章节名称不能为空');
      return;
    }

    if (!formData.subjectVersionId) {
      setError('请选择学科版本');
      return;
    }

    try {
      if (editingChapter) {
        // 更新章节
        await curriculumAPI.updateChapter(editingChapter.id, {
          name: formData.name,
          orderIndex: formData.orderIndex,
        });
        setSuccess('章节更新成功');
      } else {
        // 创建新章节
        await curriculumAPI.createChapter(formData);
        setSuccess('章节创建成功');
      }

      setOpenDialog(false);
      await loadChapters();
    } catch (error: any) {
      console.error('保存章节失败:', error);
      setError('保存章节失败: ' + (error.message || '未知错误'));
    }
  };

  // 删除章节
  const handleDelete = async (chapter: Chapter) => {
    const confirmMessage = `⚠️ 警告：级联删除操作

确定要删除章节"${chapter.name}"吗？

此操作将同时删除：
• 该章节下的所有知识点
• 知识点关联的视频和图片文件

此操作不可撤销，请谨慎操作！`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const response = await curriculumAPI.deleteChapter(chapter.id);
      if (response.deletedFilesCount > 0) {
        setSuccess(`章节删除成功，已同时删除 ${response.deletedFilesCount} 个文件`);
      } else {
        setSuccess('章节删除成功');
      }
      await loadChapters();
    } catch (error: any) {
      console.error('删除章节失败:', error);
      setError('删除章节失败: ' + (error.message || '未知错误'));
    }
  };

  // 获取章节的学科信息
  const getChapterSubjectInfo = (chapter: Chapter) => {
    // 首先尝试从章节DTO的字段获取（后端已经填充）
    if (chapter.subjectName && chapter.subjectVersionName) {
      return `${chapter.subjectName} - ${chapter.subjectVersionName}`;
    }

    // 如果没有，则从 subjects 中查找
    for (const subject of subjects) {
      const version = subject.versions?.find(v => v.id === chapter.subjectVersionId);
      if (version) {
        return `${subject.name} - ${version.name}`;
      }
    }
    return '未知学科';
  };

  // 获取章节的年级学期信息
  const getChapterSchoolLevel = (chapter: Chapter) => {
    // 首先尝试从章节DTO的 schoolLevel 字段获取（后端已经填充）
    if (chapter.schoolLevel) {
      return chapter.schoolLevel;
    }

    // 如果没有，则从章节的 subjectVersion 字段获取
    if (chapter.subjectVersion?.schoolLevel) {
      return chapter.subjectVersion.schoolLevel;
    }

    // 最后从 subjects 中查找
    for (const subject of subjects) {
      const version = subject.versions?.find(v => v.id === chapter.subjectVersionId);
      if (version?.schoolLevel) {
        return version.schoolLevel;
      }
    }
    return '未知年级';
  };

  return (
    <Layout>
      <Box sx={{ p: 3 }}>
        {/* 页面标题 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <MenuBook sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h4" fontWeight="bold">
              章节管理
            </Typography>
          </Box>
          <Box>
            <Button
              startIcon={<Refresh />}
              onClick={loadChapters}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              刷新
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleAdd}
            >
              新增章节
            </Button>
          </Box>
        </Box>

        {/* 章节列表 */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>章节名称</TableCell>
                  <TableCell>年级学期</TableCell>
                  <TableCell>所属学科版本</TableCell>
                  <TableCell>排序</TableCell>
                  <TableCell>知识点数量</TableCell>
                  <TableCell>创建时间</TableCell>
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : chapters.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <Typography color="text.secondary">
                        暂无章节数据
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  chapters.map((chapter) => (
                    <TableRow key={chapter.id}>
                      <TableCell>{chapter.id}</TableCell>
                      <TableCell>
                        <Typography fontWeight="medium">
                          {chapter.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getChapterSchoolLevel(chapter)}
                          size="small"
                          color="info"
                          variant="outlined"
                          sx={{ fontWeight: 'medium' }}
                        />
                      </TableCell>
                      <TableCell>
                        {getChapterSubjectInfo(chapter)}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={chapter.orderIndex} 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={chapter.knowledgePoints?.length || 0} 
                          size="small" 
                          color="secondary" 
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        {chapter.createdAt ? new Date(chapter.createdAt).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(chapter)}
                          color="primary"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(chapter)}
                          color="error"
                          disabled={!isSuperAdmin}
                          title={!isSuperAdmin ? "只有超级管理员可以删除章节" : "删除章节"}
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* 新增/编辑对话框 */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingChapter ? '编辑章节' : '新增章节'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="章节名称 *"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                sx={{ mb: 2 }}
              />
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>选择学科 *</InputLabel>
                <Select
                  value={selectedSubjectId}
                  onChange={(e) => setSelectedSubjectId(Number(e.target.value))}
                  label="选择学科 *"
                >
                  <MenuItem value={0}>请选择学科</MenuItem>
                  {subjects.map((subject) => (
                    <MenuItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>选择学科版本 *</InputLabel>
                <Select
                  value={formData.subjectVersionId}
                  onChange={(e) => setFormData({ ...formData, subjectVersionId: Number(e.target.value) })}
                  label="选择学科版本 *"
                  disabled={!selectedSubjectId}
                >
                  <MenuItem value={0}>请选择学科版本</MenuItem>
                  {subjectVersions.map((version) => (
                    <MenuItem key={version.id} value={version.id}>
                      {version.schoolLevel ? `${version.schoolLevel} - ${version.name}` : version.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="排序序号"
                type="number"
                value={formData.orderIndex}
                onChange={(e) => setFormData({ ...formData, orderIndex: Number(e.target.value) })}
                inputProps={{ min: 1 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>取消</Button>
            <Button onClick={handleSave} variant="contained">
              {editingChapter ? '更新' : '创建'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 成功提示 */}
        <Snackbar
          open={!!success}
          autoHideDuration={3000}
          onClose={() => setSuccess('')}
        >
          <Alert severity="success" onClose={() => setSuccess('')}>
            {success}
          </Alert>
        </Snackbar>

        {/* 错误提示 */}
        <Snackbar
          open={!!error}
          autoHideDuration={5000}
          onClose={() => setError('')}
        >
          <Alert severity="error" onClose={() => setError('')}>
            {error}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default ChapterManagePage;
