package com.example.adminbackend.exception;

import com.example.adminbackend.dto.ErrorDetail;
import lombok.Getter;

import java.util.List;

/**
 * 业务异常类
 */
@Getter
public class BusinessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final ErrorCode errorCode;
    
    /**
     * 错误详情列表
     */
    private final List<ErrorDetail> errorDetails;
    
    /**
     * 构造函数 - 使用错误码
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数 - 使用错误码和自定义消息
     */
    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数 - 使用错误码、消息和原因
     */
    public BusinessException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorDetails = null;
    }
    
    /**
     * 构造函数 - 使用错误码和错误详情
     */
    public BusinessException(ErrorCode errorCode, List<ErrorDetail> errorDetails) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }
    
    /**
     * 构造函数 - 使用错误码、消息和错误详情
     */
    public BusinessException(ErrorCode errorCode, String message, List<ErrorDetail> errorDetails) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建用户不存在异常
     */
    public static BusinessException userNotFound(String identifier) {
        return new BusinessException(ErrorCode.USER_NOT_FOUND, "用户不存在: " + identifier);
    }
    
    /**
     * 创建用户已存在异常
     */
    public static BusinessException userAlreadyExists(String identifier) {
        return new BusinessException(ErrorCode.USER_ALREADY_EXISTS, "用户已存在: " + identifier);
    }
    
    /**
     * 创建权限不足异常
     */
    public static BusinessException insufficientPermissions(String operation) {
        return new BusinessException(ErrorCode.INSUFFICIENT_PERMISSIONS, "权限不足，无法执行操作: " + operation);
    }
    
    /**
     * 创建题目不存在异常
     */
    public static BusinessException questionNotFound(Long questionId) {
        return new BusinessException(ErrorCode.QUESTION_NOT_FOUND, "题目不存在: " + questionId);
    }
    
    /**
     * 创建题目JSON格式无效异常
     */
    public static BusinessException questionJsonInvalid(String reason) {
        return new BusinessException(ErrorCode.QUESTION_JSON_INVALID, "题目JSON格式无效: " + reason);
    }
    
    /**
     * 创建知识点不存在异常
     */
    public static BusinessException knowledgePointNotFound(Long knowledgePointId) {
        return new BusinessException(ErrorCode.KNOWLEDGE_POINT_NOT_FOUND, "知识点不存在: " + knowledgePointId);
    }
    
    /**
     * 创建参数验证异常
     */
    public static BusinessException validationError(String message, List<ErrorDetail> errorDetails) {
        return new BusinessException(ErrorCode.VALIDATION_ERROR, message, errorDetails);
    }
    
    /**
     * 创建操作不被允许异常
     */
    public static BusinessException operationNotAllowed(String operation, String reason) {
        return new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, 
                String.format("操作不被允许: %s, 原因: %s", operation, reason));
    }
    
    /**
     * 创建资源冲突异常
     */
    public static BusinessException resourceConflict(String resource, String reason) {
        return new BusinessException(ErrorCode.RESOURCE_CONFLICT, 
                String.format("资源冲突: %s, 原因: %s", resource, reason));
    }
    
    /**
     * 创建文件处理异常
     */
    public static BusinessException fileProcessingFailed(String fileName, String reason) {
        return new BusinessException(ErrorCode.FILE_PROCESSING_FAILED, 
                String.format("文件处理失败: %s, 原因: %s", fileName, reason));
    }
    
    /**
     * 获取错误码值
     */
    public int getErrorCodeValue() {
        return errorCode.getCode();
    }
    
    /**
     * 判断是否有错误详情
     */
    public boolean hasErrorDetails() {
        return errorDetails != null && !errorDetails.isEmpty();
    }
}
