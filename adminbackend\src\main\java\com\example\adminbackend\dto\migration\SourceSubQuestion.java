package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 源数据子题目结构DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceSubQuestion {
    
    /**
     * 子题目类型
     */
    private String type;
    
    /**
     * 子题目ID
     */
    private String id;
    
    /**
     * 子题目内容（HTML格式）
     */
    private String content;
    
    /**
     * 子题目选项（选择题使用）
     */
    private List<String> options;
    
    /**
     * 子题目答案
     */
    private Object answer;
    
    /**
     * 子题目解析（HTML格式）
     */
    private String explanation;
}
