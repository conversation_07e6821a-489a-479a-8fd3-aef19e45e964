import React from 'react';
import {
  <PERSON>ert,
  Alert<PERSON><PERSON>le,
  Box,
  Button,
  Collapse,
  IconButton,
  Typography,
  Chip
} from '@mui/material';
import {
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

export interface ErrorInfo {
  type: 'error' | 'warning' | 'info' | 'success';
  title?: string;
  message: string;
  details?: string;
  code?: string;
  timestamp?: Date;
  retryable?: boolean;
  onRetry?: () => void;
}

interface ErrorDisplayProps {
  error: ErrorInfo;
  showDetails?: boolean;
  collapsible?: boolean;
  onDismiss?: () => void;
}

/**
 * 增强的错误显示组件
 * 支持不同类型的消息显示，详细信息展开，重试功能等
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  showDetails = false,
  collapsible = true,
  onDismiss
}) => {
  const [expanded, setExpanded] = React.useState(showDetails);

  const getIcon = () => {
    switch (error.type) {
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      case 'success':
        return <SuccessIcon />;
      default:
        return <InfoIcon />;
    }
  };

  const getSeverity = () => {
    switch (error.type) {
      case 'error':
        return 'error' as const;
      case 'warning':
        return 'warning' as const;
      case 'info':
        return 'info' as const;
      case 'success':
        return 'success' as const;
      default:
        return 'info' as const;
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <Alert
      severity={getSeverity()}
      icon={getIcon()}
      onClose={onDismiss}
      sx={{ mb: 2 }}
      action={
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {error.retryable && error.onRetry && (
            <Button
              size="small"
              startIcon={<RefreshIcon />}
              onClick={error.onRetry}
              color="inherit"
            >
              重试
            </Button>
          )}
          {collapsible && (error.details || error.code || error.timestamp) && (
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              color="inherit"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>
      }
    >
      {error.title && <AlertTitle>{error.title}</AlertTitle>}
      
      <Typography variant="body2">
        {error.message}
      </Typography>

      {/* 详细信息展开区域 */}
      <Collapse in={expanded}>
        <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid rgba(0,0,0,0.1)' }}>
          {error.code && (
            <Box sx={{ mb: 1 }}>
              <Chip
                label={`错误代码: ${error.code}`}
                size="small"
                variant="outlined"
                color={getSeverity()}
              />
            </Box>
          )}
          
          {error.timestamp && (
            <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
              发生时间: {formatTimestamp(error.timestamp)}
            </Typography>
          )}
          
          {error.details && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                详细信息:
              </Typography>
              <Typography 
                variant="body2" 
                component="pre"
                sx={{ 
                  whiteSpace: 'pre-wrap',
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  backgroundColor: 'rgba(0,0,0,0.05)',
                  padding: 1,
                  borderRadius: 1,
                  maxHeight: 200,
                  overflow: 'auto'
                }}
              >
                {error.details}
              </Typography>
            </Box>
          )}
        </Box>
      </Collapse>
    </Alert>
  );
};

/**
 * 错误列表显示组件
 */
interface ErrorListProps {
  errors: ErrorInfo[];
  maxVisible?: number;
  onDismissAll?: () => void;
  onDismiss?: (index: number) => void;
}

export const ErrorList: React.FC<ErrorListProps> = ({
  errors,
  maxVisible = 5,
  onDismissAll,
  onDismiss
}) => {
  if (errors.length === 0) return null;

  const visibleErrors = errors.slice(0, maxVisible);
  const hiddenCount = errors.length - maxVisible;

  return (
    <Box>
      {errors.length > 1 && onDismissAll && (
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
          <Button size="small" onClick={onDismissAll}>
            清除所有消息
          </Button>
        </Box>
      )}
      
      {visibleErrors.map((error, index) => (
        <ErrorDisplay
          key={index}
          error={error}
          onDismiss={onDismiss ? () => onDismiss(index) : undefined}
        />
      ))}
      
      {hiddenCount > 0 && (
        <Alert severity="info" sx={{ mb: 2 }}>
          还有 {hiddenCount} 条消息未显示
        </Alert>
      )}
    </Box>
  );
};

/**
 * 创建错误信息的辅助函数
 */
export const createError = (
  message: string,
  options: Partial<Omit<ErrorInfo, 'message' | 'type'>> = {}
): ErrorInfo => ({
  type: 'error',
  message,
  timestamp: new Date(),
  ...options
});

export const createWarning = (
  message: string,
  options: Partial<Omit<ErrorInfo, 'message' | 'type'>> = {}
): ErrorInfo => ({
  type: 'warning',
  message,
  timestamp: new Date(),
  ...options
});

export const createInfo = (
  message: string,
  options: Partial<Omit<ErrorInfo, 'message' | 'type'>> = {}
): ErrorInfo => ({
  type: 'info',
  message,
  timestamp: new Date(),
  ...options
});

export const createSuccess = (
  message: string,
  options: Partial<Omit<ErrorInfo, 'message' | 'type'>> = {}
): ErrorInfo => ({
  type: 'success',
  message,
  timestamp: new Date(),
  ...options
});

export default ErrorDisplay;
