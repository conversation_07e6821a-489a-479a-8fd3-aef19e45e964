import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import {
  Settings,
  Public,
  School,
  MenuBook,
  Psychology,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import GlobalConfigTab from '../components/practiceConfig/GlobalConfigTab';
import SubjectConfigTab from '../components/practiceConfig/SubjectConfigTab';
import ChapterConfigTab from '../components/practiceConfig/ChapterConfigTab';
import KnowledgePointConfigTab from '../components/practiceConfig/KnowledgePointConfigTab';
import ConfigListTab from '../components/practiceConfig/ConfigListTab';
import { PracticeConfigAPI } from '../services/practiceConfigApi';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`practice-config-tabpanel-${index}`}
      aria-labelledby={`practice-config-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `practice-config-tab-${index}`,
    'aria-controls': `practice-config-tabpanel-${index}`,
  };
}

const PracticeConfigPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      setLoading(true);
      const stats = await PracticeConfigAPI.getConfigStatistics();
      setStatistics(stats);
    } catch (error: any) {
      console.error('加载统计信息失败:', error);
      showSnackbar('加载统计信息失败: ' + (error.message || '未知错误'), 'error');
    } finally {
      setLoading(false);
    }
  };

  // 显示提示消息
  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  // 关闭提示消息
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // 处理Tab切换
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadStatistics();
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  return (
    <Layout>
      <Box sx={{ width: '100%' }}>
        {/* 页面标题 */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Settings sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                题目数量管理
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                配置知识点练习和章节测试的题目数量，支持全局、科目、章节、知识点四级配置
              </Typography>
            </Box>
          </Box>

          {/* 统计信息 */}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : statistics && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                当前共有 <strong>{statistics.total || 0}</strong> 个配置，
                其中知识点练习配置 <strong>{statistics.byType?.KNOWLEDGE_POINT_PRACTICE || 0}</strong> 个，
                章节测试配置 <strong>{statistics.byType?.CHAPTER_TEST || 0}</strong> 个
              </Typography>
            </Alert>
          )}
        </Paper>

        {/* 配置选项卡 */}
        <Paper sx={{ borderRadius: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="practice config tabs"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab 
                icon={<Public />} 
                label="全局配置" 
                {...a11yProps(0)} 
                sx={{ minHeight: 72 }}
              />
              <Tab 
                icon={<School />} 
                label="科目配置" 
                {...a11yProps(1)} 
                sx={{ minHeight: 72 }}
              />
              <Tab 
                icon={<MenuBook />} 
                label="章节配置" 
                {...a11yProps(2)} 
                sx={{ minHeight: 72 }}
              />
              <Tab 
                icon={<Psychology />} 
                label="知识点配置" 
                {...a11yProps(3)} 
                sx={{ minHeight: 72 }}
              />
              <Tab 
                icon={<Settings />} 
                label="配置列表" 
                {...a11yProps(4)} 
                sx={{ minHeight: 72 }}
              />
            </Tabs>
          </Box>

          {/* 全局配置 */}
          <TabPanel value={tabValue} index={0}>
            <GlobalConfigTab 
              onSuccess={(message) => {
                showSnackbar(message, 'success');
                handleRefresh();
              }}
              onError={(message) => showSnackbar(message, 'error')}
            />
          </TabPanel>

          {/* 科目配置 */}
          <TabPanel value={tabValue} index={1}>
            <SubjectConfigTab 
              onSuccess={(message) => {
                showSnackbar(message, 'success');
                handleRefresh();
              }}
              onError={(message) => showSnackbar(message, 'error')}
            />
          </TabPanel>

          {/* 章节配置 */}
          <TabPanel value={tabValue} index={2}>
            <ChapterConfigTab 
              onSuccess={(message) => {
                showSnackbar(message, 'success');
                handleRefresh();
              }}
              onError={(message) => showSnackbar(message, 'error')}
            />
          </TabPanel>

          {/* 知识点配置 */}
          <TabPanel value={tabValue} index={3}>
            <KnowledgePointConfigTab 
              onSuccess={(message) => {
                showSnackbar(message, 'success');
                handleRefresh();
              }}
              onError={(message) => showSnackbar(message, 'error')}
            />
          </TabPanel>

          {/* 配置列表 */}
          <TabPanel value={tabValue} index={4}>
            <ConfigListTab 
              onSuccess={(message) => {
                showSnackbar(message, 'success');
                handleRefresh();
              }}
              onError={(message) => showSnackbar(message, 'error')}
            />
          </TabPanel>
        </Paper>

        {/* 提示消息 */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert 
            onClose={handleCloseSnackbar} 
            severity={snackbar.severity} 
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default PracticeConfigPage;
