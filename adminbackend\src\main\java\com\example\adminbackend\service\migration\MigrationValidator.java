package com.example.adminbackend.service.migration;

import com.example.adminbackend.dto.migration.SourceQuestionData;
import com.example.adminbackend.dto.migration.ValidationResult;
import com.example.adminbackend.model.Question;
import com.example.adminbackend.validator.QuestionBodyValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 迁移数据验证器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MigrationValidator {
    
    private final QuestionBodyValidator questionBodyValidator;
    
    // 支持的题目类型
    private static final List<String> SUPPORTED_QUESTION_TYPES = Arrays.asList(
        "SINGLE_CHOICE", "MULTIPLE_CHOICE", "FILL_IN_BLANK", "TRUE_FALSE",
        "READING_COMPREHENSION", "LISTENING", "CLOZE_TEST", "MATCHING"
    );
    
    // 支持的学科
    private static final List<String> SUPPORTED_SUBJECTS = Arrays.asList(
        "ENGLISH", "MATH", "PHYSICS", "CHEMISTRY", "BIOLOGY", "HISTORY", "GEOGRAPHY"
    );
    
    // 支持的难度
    private static final List<String> SUPPORTED_DIFFICULTIES = Arrays.asList(
        "EASY", "MEDIUM", "HARD"
    );
    
    /**
     * 验证源数据
     */
    public ValidationResult validateSourceData(SourceQuestionData sourceData) {
        ValidationResult result = ValidationResult.builder().valid(true).build();
        
        try {
            // 1. 基础字段验证
            validateBasicFields(sourceData, result);
            
            // 2. 题型特定验证
            validateQuestionTypeSpecific(sourceData, result);
            
            // 3. 层级信息验证
            validateHierarchyInfo(sourceData, result);
            
            // 4. 内容格式验证
            validateContentFormat(sourceData, result);
            
        } catch (Exception e) {
            log.error("验证源数据时发生异常", e);
            result.addError("验证过程发生异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证目标数据
     */
    public ValidationResult validateTargetData(Question question) {
        ValidationResult result = ValidationResult.builder().valid(true).build();
        
        try {
            // 1. 基础字段验证
            if (question.getQuestionType() == null) {
                result.addError("题目类型不能为空");
            }
            
            if (question.getKnowledgePointId() == null) {
                result.addError("知识点ID不能为空");
            }
            
            if (question.getBody() == null || question.getBody().isEmpty()) {
                result.addError("题目内容不能为空");
            }
            
            // 2. 使用现有的题目验证器验证body内容
            if (question.getBody() != null && !question.getBody().isEmpty()) {
                com.example.adminbackend.validator.ValidationResult bodyValidation =
                    questionBodyValidator.validateQuestionBody(question.getBody());

                if (!bodyValidation.isValid()) {
                    result.addError("题目内容验证失败: " + bodyValidation.getFirstErrorMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("验证目标数据时发生异常", e);
            result.addError("验证过程发生异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证基础字段
     */
    private void validateBasicFields(SourceQuestionData sourceData, ValidationResult result) {
        // 题目类型验证
        if (sourceData.getType() == null || sourceData.getType().trim().isEmpty()) {
            result.addError("题目类型不能为空");
        } else if (!SUPPORTED_QUESTION_TYPES.contains(sourceData.getType())) {
            result.addError("不支持的题目类型: " + sourceData.getType());
        }
        
        // 题目ID验证
        if (sourceData.getId() == null) {
            result.addError("题目ID不能为空");
        }
        
        // 学科验证
        if (sourceData.getSubject() == null || sourceData.getSubject().trim().isEmpty()) {
            result.addError("学科不能为空");
        } else if (!SUPPORTED_SUBJECTS.contains(sourceData.getSubject())) {
            result.addWarning("未知学科: " + sourceData.getSubject());
        }
        
        // 难度验证
        if (sourceData.getDifficulty() == null || sourceData.getDifficulty().trim().isEmpty()) {
            result.addError("难度不能为空");
        } else if (!SUPPORTED_DIFFICULTIES.contains(sourceData.getDifficulty())) {
            result.addWarning("未知难度: " + sourceData.getDifficulty());
        }
        
        // 内容验证
        if (sourceData.getContent() == null || sourceData.getContent().trim().isEmpty()) {
            result.addError("题目内容不能为空");
        }
    }
    
    /**
     * 验证题型特定字段
     */
    private void validateQuestionTypeSpecific(SourceQuestionData sourceData, ValidationResult result) {
        String questionType = sourceData.getType();
        
        if (questionType == null) {
            return; // 基础验证已经处理
        }
        
        switch (questionType) {
            case "SINGLE_CHOICE":
            case "MULTIPLE_CHOICE":
                // 选择题必须有选项和答案
                if (sourceData.getOptions() == null || sourceData.getOptions().isEmpty()) {
                    result.addError("选择题必须包含选项");
                }
                if (sourceData.getAnswer() == null) {
                    result.addError("选择题必须包含答案");
                }
                break;
                
            case "FILL_IN_BLANK":
                // 填空题必须有答案
                if (sourceData.getAnswer() == null) {
                    result.addError("填空题必须包含答案");
                }
                break;
                
            case "TRUE_FALSE":
                // 判断题必须有答案
                if (sourceData.getAnswer() == null) {
                    result.addError("判断题必须包含答案");
                }
                break;
                
            case "READING_COMPREHENSION":
            case "LISTENING":
            case "CLOZE_TEST":
                // 复合题型必须有材料和子题目
                if (sourceData.getMaterial() == null || sourceData.getMaterial().trim().isEmpty()) {
                    result.addError("复合题型必须包含材料内容");
                }
                if (sourceData.getSubQuestions() == null || sourceData.getSubQuestions().isEmpty()) {
                    result.addError("复合题型必须包含子题目");
                }
                break;
        }
    }
    
    /**
     * 验证层级信息
     */
    private void validateHierarchyInfo(SourceQuestionData sourceData, ValidationResult result) {
        if (sourceData.getBasicInfo() == null) {
            result.addError("基础层级信息不能为空");
            return;
        }
        
        var basicInfo = sourceData.getBasicInfo();
        
        // 学科信息验证
        if (basicInfo.getSubject() == null || basicInfo.getSubject().getName() == null) {
            result.addError("学科信息不完整");
        }
        
        // 版本信息验证
        if (basicInfo.getBookversion() == null || basicInfo.getBookversion().getName() == null) {
            result.addError("版本信息不完整");
        }
        
        // 章节信息验证
        if (basicInfo.getChapter() == null || basicInfo.getChapter().getName() == null) {
            result.addError("章节信息不完整");
        }
        
        // 知识点信息验证
        if (basicInfo.getKnowledgePoints() == null || basicInfo.getKnowledgePoints().isEmpty()) {
            result.addError("知识点信息不能为空");
        } else {
            var firstKp = basicInfo.getKnowledgePoints().get(0);
            if (firstKp.getName() == null || firstKp.getName().trim().isEmpty()) {
                result.addError("知识点名称不能为空");
            }
        }
    }
    
    /**
     * 验证内容格式
     */
    private void validateContentFormat(SourceQuestionData sourceData, ValidationResult result) {
        // 检查HTML内容是否包含危险标签
        if (sourceData.getContent() != null) {
            if (containsDangerousTags(sourceData.getContent())) {
                result.addWarning("题目内容包含可能不安全的HTML标签");
            }
        }
        
        if (sourceData.getExplanation() != null) {
            if (containsDangerousTags(sourceData.getExplanation())) {
                result.addWarning("解析内容包含可能不安全的HTML标签");
            }
        }
    }
    
    /**
     * 检查是否包含危险的HTML标签
     */
    private boolean containsDangerousTags(String content) {
        String[] dangerousTags = {"<script", "<iframe", "<object", "<embed", "<form"};
        String lowerContent = content.toLowerCase();
        
        for (String tag : dangerousTags) {
            if (lowerContent.contains(tag)) {
                return true;
            }
        }
        
        return false;
    }
}
