import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  FileDownload as FileDownloadIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import { questionAPI } from '../services/api';
import { QuestionImportResult } from '../types';
import { tableToJsonConverter } from '../utils/tableToJsonConverter';
import { TemplateGenerator } from '../utils/templateGenerator';

interface QuestionTableImportProps {
  open: boolean;
  onClose: () => void;
  knowledgePointId: number;
  onImportSuccess?: () => void;
}

interface TableRow {
  [key: string]: string | number;
}

interface ValidationError {
  row: number;
  column: string;
  message: string;
  severity: 'error' | 'warning';
}

const QuestionTableImport: React.FC<QuestionTableImportProps> = ({
  open,
  onClose,
  knowledgePointId,
  onImportSuccess,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [importResult, setImportResult] = useState<QuestionImportResult | null>(null);
  
  // 文件和数据状态
  const [fileName, setFileName] = useState<string>('');
  const [tableData, setTableData] = useState<TableRow[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [convertedQuestions, setConvertedQuestions] = useState<any[]>([]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const steps = ['上传文件', '数据预览', '数据验证', '确认导入'];

  // 必需的列名映射
  const requiredColumns = {
    '题目类型': 'type',
    '学科': 'subject',
    '难度': 'difficulty',
    '题目内容': 'content',
    '答案': 'answer',
    '解析': 'explanation'
  };

  // 可选的列名映射
  const optionalColumns = {
    '选项A': 'option_a',
    '选项B': 'option_b', 
    '选项C': 'option_c',
    '选项D': 'option_d',
    '选项E': 'option_e',
    '选项F': 'option_f',
    '标签': 'tags',
    '备注': 'remarks'
  };

  // 枚举值定义
  const enums = {
    type: ['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'FILL_IN_BLANK', 'TRUE_FALSE', 'READING_COMPREHENSION', 'LISTENING', 'CLOZE_TEST', 'MATCHING'],
    subject: ['ENGLISH', 'MATH', 'PHYSICS', 'CHEMISTRY', 'BIOLOGY', 'HISTORY', 'GEOGRAPHY'],
    difficulty: ['EASY', 'MEDIUM', 'HARD']
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setFileName(file.name);
    setError(null);
    setLoading(true);

    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (fileExtension === 'csv') {
      // 处理CSV文件
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        encoding: 'UTF-8',
        complete: (results) => {
          if (results.errors.length > 0) {
            setError('CSV文件解析错误: ' + results.errors[0].message);
            setLoading(false);
            return;
          }
          
          const data = results.data as TableRow[];
          const headers = Object.keys(data[0] || {});
          
          setHeaders(headers);
          setTableData(data);
          setActiveStep(1);
          setLoading(false);
        },
        error: (error) => {
          setError('CSV文件读取失败: ' + error.message);
          setLoading(false);
        }
      });
    } else if (['xlsx', 'xls'].includes(fileExtension || '')) {
      // 处理Excel文件
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // 读取第一个工作表
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          
          // 转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
          
          if (jsonData.length < 2) {
            setError('Excel文件数据不足，至少需要标题行和一行数据');
            setLoading(false);
            return;
          }
          
          // 第一行作为标题
          const headers = jsonData[0].map(h => String(h || '').trim()).filter(h => h);
          const dataRows = jsonData.slice(1).map(row => {
            const rowData: TableRow = {};
            headers.forEach((header, index) => {
              rowData[header] = row[index] || '';
            });
            return rowData;
          }).filter(row => Object.values(row).some(val => val !== ''));
          
          setHeaders(headers);
          setTableData(dataRows);
          setActiveStep(1);
          setLoading(false);
        } catch (error) {
          setError('Excel文件解析失败: ' + (error as Error).message);
          setLoading(false);
        }
      };
      reader.readAsArrayBuffer(file);
    } else {
      setError('不支持的文件格式，请上传CSV或Excel文件');
      setLoading(false);
    }
  };

  // 验证表格数据并转换为JSON
  const validateAndConvertData = () => {
    const errors: ValidationError[] = [];

    // 检查必需列是否存在
    Object.keys(requiredColumns).forEach(colName => {
      if (!headers.includes(colName)) {
        errors.push({
          row: 0,
          column: colName,
          message: `缺少必需列: ${colName}`,
          severity: 'error'
        });
      }
    });

    // 如果缺少必需列，直接返回
    if (errors.length > 0) {
      setValidationErrors(errors);
      return false;
    }

    // 使用转换器进行数据转换和验证
    const conversionResult = tableToJsonConverter.convertTableToJson(tableData);

    // 转换转换错误为验证错误格式
    conversionResult.errors.forEach(error => {
      const match = error.match(/第(\d+)行/);
      const rowNum = match ? parseInt(match[1]) : 0;
      errors.push({
        row: rowNum,
        column: '数据转换',
        message: error,
        severity: 'error'
      });
    });

    // 如果转换成功，进行额外验证
    if (conversionResult.success && conversionResult.data.length > 0) {
      const additionalErrors = tableToJsonConverter.validateConvertedData(conversionResult.data);
      additionalErrors.forEach(error => {
        const match = error.match(/第(\d+)行/);
        const rowNum = match ? parseInt(match[1]) : 0;
        errors.push({
          row: rowNum,
          column: '数据验证',
          message: error,
          severity: 'error'
        });
      });

      // 保存转换后的数据
      setConvertedQuestions(conversionResult.data);
    }

    setValidationErrors(errors);
    return errors.filter(e => e.severity === 'error').length === 0;
  };

  // 执行导入
  const handleImport = async () => {
    if (convertedQuestions.length === 0) {
      setError('没有可导入的数据');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 转换为后端期望的格式
      const questionBodies = convertedQuestions.map(q => ({
        id: q.id,
        type: q.type,
        subject: q.subject,
        difficulty: q.difficulty,
        content: q.content,
        answer: q.answer,
        explanation: q.explanation,
        ...(q.options && { options: q.options }),
        ...(q.material && { material: q.material }),
        ...(q.subQuestions && { subQuestions: q.subQuestions }),
        ...(q.tags && { tags: q.tags }),
        ...(q.remarks && { remarks: q.remarks })
      }));

      const result = await questionAPI.importQuestions(knowledgePointId, questionBodies);
      setImportResult(result);

      if (result.success > 0) {
        onImportSuccess?.();
      }
    } catch (err: any) {
      console.error('导入失败:', err);
      setError(err.message || '导入失败');
    } finally {
      setLoading(false);
    }
  };

  // 下载模板文件
  const downloadTemplate = () => {
    TemplateGenerator.generateQuestionImportTemplate();
  };

  // 重置状态
  const handleClose = () => {
    setActiveStep(0);
    setFileName('');
    setTableData([]);
    setHeaders([]);
    setValidationErrors([]);
    setConvertedQuestions([]);
    setError(null);
    setImportResult(null);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">表格批量导入题目</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<FileDownloadIcon />}
              onClick={downloadTemplate}
              variant="contained"
              size="small"
            >
              下载完整模板
            </Button>
            <Button
              startIcon={<FileDownloadIcon />}
              onClick={() => TemplateGenerator.generateSingleChoiceTemplate()}
              variant="outlined"
              size="small"
            >
              单选题模板
            </Button>
            <Button
              startIcon={<FileDownloadIcon />}
              onClick={() => TemplateGenerator.generateMultipleChoiceTemplate()}
              variant="outlined"
              size="small"
            >
              多选题模板
            </Button>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
            <Typography variant="body2" sx={{ mt: 1 }}>
              正在处理文件...
            </Typography>
          </Box>
        )}

        {/* 步骤1: 文件上传 */}
        {activeStep === 0 && (
          <Box>
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Box textAlign="center" py={4}>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileUpload}
                    accept=".xlsx,.xls,.csv"
                    style={{ display: 'none' }}
                  />
                  <CloudUploadIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    选择要导入的文件
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    支持 Excel (.xlsx, .xls) 和 CSV 格式
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => fileInputRef.current?.click()}
                    sx={{ mt: 2 }}
                  >
                    选择文件
                  </Button>
                  {fileName && (
                    <Typography variant="body2" sx={{ mt: 2 }}>
                      已选择文件: {fileName}
                    </Typography>
                  )}
                </Box>
              </CardContent>
            </Card>

            {/* 使用说明 */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📋 使用说明
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="第一步：下载模板"
                      secondary="点击上方的模板下载按钮，获取标准格式的Excel模板文件"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="第二步：填写数据"
                      secondary="在Excel中按照模板格式填写题目数据，注意必填字段不能为空"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="第三步：上传文件"
                      secondary="保存Excel文件后，点击上方按钮选择文件进行上传"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="第四步：验证导入"
                      secondary="系统会自动验证数据格式，确认无误后即可完成导入"
                    />
                  </ListItem>
                </List>

                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    💡 <strong>小贴士：</strong>建议先下载模板文件查看格式要求，然后按照示例数据填写。
                    支持单选题、多选题、填空题等多种题型。
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Box>
        )}

        {/* 步骤2: 数据预览 */}
        {activeStep === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              数据预览 ({tableData.length} 行数据)
            </Typography>
            <TableContainer component={Paper} sx={{ maxHeight: 400, mb: 2 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>行号</TableCell>
                    {headers.map((header) => (
                      <TableCell key={header}>
                        {header}
                        {Object.keys(requiredColumns).includes(header) && (
                          <Chip label="必需" size="small" color="primary" sx={{ ml: 1 }} />
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tableData.slice(0, 10).map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{index + 1}</TableCell>
                      {headers.map((header) => (
                        <TableCell key={header}>
                          {String(row[header] || '')}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            {tableData.length > 10 && (
              <Typography variant="body2" color="text.secondary">
                仅显示前10行数据，共{tableData.length}行
              </Typography>
            )}
          </Box>
        )}

        {/* 步骤3: 数据验证 */}
        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              数据验证结果
            </Typography>
            {validationErrors.length === 0 ? (
              <Alert severity="success" sx={{ mb: 2 }}>
                <Box display="flex" alignItems="center">
                  <CheckCircleIcon sx={{ mr: 1 }} />
                  数据验证通过，共 {tableData.length} 条记录
                </Box>
              </Alert>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  发现 {validationErrors.filter(e => e.severity === 'error').length} 个错误，
                  {validationErrors.filter(e => e.severity === 'warning').length} 个警告
                </Alert>
                <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {validationErrors.map((error, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {error.severity === 'error' ? (
                          <ErrorIcon color="error" />
                        ) : (
                          <WarningIcon color="warning" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={error.message}
                        secondary={`列: ${error.column}${error.row > 0 ? `, 行: ${error.row}` : ''}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        )}

        {/* 步骤4: 确认导入 */}
        {activeStep === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              确认导入
            </Typography>

            {!importResult && (
              <Box>
                <Alert severity="info" sx={{ mb: 2 }}>
                  即将导入 {convertedQuestions.length} 道题目到知识点 ID: {knowledgePointId}
                </Alert>

                {/* 显示统计信息 */}
                {convertedQuestions.length > 0 && (
                  <Card sx={{ mb: 2 }}>
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        数据统计
                      </Typography>
                      {(() => {
                        const stats = tableToJsonConverter.getConversionStats(convertedQuestions);
                        return (
                          <Box>
                            <Typography variant="body2" gutterBottom>
                              总计: {stats.total} 道题目
                            </Typography>

                            <Box sx={{ mt: 1 }}>
                              <Typography variant="body2" gutterBottom>题型分布:</Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {Object.entries(stats.byType).map(([type, count]) => (
                                  <Chip key={type} label={`${type}: ${count}`} size="small" />
                                ))}
                              </Box>
                            </Box>

                            <Box sx={{ mt: 1 }}>
                              <Typography variant="body2" gutterBottom>学科分布:</Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {Object.entries(stats.bySubject).map(([subject, count]) => (
                                  <Chip key={subject} label={`${subject}: ${count}`} size="small" />
                                ))}
                              </Box>
                            </Box>

                            <Box sx={{ mt: 1 }}>
                              <Typography variant="body2" gutterBottom>难度分布:</Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {Object.entries(stats.byDifficulty).map(([difficulty, count]) => (
                                  <Chip key={difficulty} label={`${difficulty}: ${count}`} size="small" />
                                ))}
                              </Box>
                            </Box>
                          </Box>
                        );
                      })()}
                    </CardContent>
                  </Card>
                )}
              </Box>
            )}

            {importResult && (
              <Alert
                severity={importResult.fail > 0 ? 'warning' : 'success'}
                sx={{ mb: 2 }}
              >
                <Typography variant="body2" gutterBottom>
                  导入完成：成功 {importResult.success} 个，失败 {importResult.fail} 个
                </Typography>
                {importResult.errors.length > 0 && (
                  <List dense>
                    {importResult.errors.slice(0, 5).map((error, index) => (
                      <ListItem key={index} sx={{ py: 0 }}>
                        <ListItemText
                          primary={error}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                    {importResult.errors.length > 5 && (
                      <Typography variant="body2">
                        还有 {importResult.errors.length - 5} 个错误...
                      </Typography>
                    )}
                  </List>
                )}
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {importResult ? '关闭' : '取消'}
        </Button>

        {activeStep > 0 && activeStep < 3 && !importResult && (
          <Button onClick={() => setActiveStep(activeStep - 1)}>
            上一步
          </Button>
        )}

        {activeStep === 1 && (
          <Button
            onClick={() => {
              if (validateAndConvertData()) {
                setActiveStep(2);
              } else {
                setActiveStep(2); // 即使有错误也进入验证步骤查看详情
              }
            }}
            variant="contained"
          >
            验证数据
          </Button>
        )}

        {activeStep === 2 && validationErrors.filter(e => e.severity === 'error').length === 0 && (
          <Button
            onClick={() => setActiveStep(3)}
            variant="contained"
          >
            下一步
          </Button>
        )}

        {activeStep === 3 && !importResult && (
          <Button
            onClick={handleImport}
            variant="contained"
            disabled={loading}
          >
            {loading ? '导入中...' : '确认导入'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default QuestionTableImport;
