package com.example.adminbackend.controller;

import com.example.adminbackend.dto.JwtResponse;
import com.example.adminbackend.dto.LoginRequest;
import com.example.adminbackend.security.JwtTokenUtil;
import com.example.adminbackend.model.User;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import org.springframework.web.bind.annotation.*;

@CrossOrigin(originPatterns = "*", maxAge = 3600, allowCredentials = "true")
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    


    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            // 直接使用手机号进行认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getPhone(), loginRequest.getPassword()));

            // 设置认证信息
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 获取认证用户
            User userDetails = (User) authentication.getPrincipal();

            // 生成JWT令牌
            String jwt = jwtTokenUtil.generateToken(userDetails);

            // 构建响应
            JwtResponse response = JwtResponse.builder()
                    .token(jwt)
                    .id(userDetails.getId())
                    .username(userDetails.getUsername())
                    .fullName(userDetails.getFullName())
                    .phone(userDetails.getPhone())
                    .role(userDetails.getRole())
                    .build();

            return ResponseEntity.ok(response);
        } catch (BadCredentialsException e) {
            throw new BadCredentialsException("手机号或密码错误");
        }
    }
} 