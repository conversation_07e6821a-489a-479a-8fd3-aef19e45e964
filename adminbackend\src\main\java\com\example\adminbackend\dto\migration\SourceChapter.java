package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Objects;

/**
 * 源数据章节信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceChapter {
    
    /**
     * 章节ID
     */
    private Long id;
    
    /**
     * 章节名称
     */
    private String name;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SourceChapter that = (SourceChapter) o;
        return Objects.equals(name, that.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
