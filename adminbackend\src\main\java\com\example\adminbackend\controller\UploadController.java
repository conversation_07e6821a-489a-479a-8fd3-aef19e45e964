package com.example.adminbackend.controller;

import com.example.adminbackend.service.QiniuUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/upload")
@CrossOrigin(originPatterns = "*")
@Slf4j
public class UploadController {
    
    @Autowired
    private QiniuUploadService qiniuUploadService;
    
    /**
     * 图片上传接口
     * 
     * @param file 图片文件
     * @return 上传结果
     */
    @PostMapping("/image")
    public ResponseEntity<?> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            log.info("收到图片上传请求: {}, 大小: {} bytes", 
                file.getOriginalFilename(), file.getSize());
            
            // 验证文件
            if (!qiniuUploadService.isValidImage(file)) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("无效的图片文件或文件过大"));
            }
            
            // 上传到七牛云
            String imageUrl = qiniuUploadService.uploadImage(file);
            
            // 返回成功结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "上传成功");
            result.put("url", imageUrl);
            result.put("filename", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("contentType", file.getContentType());
            
            log.info("图片上传成功: {}", imageUrl);
            return ResponseEntity.ok(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("图片上传参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(createErrorResponse(e.getMessage()));
                
        } catch (Exception e) {
            log.error("图片上传失败", e);
            return ResponseEntity.status(500)
                .body(createErrorResponse("上传失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取上传凭证接口（用于前端直传）
     * 
     * @return 上传凭证
     */
    @GetMapping("/token")
    public ResponseEntity<?> getUploadToken() {
        try {
            String token = qiniuUploadService.getUploadToken();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("token", token);
            result.put("expires", 3600); // 1小时过期
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取上传凭证失败", e);
            return ResponseEntity.status(500)
                .body(createErrorResponse("获取上传凭证失败: " + e.getMessage()));
        }
    }
    
    /**
     * 批量图片上传接口
     * 
     * @param files 图片文件数组
     * @return 上传结果
     */
    @PostMapping("/images")
    public ResponseEntity<?> uploadImages(@RequestParam("files") MultipartFile[] files) {
        try {
            if (files == null || files.length == 0) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("请选择要上传的文件"));
            }
            
            if (files.length > 10) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("一次最多上传10个文件"));
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "批量上传完成");
            result.put("total", files.length);
            
            Map<String, Object> results = new HashMap<>();
            int successCount = 0;
            
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                try {
                    if (qiniuUploadService.isValidImage(file)) {
                        String imageUrl = qiniuUploadService.uploadImage(file);
                        
                        Map<String, Object> fileResult = new HashMap<>();
                        fileResult.put("success", true);
                        fileResult.put("url", imageUrl);
                        fileResult.put("filename", file.getOriginalFilename());
                        fileResult.put("size", file.getSize());
                        
                        results.put("file_" + i, fileResult);
                        successCount++;
                    } else {
                        Map<String, Object> fileResult = new HashMap<>();
                        fileResult.put("success", false);
                        fileResult.put("error", "无效的图片文件");
                        fileResult.put("filename", file.getOriginalFilename());
                        
                        results.put("file_" + i, fileResult);
                    }
                } catch (Exception e) {
                    Map<String, Object> fileResult = new HashMap<>();
                    fileResult.put("success", false);
                    fileResult.put("error", e.getMessage());
                    fileResult.put("filename", file.getOriginalFilename());
                    
                    results.put("file_" + i, fileResult);
                }
            }
            
            result.put("results", results);
            result.put("successCount", successCount);
            result.put("failCount", files.length - successCount);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("批量图片上传失败", e);
            return ResponseEntity.status(500)
                .body(createErrorResponse("批量上传失败: " + e.getMessage()));
        }
    }
    
    /**
     * 视频上传接口
     *
     * @param file 视频文件
     * @return 上传结果
     */
    @PostMapping("/video")
    public ResponseEntity<?> uploadVideo(@RequestParam("file") MultipartFile file) {
        try {
            log.info("收到视频上传请求: {}, 大小: {} bytes",
                file.getOriginalFilename(), file.getSize());

            // 验证文件
            if (!qiniuUploadService.isValidVideo(file)) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("无效的视频文件或文件过大"));
            }

            // 上传到七牛云
            String videoUrl = qiniuUploadService.uploadVideo(file);

            // 返回成功结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "视频上传成功");
            result.put("url", videoUrl);
            result.put("filename", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("contentType", file.getContentType());

            log.info("视频上传成功: {}", videoUrl);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            log.warn("视频上传参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(createErrorResponse(e.getMessage()));

        } catch (Exception e) {
            log.error("视频上传失败", e);
            return ResponseEntity.status(500)
                .body(createErrorResponse("上传失败: " + e.getMessage()));
        }
    }

    /**
     * 检查文件是否为有效图片
     *
     * @param file 文件
     * @return 检查结果
     */
    @PostMapping("/validate")
    public ResponseEntity<?> validateImage(@RequestParam("file") MultipartFile file) {
        try {
            boolean isValid = qiniuUploadService.isValidImage(file);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            result.put("filename", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("contentType", file.getContentType());

            if (!isValid) {
                try {
                    qiniuUploadService.validateImageFile(file);
                } catch (Exception e) {
                    result.put("error", e.getMessage());
                }
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(createErrorResponse("文件验证失败: " + e.getMessage()));
        }
    }

    /**
     * 检查文件是否为有效视频
     *
     * @param file 文件
     * @return 检查结果
     */
    @PostMapping("/validate-video")
    public ResponseEntity<?> validateVideo(@RequestParam("file") MultipartFile file) {
        try {
            boolean isValid = qiniuUploadService.isValidVideo(file);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            result.put("filename", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("contentType", file.getContentType());

            if (!isValid) {
                try {
                    qiniuUploadService.validateVideoFile(file);
                } catch (Exception e) {
                    result.put("error", e.getMessage());
                }
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(createErrorResponse("文件验证失败: " + e.getMessage()));
        }
    }

    /**
     * 音频上传接口
     *
     * @param file 音频文件
     * @return 上传结果
     */
    @PostMapping("/audio")
    public ResponseEntity<?> uploadAudio(@RequestParam("file") MultipartFile file) {
        try {
            log.info("收到音频上传请求: {}, 大小: {} bytes",
                file.getOriginalFilename(), file.getSize());

            // 上传到七牛云
            String audioUrl = qiniuUploadService.uploadAudio(file);

            // 返回成功结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "音频上传成功");
            result.put("url", audioUrl);
            result.put("filename", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("contentType", file.getContentType());

            log.info("音频上传成功: {}", audioUrl);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("音频上传失败", e);
            return ResponseEntity.status(500)
                .body(createErrorResponse("上传失败: " + e.getMessage()));
        }
    }

    /**
     * 测试七牛云配置接口
     *
     * @return 配置信息
     */
    @GetMapping("/test-config")
    public ResponseEntity<?> testQiniuConfig() {
        try {
            String configInfo = qiniuUploadService.testQiniuConfig();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "配置检查完成");
            result.put("config", configInfo);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("配置检查失败", e);
            return ResponseEntity.status(500)
                .body(createErrorResponse("配置检查失败: " + e.getMessage()));
        }
    }


    
    /**
     * 创建错误响应
     * 
     * @param message 错误信息
     * @return 错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        error.put("timestamp", System.currentTimeMillis());
        return error;
    }
}
