package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批次处理结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchResult {
    
    /**
     * 批次编号
     */
    private int batchNumber;
    
    /**
     * 批次大小
     */
    private int batchSize;
    
    /**
     * 成功数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int errorCount;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 耗时（毫秒）
     */
    private long durationMs;
    
    /**
     * 错误信息映射 (题目ID -> 错误信息)
     */
    @Builder.Default
    private Map<String, String> errors = new HashMap<>();
    
    /**
     * 成功的题目ID列表
     */
    @Builder.Default
    private List<String> successIds = new ArrayList<>();
    
    /**
     * 增加成功计数
     */
    public void incrementSuccess() {
        this.successCount++;
    }
    
    /**
     * 增加错误计数并记录错误信息
     */
    public void addError(String questionId, String errorMessage) {
        this.errorCount++;
        this.errors.put(questionId, errorMessage);
    }
    
    /**
     * 添加成功的题目ID
     */
    public void addSuccessId(String questionId) {
        this.successIds.add(questionId);
    }
    
    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (batchSize == 0) {
            return 0.0;
        }
        return (double) successCount / batchSize * 100;
    }
}
