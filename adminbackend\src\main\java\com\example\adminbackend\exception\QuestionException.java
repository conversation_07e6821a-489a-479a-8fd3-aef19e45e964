package com.example.adminbackend.exception;

import com.example.adminbackend.dto.ErrorDetail;

import java.util.List;

/**
 * 题目相关异常类
 */
public class QuestionException extends BusinessException {
    
    /**
     * 构造函数 - 使用错误码
     */
    public QuestionException(ErrorCode errorCode) {
        super(errorCode);
    }
    
    /**
     * 构造函数 - 使用错误码和自定义消息
     */
    public QuestionException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }
    
    /**
     * 构造函数 - 使用错误码、消息和原因
     */
    public QuestionException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
    
    /**
     * 构造函数 - 使用错误码和错误详情
     */
    public QuestionException(ErrorCode errorCode, List<ErrorDetail> errorDetails) {
        super(errorCode, errorDetails);
    }
    
    /**
     * 构造函数 - 使用错误码、消息和错误详情
     */
    public QuestionException(ErrorCode errorCode, String message, List<ErrorDetail> errorDetails) {
        super(errorCode, message, errorDetails);
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 题目不存在异常
     */
    public static QuestionException notFound(Long questionId) {
        return new QuestionException(ErrorCode.QUESTION_NOT_FOUND, "题目不存在: " + questionId);
    }
    
    /**
     * 题目类型无效异常
     */
    public static QuestionException invalidType(String questionType) {
        return new QuestionException(ErrorCode.QUESTION_TYPE_INVALID, "题目类型无效: " + questionType);
    }
    
    /**
     * 题目JSON格式无效异常
     */
    public static QuestionException invalidJson(String reason) {
        return new QuestionException(ErrorCode.QUESTION_JSON_INVALID, "题目JSON格式无效: " + reason);
    }
    
    /**
     * 题目JSON格式无效异常（带详细错误）
     */
    public static QuestionException invalidJson(String reason, List<ErrorDetail> errorDetails) {
        return new QuestionException(ErrorCode.QUESTION_JSON_INVALID, "题目JSON格式无效: " + reason, errorDetails);
    }
    
    /**
     * 题目内容格式无效异常
     */
    public static QuestionException invalidContent(String reason) {
        return new QuestionException(ErrorCode.QUESTION_CONTENT_INVALID, "题目内容格式无效: " + reason);
    }
    
    /**
     * 题目答案格式无效异常
     */
    public static QuestionException invalidAnswer(String reason) {
        return new QuestionException(ErrorCode.QUESTION_ANSWER_INVALID, "题目答案格式无效: " + reason);
    }
    
    /**
     * 题目选项格式无效异常
     */
    public static QuestionException invalidOptions(String reason) {
        return new QuestionException(ErrorCode.QUESTION_OPTIONS_INVALID, "题目选项格式无效: " + reason);
    }
    
    /**
     * 题目难度无效异常
     */
    public static QuestionException invalidDifficulty(String difficulty) {
        return new QuestionException(ErrorCode.QUESTION_DIFFICULTY_INVALID, "题目难度无效: " + difficulty);
    }
    
    /**
     * 题目科目无效异常
     */
    public static QuestionException invalidSubject(String subject) {
        return new QuestionException(ErrorCode.QUESTION_SUBJECT_INVALID, "题目科目无效: " + subject);
    }
    
    /**
     * 知识点不存在异常
     */
    public static QuestionException knowledgePointNotFound(Long knowledgePointId) {
        return new QuestionException(ErrorCode.QUESTION_KNOWLEDGE_POINT_NOT_FOUND, "知识点不存在: " + knowledgePointId);
    }
    
    /**
     * 题目导入失败异常
     */
    public static QuestionException importFailed(String reason) {
        return new QuestionException(ErrorCode.QUESTION_IMPORT_FAILED, "题目导入失败: " + reason);
    }
    
    /**
     * 题目导入失败异常（带详细错误）
     */
    public static QuestionException importFailed(String reason, List<ErrorDetail> errorDetails) {
        return new QuestionException(ErrorCode.QUESTION_IMPORT_FAILED, "题目导入失败: " + reason, errorDetails);
    }
    
    /**
     * 题目导出失败异常
     */
    public static QuestionException exportFailed(String reason) {
        return new QuestionException(ErrorCode.QUESTION_EXPORT_FAILED, "题目导出失败: " + reason);
    }
    
    /**
     * 批量删除题目失败异常
     */
    public static QuestionException batchDeleteFailed(String reason) {
        return new QuestionException(ErrorCode.QUESTION_BATCH_DELETE_FAILED, "批量删除题目失败: " + reason);
    }
    
    /**
     * 批量删除题目失败异常（带详细错误）
     */
    public static QuestionException batchDeleteFailed(String reason, List<ErrorDetail> errorDetails) {
        return new QuestionException(ErrorCode.QUESTION_BATCH_DELETE_FAILED, "批量删除题目失败: " + reason, errorDetails);
    }
}
