/**
 * 数据迁移相关的类型定义
 */

// 源数据结构
export interface SourceQuestionData {
  type: string;
  id: number;
  subject: string;
  difficulty: string;
  content: string;
  options?: string[];
  answer?: any;
  explanation?: string;
  tags?: string[];
  material?: string;
  subQuestions?: SourceSubQuestion[];
  basicInfo: BasicInfo;
}

export interface SourceSubQuestion {
  type: string;
  id: string;
  content: string;
  options?: string[];
  answer?: any;
  explanation?: string;
}

export interface BasicInfo {
  subject: SourceSubject;
  bookversion: SourceBookVersion;
  chapter: SourceChapter;
  knowledgePoints: SourceKnowledgePoint[];
}

export interface SourceSubject {
  id: number;
  name: string;
}

export interface SourceBookVersion {
  id: string;
  name: string;
  grade: string;
}

export interface SourceChapter {
  id: number;
  name: string;
}

export interface SourceKnowledgePoint {
  id: number;
  name: string;
  video?: string;
}

// 迁移结果
export interface MigrationResult {
  success: boolean;
  totalCount: number;
  successCount: number;
  errorCount: number;
  skipCount?: number;
  startTime: string;
  endTime: string;
  durationMs: number;
  errors: string[];
  statistics?: Record<string, any>;
  message: string;
  batchResults?: BatchResult[];
  successRate?: number;
  processingSpeed?: number;
}

export interface BatchResult {
  batchNumber: number;
  batchSize: number;
  successCount: number;
  errorCount: number;
  startTime: string;
  endTime: string;
  durationMs: number;
  errors: Record<string, string>;
  successIds: string[];
  successRate?: number;
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  errorMessage?: string;
}

// 迁移状态
export enum MigrationStatus {
  IDLE = 'idle',
  VALIDATING = 'validating',
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 迁移配置
export interface MigrationConfig {
  batchSize?: number;
  threadPoolSize?: number;
  enableStrictValidation?: boolean;
  skipInvalidData?: boolean;
  maxErrorCount?: number;
}



// 迁移进度
export interface MigrationProgress {
  status: MigrationStatus;
  currentBatch?: number;
  totalBatches?: number;
  processedCount: number;
  totalCount: number;
  errorCount: number;
  startTime?: string;
  estimatedTimeRemaining?: number;
  message?: string;
}

// API响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 迁移统计信息
export interface MigrationStatistics {
  totalMigrations: number;
  successfulMigrations: number;
  failedMigrations: number;
  totalQuestionsProcessed: number;
  averageProcessingTime: number;
  lastMigrationTime?: string;
}

// 系统状态
export interface SystemStatus {
  systemReady: boolean;
  timestamp: number;
  version: string;
  systemInfo: {
    totalMemory: number;
    freeMemory: number;
    maxMemory: number;
    availableProcessors: number;
  };
}

// 测试数据生成选项
export interface TestDataOptions {
  count: number;
  includeComplexTypes?: boolean;
  subjects?: string[];
  difficulties?: string[];
}

// 性能测试结果
export interface PerformanceTestResult {
  dataCount: number;
  generateTimeMs: number;
  migrationTimeMs: number;
  totalTimeMs: number;
  throughputPerSecond: number;
  migrationResult: MigrationResult;
}

// 错误详情
export interface MigrationError {
  questionId: string;
  errorType: 'validation' | 'conversion' | 'database' | 'system';
  errorMessage: string;
  timestamp: string;
  details?: any;
}

// 迁移历史记录
export interface MigrationHistory {
  id: string;
  startTime: string;
  endTime: string;
  status: MigrationStatus;
  totalCount: number;
  successCount: number;
  errorCount: number;
  duration: number;
  operator: string;
  notes?: string;
}

// 数据源类型
export enum DataSourceType {
  JSON_TEXT = 'json_text',
  URL = 'url'
}

// 迁移任务
export interface MigrationTask {
  id: string;
  name: string;
  description?: string;
  dataSource: DataSourceType;
  sourceData?: any;
  config: MigrationConfig;
  status: MigrationStatus;
  progress: MigrationProgress;
  result?: MigrationResult;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

// 导出类型
export * from './migration';
