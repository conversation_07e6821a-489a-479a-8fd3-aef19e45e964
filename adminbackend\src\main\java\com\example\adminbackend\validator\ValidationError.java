package com.example.adminbackend.validator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证错误详情类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationError {
    
    /**
     * 错误字段路径，如 "answer", "subQuestions[0].content"
     */
    private String fieldPath;
    
    /**
     * 错误类型
     */
    private String errorType;
    
    /**
     * 当前值
     */
    private String currentValue;
    
    /**
     * 期望格式
     */
    private String expectedFormat;
    
    /**
     * 修复建议
     */
    private String fixSuggestion;
    
    /**
     * 是否可自动修复
     */
    private boolean autoFixable;
    
    /**
     * 获取详细错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("字段: ").append(fieldPath);
        sb.append(", 错误类型: ").append(errorType);
        sb.append(", 当前值: ").append(currentValue);
        sb.append(", 期望格式: ").append(expectedFormat);
        sb.append(", 修复建议: ").append(fixSuggestion);
        if (autoFixable) {
            sb.append(" (可自动修复)");
        }
        return sb.toString();
    }
}
