import React, { useEffect, useState } from 'react';
import MathQuillKeyboard, { GlobalKeyboardController } from './keyborad/MathQuillKeyboard';

/**
 * 全局键盘组件
 * 确保整个应用只有一个键盘实例，避免冲突
 */
const GlobalKeyboard: React.FC = () => {
  const [keyboardState, setKeyboardState] = useState(() => GlobalKeyboardController.getState());

  useEffect(() => {
    const unsubscribe = GlobalKeyboardController.subscribe((newState) => {
      setKeyboardState({ ...newState });
    });

    return unsubscribe;
  }, []);

  // 监听 MathLive 输入框的键盘显示事件
  useEffect(() => {
    const handleShowMathKeyboard = (event: CustomEvent) => {
      const { mathField } = event.detail;

      // 创建按键处理函数
      const handleKeyPress = (keyData: any) => {
        if (!mathField) return;

        try {
          // 根据按键类型处理输入
          switch (keyData.type) {
            case 'number':
            case 'letter':
              // 数字和字母直接插入
              if (keyData.insert) {
                mathField.insert(keyData.insert);
              }
              break;

            case 'operator':
              // 运算符处理
              if (keyData.insert) {
                mathField.insert(keyData.insert);
              }
              break;

            case 'function':
            case 'power':
            case 'special':
              // 函数、幂次、特殊符号使用模板或LaTeX
              if (keyData.template) {
                mathField.insert(keyData.template);
              } else if (keyData.insert) {
                mathField.insert(keyData.insert);
              } else if (keyData.latex) {
                mathField.insert(keyData.latex);
              }
              break;

            case 'navigation':
              // 导航命令
              if (Array.isArray(keyData.command)) {
                keyData.command.forEach((cmd: string) => {
                  if (cmd === 'deleteBackward') {
                    mathField.executeCommand('deleteBackward');
                  } else if (cmd === 'moveLeft') {
                    mathField.executeCommand('moveToPreviousChar');
                  } else if (cmd === 'moveRight') {
                    mathField.executeCommand('moveToNextChar');
                  }
                });
              } else if (keyData.command) {
                if (keyData.command === 'deleteBackward') {
                  mathField.executeCommand('deleteBackward');
                } else if (keyData.command === 'moveLeft') {
                  mathField.executeCommand('moveToPreviousChar');
                } else if (keyData.command === 'moveRight') {
                  mathField.executeCommand('moveToNextChar');
                }
              }
              break;

            default:
              // 默认处理：尝试插入
              if (keyData.insert) {
                mathField.insert(keyData.insert);
              } else if (keyData.latex) {
                mathField.insert(keyData.latex);
              }
          }
        } catch (error) {
          console.error('键盘输入处理错误:', error);
        }
      };

      // 显示键盘
      GlobalKeyboardController.show(mathField, handleKeyPress);
    };

    // 添加事件监听器
    document.addEventListener('showMathKeyboard', handleShowMathKeyboard as EventListener);

    // 清理函数
    return () => {
      document.removeEventListener('showMathKeyboard', handleShowMathKeyboard as EventListener);
    };
  }, []);

  // 添加全局样式确保键盘在所有Dialog之上
  useEffect(() => {
    const existingStyle = document.getElementById('global-keyboard-zindex');
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = 'global-keyboard-zindex';
      style.textContent = `
        /* 确保自定义键盘在所有Material-UI组件之上 */
        .keyboard-overlay {
          z-index: 2000 !important;
        }
        .mathquill-keyboard {
          z-index: 2001 !important;
        }
        .mathquill-keyboard .keyboard-key {
          z-index: 2002 !important;
        }
        .keyboard-close-btn {
          z-index: 2003 !important;
        }
        .math-panel,
        .numeric-panel,
        .english-keyboard-panel {
          z-index: 2001 !important;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const style = document.getElementById('global-keyboard-zindex');
      if (style) {
        style.remove();
      }
    };
  }, []);

  const handleKeyPress = (keyData: any) => {
    // 调用当前注册的按键处理函数
    if (keyboardState.onKeyPress) {
      keyboardState.onKeyPress(keyData);
    }
  };

  const handleClose = () => {
    GlobalKeyboardController.hide();
  };

  return (
    <MathQuillKeyboard
      visible={keyboardState.visible}
      onKeyPress={handleKeyPress}
      onClose={handleClose}
      targetMathField={keyboardState.targetField}
      autoHandle={false}
    />
  );
};

export default GlobalKeyboard;
