package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {
    
    /**
     * 验证是否通过
     */
    private boolean valid;
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 添加错误信息
     */
    public void addError(String error) {
        this.errors.add(error);
        this.valid = false;
    }
    
    /**
     * 添加警告信息
     */
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    /**
     * 获取第一个错误信息
     */
    public String getFirstErrorMessage() {
        return errors.isEmpty() ? null : errors.get(0);
    }
    
    /**
     * 获取所有错误信息的字符串
     */
    public String getErrorMessage() {
        return String.join("; ", errors);
    }
    
    /**
     * 创建成功的验证结果
     */
    public static ValidationResult success() {
        return ValidationResult.builder()
                .valid(true)
                .build();
    }
    
    /**
     * 创建失败的验证结果
     */
    public static ValidationResult failure(String error) {
        ValidationResult result = new ValidationResult();
        result.addError(error);
        return result;
    }
}
