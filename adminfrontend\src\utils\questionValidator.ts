import { QuestionBody, QuestionType, SubjectEnum, Difficulty } from '../types';

/**
 * 验证错误详情接口
 */
export interface ValidationError {
  fieldPath: string;
  errorType: string;
  currentValue: any;
  expectedFormat: string;
  fixSuggestion: string;
  autoFixable: boolean;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean;
  message: string;
  errors: ValidationError[];
}

/**
 * 题目Body字段完整验证器
 * 严格按照《Questions表Body字段JSON数据格式规范》执行验证
 */
export class QuestionBodyValidator {
  
  /**
   * 验证题目Body的完整性
   */
  validateQuestionBody(questionBody: any): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      message: '',
      errors: []
    };

    try {
      // 1. 验证所有必需字段
      if (!this.validateRequiredFields(questionBody, result)) {
        return result;
      }

      // 2. 验证字段类型和格式
      if (!this.validateFieldTypes(questionBody, result)) {
        return result;
      }

      // 3. 验证题型特定字段
      if (!this.validateQuestionTypeSpecific(questionBody, result)) {
        return result;
      }

      // 4. 验证答案格式
      if (!this.validateAnswerFormat(questionBody, result)) {
        return result;
      }

      // 5. 验证HTML内容安全性
      if (!this.validateHtmlContent(questionBody, result)) {
        return result;
      }

      result.valid = true;
      result.message = '验证通过';

    } catch (error) {
      console.error('验证题目Body时发生异常', error);
      result.valid = false;
      result.message = `验证过程中发生异常: ${error}`;
    }

    return result;
  }

  /**
   * 验证所有必需字段
   */
  private validateRequiredFields(questionBody: any, result: ValidationResult): boolean {
    const requiredFields = ['type', 'id', 'subject', 'difficulty', 'tags', 'content', 'answer', 'explanation'];

    for (const field of requiredFields) {
      if (!(field in questionBody)) {
        this.addError(result, {
          fieldPath: field,
          errorType: 'MISSING_FIELD',
          currentValue: null,
          expectedFormat: '必需字段',
          fixSuggestion: `缺少必需字段: ${field}`,
          autoFixable: false
        });
        return false;
      }

      const value = questionBody[field];
      if (value === null || value === undefined) {
        this.addError(result, {
          fieldPath: field,
          errorType: 'NULL_VALUE',
          currentValue: 'null',
          expectedFormat: '非空值',
          fixSuggestion: `字段不能为空: ${field}`,
          autoFixable: false
        });
        return false;
      }
    }

    return true;
  }

  /**
   * 验证字段类型和格式
   */
  private validateFieldTypes(questionBody: any, result: ValidationResult): boolean {
    // 验证type字段
    const type = questionBody.type;
    const validTypes: QuestionType[] = [
      'SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'FILL_IN_BLANK', 'TRUE_FALSE',
      'MATCHING', 'READING_COMPREHENSION', 'CLOZE_TEST', 'LISTENING'
    ];
    if (!validTypes.includes(type)) {
      this.addError(result, {
        fieldPath: 'type',
        errorType: 'INVALID_ENUM',
        currentValue: type,
        expectedFormat: '有效的题目类型枚举值',
        fixSuggestion: `无效的题目类型: ${type}`,
        autoFixable: false
      });
      return false;
    }

    // 验证subject字段
    const subject = questionBody.subject;
    const validSubjects: SubjectEnum[] = ['ENGLISH', 'MATH', 'PHYSICS', 'CHEMISTRY'];
    if (!validSubjects.includes(subject)) {
      this.addError(result, {
        fieldPath: 'subject',
        errorType: 'INVALID_ENUM',
        currentValue: subject,
        expectedFormat: '有效的科目枚举值(ENGLISH/MATH/PHYSICS/CHEMISTRY)',
        fixSuggestion: `无效的科目: ${subject}`,
        autoFixable: false
      });
      return false;
    }

    // 验证difficulty字段
    const difficulty = questionBody.difficulty;
    const validDifficulties: Difficulty[] = ['EASY', 'MEDIUM', 'HARD'];
    if (!validDifficulties.includes(difficulty)) {
      this.addError(result, {
        fieldPath: 'difficulty',
        errorType: 'INVALID_ENUM',
        currentValue: difficulty,
        expectedFormat: '有效的难度枚举值(EASY/MEDIUM/HARD)',
        fixSuggestion: `无效的难度: ${difficulty}`,
        autoFixable: false
      });
      return false;
    }

    // 验证id字段
    const id = questionBody.id;
    if (typeof id !== 'string' || id.trim() === '') {
      this.addError(result, {
        fieldPath: 'id',
        errorType: 'EMPTY_STRING',
        currentValue: id,
        expectedFormat: '非空字符串',
        fixSuggestion: '题目ID不能为空',
        autoFixable: false
      });
      return false;
    }

    // 验证tags字段
    const tags = questionBody.tags;
    if (!Array.isArray(tags)) {
      this.addError(result, {
        fieldPath: 'tags',
        errorType: 'INVALID_TYPE',
        currentValue: typeof tags,
        expectedFormat: '字符串数组',
        fixSuggestion: 'tags字段必须是字符串数组',
        autoFixable: false
      });
      return false;
    }

    // 验证content字段
    const content = questionBody.content;
    if (typeof content !== 'string' || content.trim() === '') {
      this.addError(result, {
        fieldPath: 'content',
        errorType: 'EMPTY_STRING',
        currentValue: content,
        expectedFormat: '非空HTML字符串',
        fixSuggestion: '题目内容不能为空',
        autoFixable: false
      });
      return false;
    }

    // 验证explanation字段
    const explanation = questionBody.explanation;
    if (typeof explanation !== 'string' || explanation.trim() === '') {
      this.addError(result, {
        fieldPath: 'explanation',
        errorType: 'EMPTY_STRING',
        currentValue: explanation,
        expectedFormat: '非空HTML字符串',
        fixSuggestion: '题目解析不能为空',
        autoFixable: false
      });
      return false;
    }

    return true;
  }

  /**
   * 验证题型特定字段
   */
  private validateQuestionTypeSpecific(questionBody: any, result: ValidationResult): boolean {
    const type: QuestionType = questionBody.type;

    switch (type) {
      case 'SINGLE_CHOICE':
      case 'MULTIPLE_CHOICE':
        return this.validateChoiceQuestion(questionBody, result);
      case 'READING_COMPREHENSION':
      case 'CLOZE_TEST':
      case 'LISTENING':
        return this.validateNestedQuestion(questionBody, result);
      case 'FILL_IN_BLANK':
      case 'TRUE_FALSE':
      case 'MATCHING':
        // 简单题型，只需要基础字段验证
        return true;
      default:
        return true;
    }
  }

  /**
   * 验证选择题特定字段
   */
  private validateChoiceQuestion(questionBody: any, result: ValidationResult): boolean {
    // 验证options字段
    const options = questionBody.options;
    if (!options) {
      this.addError(result, {
        fieldPath: 'options',
        errorType: 'MISSING_FIELD',
        currentValue: null,
        expectedFormat: '字符串数组',
        fixSuggestion: '选择题必须包含options字段',
        autoFixable: false
      });
      return false;
    }

    if (!Array.isArray(options)) {
      this.addError(result, {
        fieldPath: 'options',
        errorType: 'INVALID_TYPE',
        currentValue: typeof options,
        expectedFormat: '字符串数组',
        fixSuggestion: 'options字段必须是字符串数组',
        autoFixable: false
      });
      return false;
    }

    if (options.length < 2) {
      this.addError(result, {
        fieldPath: 'options',
        errorType: 'INSUFFICIENT_OPTIONS',
        currentValue: options.length.toString(),
        expectedFormat: '至少2个选项',
        fixSuggestion: '选择题至少需要2个选项',
        autoFixable: false
      });
      return false;
    }

    // 验证选项内容不为空
    for (let i = 0; i < options.length; i++) {
      const option = options[i];
      if (typeof option !== 'string' || option.trim() === '') {
        this.addError(result, {
          fieldPath: `options[${i}]`,
          errorType: 'EMPTY_OPTION',
          currentValue: option,
          expectedFormat: '非空字符串',
          fixSuggestion: '选项内容不能为空',
          autoFixable: false
        });
        return false;
      }
    }

    return true;
  }

  /**
   * 验证嵌套题型特定字段
   */
  private validateNestedQuestion(questionBody: any, result: ValidationResult): boolean {
    const type = questionBody.type;

    // 只有阅读理解和完形填空需要material字段
    if (type === 'READING_COMPREHENSION' || type === 'CLOZE_TEST') {
      const material = questionBody.material;
      if (typeof material !== 'string' || material.trim() === '') {
        const typeName = type === 'READING_COMPREHENSION' ? '阅读理解' : '完形填空';
        this.addError(result, {
          fieldPath: 'material',
          errorType: 'MISSING_FIELD',
          currentValue: material,
          expectedFormat: '非空HTML字符串',
          fixSuggestion: `${typeName}必须包含material字段`,
          autoFixable: false
        });
        return false;
      }
    }
    // 听力题不需要material字段，音频内容在content字段中

    // 验证subQuestions字段
    const subQuestions = questionBody.subQuestions;
    if (!subQuestions) {
      this.addError(result, {
        fieldPath: 'subQuestions',
        errorType: 'MISSING_FIELD',
        currentValue: null,
        expectedFormat: '子题目数组',
        fixSuggestion: '嵌套题型必须包含subQuestions字段',
        autoFixable: false
      });
      return false;
    }

    if (!Array.isArray(subQuestions)) {
      this.addError(result, {
        fieldPath: 'subQuestions',
        errorType: 'INVALID_TYPE',
        currentValue: typeof subQuestions,
        expectedFormat: '子题目数组',
        fixSuggestion: 'subQuestions字段必须是数组',
        autoFixable: false
      });
      return false;
    }

    if (subQuestions.length === 0) {
      this.addError(result, {
        fieldPath: 'subQuestions',
        errorType: 'EMPTY_ARRAY',
        currentValue: '[]',
        expectedFormat: '至少1个子题目',
        fixSuggestion: '嵌套题型至少需要1个子题目',
        autoFixable: false
      });
      return false;
    }

    // 验证每个子题目
    for (let i = 0; i < subQuestions.length; i++) {
      if (!this.validateSubQuestion(subQuestions[i], i, result)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 验证子题目
   */
  private validateSubQuestion(subQuestion: any, index: number, result: ValidationResult): boolean {
    const prefix = `subQuestions[${index}]`;

    // 验证子题目必需字段
    const requiredFields = ['id', 'content', 'answer'];
    for (const field of requiredFields) {
      if (!(field in subQuestion) || subQuestion[field] === null || subQuestion[field] === undefined) {
        this.addError(result, {
          fieldPath: `${prefix}.${field}`,
          errorType: 'MISSING_FIELD',
          currentValue: null,
          expectedFormat: '必需字段',
          fixSuggestion: `子题目缺少必需字段: ${field}`,
          autoFixable: false
        });
        return false;
      }
    }

    // 验证子题目内容不为空
    const content = subQuestion.content;
    if (typeof content !== 'string' || content.trim() === '') {
      this.addError(result, {
        fieldPath: `${prefix}.content`,
        errorType: 'EMPTY_STRING',
        currentValue: content,
        expectedFormat: '非空字符串',
        fixSuggestion: '子题目内容不能为空',
        autoFixable: false
      });
      return false;
    }

    return true;
  }

  /**
   * 验证答案格式
   */
  private validateAnswerFormat(questionBody: any, result: ValidationResult): boolean {
    const type: QuestionType = questionBody.type;
    const answer = questionBody.answer;

    switch (type) {
      case 'SINGLE_CHOICE':
        return this.validateSingleChoiceAnswer(answer, questionBody, result);
      case 'MULTIPLE_CHOICE':
        return this.validateMultipleChoiceAnswer(answer, questionBody, result);
      case 'FILL_IN_BLANK':
        return this.validateFillInBlankAnswer(answer, result);
      case 'TRUE_FALSE':
        return this.validateTrueFalseAnswer(answer, result);
      case 'READING_COMPREHENSION':
      case 'CLOZE_TEST':
      case 'LISTENING':
        // 嵌套题型的答案在子题目中验证
        return true;
      case 'MATCHING':
        // 匹配题答案格式验证
        return true;
      default:
        return true;
    }
  }

  /**
   * 验证单选题答案格式
   */
  private validateSingleChoiceAnswer(answer: any, questionBody: any, result: ValidationResult): boolean {
    if (typeof answer !== 'string') {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'INVALID_TYPE',
        currentValue: typeof answer,
        expectedFormat: '字母字符串(如 "A", "B")',
        fixSuggestion: '单选题答案必须是字母字符串',
        autoFixable: false
      });
      return false;
    }

    const choicePattern = /^[A-Z]$/;
    if (!choicePattern.test(answer)) {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'INVALID_FORMAT',
        currentValue: answer,
        expectedFormat: 'A-Z字母',
        fixSuggestion: '单选题答案必须是A-Z字母',
        autoFixable: false
      });
      return false;
    }

    // 验证答案是否在选项范围内
    const options = questionBody.options;
    if (options && Array.isArray(options)) {
      const answerIndex = answer.charCodeAt(0) - 'A'.charCodeAt(0);
      if (answerIndex >= options.length) {
        this.addError(result, {
          fieldPath: 'answer',
          errorType: 'OUT_OF_RANGE',
          currentValue: answer,
          expectedFormat: `A-${String.fromCharCode('A'.charCodeAt(0) + options.length - 1)}`,
          fixSuggestion: '答案超出选项范围',
          autoFixable: false
        });
        return false;
      }
    }

    return true;
  }

  /**
   * 验证多选题答案格式
   */
  private validateMultipleChoiceAnswer(answer: any, questionBody: any, result: ValidationResult): boolean {
    if (!Array.isArray(answer)) {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'INVALID_TYPE',
        currentValue: typeof answer,
        expectedFormat: '字母数组(如 ["A", "C"])',
        fixSuggestion: '多选题答案必须是字母数组',
        autoFixable: false
      });
      return false;
    }

    if (answer.length === 0) {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'EMPTY_ARRAY',
        currentValue: '[]',
        expectedFormat: '至少一个字母',
        fixSuggestion: '多选题答案不能为空',
        autoFixable: false
      });
      return false;
    }

    const choicePattern = /^[A-Z]$/;
    // 验证每个答案都是有效字母
    for (let i = 0; i < answer.length; i++) {
      const answerItem = answer[i];
      if (!choicePattern.test(answerItem)) {
        this.addError(result, {
          fieldPath: `answer[${i}]`,
          errorType: 'INVALID_FORMAT',
          currentValue: answerItem,
          expectedFormat: 'A-Z字母',
          fixSuggestion: '多选题答案必须都是A-Z字母',
          autoFixable: false
        });
        return false;
      }
    }

    // 验证答案是否在选项范围内
    const options = questionBody.options;
    if (options && Array.isArray(options)) {
      for (const answerItem of answer) {
        const answerIndex = answerItem.charCodeAt(0) - 'A'.charCodeAt(0);
        if (answerIndex >= options.length) {
          this.addError(result, {
            fieldPath: 'answer',
            errorType: 'OUT_OF_RANGE',
            currentValue: answerItem,
            expectedFormat: `A-${String.fromCharCode('A'.charCodeAt(0) + options.length - 1)}`,
            fixSuggestion: `答案 ${answerItem} 超出选项范围`,
            autoFixable: false
          });
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 验证填空题答案格式
   */
  private validateFillInBlankAnswer(answer: any, result: ValidationResult): boolean {
    if (!Array.isArray(answer)) {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'INVALID_TYPE',
        currentValue: typeof answer,
        expectedFormat: '字符串数组',
        fixSuggestion: '填空题答案必须是字符串数组',
        autoFixable: false
      });
      return false;
    }

    if (answer.length === 0) {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'EMPTY_ARRAY',
        currentValue: '[]',
        expectedFormat: '至少一个答案',
        fixSuggestion: '填空题答案不能为空',
        autoFixable: false
      });
      return false;
    }

    // 验证每个答案都不为空
    for (let i = 0; i < answer.length; i++) {
      const answerItem = answer[i];
      if (typeof answerItem !== 'string' || answerItem.trim() === '') {
        this.addError(result, {
          fieldPath: `answer[${i}]`,
          errorType: 'EMPTY_STRING',
          currentValue: answerItem,
          expectedFormat: '非空字符串',
          fixSuggestion: '填空题答案不能为空',
          autoFixable: false
        });
        return false;
      }
    }

    return true;
  }

  /**
   * 验证判断题答案格式
   */
  private validateTrueFalseAnswer(answer: any, result: ValidationResult): boolean {
    if (typeof answer !== 'boolean') {
      this.addError(result, {
        fieldPath: 'answer',
        errorType: 'INVALID_TYPE',
        currentValue: typeof answer,
        expectedFormat: '布尔值(true/false)',
        fixSuggestion: '判断题答案必须是布尔值',
        autoFixable: false
      });
      return false;
    }

    return true;
  }

  /**
   * 验证HTML内容安全性
   */
  private validateHtmlContent(questionBody: any, result: ValidationResult): boolean {
    // 验证content字段的HTML格式
    const content = questionBody.content;
    if (!this.isValidHtml(content)) {
      this.addError(result, {
        fieldPath: 'content',
        errorType: 'INVALID_HTML',
        currentValue: content.length > 50 ? content.substring(0, 50) + '...' : content,
        expectedFormat: '有效的HTML格式',
        fixSuggestion: '题目内容HTML格式不正确',
        autoFixable: false
      });
      return false;
    }

    // 验证explanation字段的HTML格式
    const explanation = questionBody.explanation;
    if (!this.isValidHtml(explanation)) {
      this.addError(result, {
        fieldPath: 'explanation',
        errorType: 'INVALID_HTML',
        currentValue: explanation.length > 50 ? explanation.substring(0, 50) + '...' : explanation,
        expectedFormat: '有效的HTML格式',
        fixSuggestion: '题目解析HTML格式不正确',
        autoFixable: false
      });
      return false;
    }

    return true;
  }

  /**
   * 简单的HTML格式验证
   */
  private isValidHtml(html: string): boolean {
    if (!html || html.trim() === '') {
      return false;
    }

    // 基础HTML标签匹配验证
    // 这里可以根据需要添加更严格的HTML验证逻辑
    return true; // 暂时返回true，后续可以添加更严格的验证
  }

  /**
   * 添加验证错误
   */
  private addError(result: ValidationResult, error: ValidationError): void {
    result.errors.push(error);
    result.valid = false;
    if (!result.message) {
      result.message = error.fixSuggestion;
    }
  }

  /**
   * 批量验证题目数组
   */
  validateQuestionBodies(questionBodies: any[]): { index: number; result: ValidationResult }[] {
    return questionBodies.map((body, index) => ({
      index,
      result: this.validateQuestionBody(body)
    }));
  }
}

// 导出验证器实例
export const questionValidator = new QuestionBodyValidator();
