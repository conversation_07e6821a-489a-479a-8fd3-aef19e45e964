package com.example.adminbackend.repository;

import com.example.adminbackend.model.Chapter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 章节数据访问层
 */
@Repository
public interface ChapterRepository extends JpaRepository<Chapter, Long> {

    /**
     * 根据科目版本ID查找所有章节，按顺序排序
     */
    List<Chapter> findBySubjectVersionIdOrderByOrderIndexAsc(Long subjectVersionId);

    /**
     * 根据科目版本ID和章节名称查找章节
     */
    Optional<Chapter> findBySubjectVersionIdAndName(Long subjectVersionId, String name);

    /**
     * 根据科目版本ID和章节名称查找所有匹配的章节（用于处理重复数据）
     */
    List<Chapter> findAllBySubjectVersionIdAndName(Long subjectVersionId, String name);

    /**
     * 根据科目版本ID和顺序索引查找章节
     */
    Optional<Chapter> findBySubjectVersionIdAndOrderIndex(Long subjectVersionId, Integer orderIndex);

    /**
     * 检查科目版本下是否存在指定名称的章节
     */
    boolean existsBySubjectVersionIdAndName(Long subjectVersionId, String name);

    /**
     * 检查科目版本下是否存在指定顺序的章节
     */
    boolean existsBySubjectVersionIdAndOrderIndex(Long subjectVersionId, Integer orderIndex);

    /**
     * 获取科目版本下章节的最大顺序索引
     */
    @Query("SELECT MAX(c.orderIndex) FROM Chapter c WHERE c.subjectVersion.id = :subjectVersionId")
    Integer findMaxOrderIndexBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 获取有知识点的章节列表
     */
    @Query("SELECT DISTINCT c FROM Chapter c JOIN c.knowledgePoints kp ORDER BY c.subjectVersion.id, c.orderIndex")
    List<Chapter> findChaptersWithKnowledgePoints();

    /**
     * 根据科目ID查找所有章节
     */
    @Query("SELECT c FROM Chapter c JOIN c.subjectVersion sv WHERE sv.subject.id = :subjectId ORDER BY sv.id, c.orderIndex")
    List<Chapter> findBySubjectId(@Param("subjectId") Long subjectId);

    /**
     * 根据章节名称模糊查询
     */
    @Query("SELECT c FROM Chapter c WHERE c.name LIKE %:keyword% ORDER BY c.subjectVersion.id, c.orderIndex")
    List<Chapter> findByNameContaining(@Param("keyword") String keyword);
}
