package com.example.adminbackend.controller;

import com.example.adminbackend.dto.ApiResponse;
import com.example.adminbackend.dto.migration.MigrationResult;
import com.example.adminbackend.dto.migration.SourceQuestionData;
import com.example.adminbackend.service.migration.QuestionMigrationService;
import com.example.adminbackend.util.JsonValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据迁移控制器
 * 提供题库数据迁移的REST API接口
 */
@RestController
@RequestMapping("/migration")
@RequiredArgsConstructor
@Slf4j
public class MigrationController {

    private final QuestionMigrationService migrationService;
    private final JsonValidationUtil jsonValidationUtil;
    
    // ==================== 数据迁移接口 ====================
    
    /**
     * 从JSON对象迁移题目（仅支持单个对象格式）
     */
    @PostMapping("/questions/from-json")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponse<MigrationResult>> migrateFromJson(
            @RequestBody SourceQuestionData questionData) {
        try {
            log.info("开始从JSON对象迁移题目，题目ID: {}", questionData.getId());

            // 直接处理单个题目对象
            MigrationResult result = migrationService.migrateQuestionFromObject(questionData);

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("JSON对象迁移失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<MigrationResult>builder()
                    .code(500)
                    .message("迁移失败: " + e.getMessage())
                    .build());
        }
    }
    
    /**
     * 从JSON字符串迁移题目（智能支持单个对象或多个连续对象）
     */
    @PostMapping("/questions/from-json-string")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponse<MigrationResult>> migrateFromJsonString(
            @RequestBody String jsonData) {
        try {
            log.info("开始从JSON字符串迁移题目");

            // 使用JsonValidationUtil解析多个JSON对象
            List<SourceQuestionData> questionList = jsonValidationUtil.parseMultipleJsonObjects(jsonData);

            if (questionList.size() == 1) {
                // 单个题目，使用单个迁移方法
                MigrationResult result = migrationService.migrateQuestionFromObject(questionList.get(0));
                return ResponseEntity.ok(ApiResponse.success(result));
            } else {
                // 多个题目，使用批量迁移方法
                MigrationResult result = migrationService.migrateQuestions(questionList);
                return ResponseEntity.ok(ApiResponse.success(result));
            }

        } catch (Exception e) {
            log.error("JSON字符串迁移失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<MigrationResult>builder()
                    .code(500)
                    .message("迁移失败: " + e.getMessage())
                    .build());
        }
    }
    

    
    // ==================== 数据验证接口 ====================
    
    /**
     * 验证源数据格式
     */
    @PostMapping("/questions/validate")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ApiResponse<MigrationResult>> validateSourceData(
            @RequestBody List<SourceQuestionData> sourceData) {
        try {
            log.info("开始验证源数据格式，数据量: {}", sourceData.size());
            
            MigrationResult result = migrationService.validateSourceData(sourceData);
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("数据验证失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<MigrationResult>builder()
                    .code(500)
                    .message("验证失败: " + e.getMessage())
                    .build());
        }
    }
    
    /**
     * 验证JSON字符串格式
     */
    @PostMapping("/questions/validate-json")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateJsonFormat(
            @RequestBody String jsonData) {
        try {
            log.info("开始验证JSON格式，数据长度: {}", jsonData != null ? jsonData.length() : 0);

            // 使用JsonValidationUtil进行详细验证
            JsonValidationUtil.JsonValidationResult validationResult = jsonValidationUtil.validateJsonFormat(jsonData);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", validationResult.isValid());
            result.put("message", validationResult.getMessage());

            if (validationResult.isValid()) {
                result.put("dataCount", validationResult.getDataCount());
                log.info("JSON验证通过，数据量: {}", validationResult.getDataCount());
            } else {
                result.put("error", validationResult.getMessage());
                log.warn("JSON验证失败: {}", validationResult.getMessage());
            }

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("JSON格式验证失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", false);
            result.put("error", e.getMessage());
            result.put("message", "JSON格式验证失败");

            return ResponseEntity.ok(ApiResponse.success(result));
        }
    }

    /**
     * 调试JSON数据格式
     */
    @PostMapping("/questions/debug-json")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> debugJsonFormat(
            @RequestBody String jsonData) {
        try {
            log.info("开始调试JSON格式");

            Map<String, Object> debugInfo = new HashMap<>();

            // 基本信息
            debugInfo.put("dataLength", jsonData != null ? jsonData.length() : 0);
            debugInfo.put("isEmpty", jsonData == null || jsonData.trim().isEmpty());

            if (jsonData != null) {
                String trimmed = jsonData.trim();
                debugInfo.put("startsWithBrace", trimmed.startsWith("{"));
                debugInfo.put("endsWithBrace", trimmed.endsWith("}"));

                // 查找最后一个]的位置
                int lastBracket = trimmed.lastIndexOf(']');
                debugInfo.put("lastBracketIndex", lastBracket);

                if (lastBracket != -1 && lastBracket < trimmed.length() - 1) {
                    String afterArray = trimmed.substring(lastBracket + 1).trim();
                    debugInfo.put("hasContentAfterArray", !afterArray.isEmpty());
                    debugInfo.put("contentAfterArray", afterArray.length() > 200 ?
                        afterArray.substring(0, 200) + "..." : afterArray);
                }

                // 尝试JSON解析
                try {
                    JsonValidationUtil.JsonValidationResult validationResult =
                        jsonValidationUtil.validateJsonFormat(jsonData);
                    debugInfo.put("validationResult", Map.of(
                        "valid", validationResult.isValid(),
                        "message", validationResult.getMessage(),
                        "dataCount", validationResult.getDataCount()
                    ));
                } catch (Exception e) {
                    debugInfo.put("validationError", e.getMessage());
                }
            }

            return ResponseEntity.ok(ApiResponse.success(debugInfo));

        } catch (Exception e) {
            log.error("JSON调试失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<Map<String, Object>>builder()
                    .code(500)
                    .message("调试失败: " + e.getMessage())
                    .build());
        }
    }
    
    // ==================== 迁移状态接口 ====================
    
    /**
     * 获取当前迁移进度
     */
    @GetMapping("/questions/progress")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ApiResponse<MigrationResult>> getMigrationProgress() {
        try {
            MigrationResult result = migrationService.getCurrentMigrationProgress();
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取迁移进度失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<MigrationResult>builder()
                    .code(500)
                    .message("获取进度失败: " + e.getMessage())
                    .build());
        }
    }
    
    /**
     * 停止当前迁移任务
     */
    @PostMapping("/questions/stop")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponse<String>> stopMigration() {
        try {
            migrationService.stopMigration();
            return ResponseEntity.ok(ApiResponse.success("迁移任务已停止"));
        } catch (Exception e) {
            log.error("停止迁移失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<String>builder()
                    .code(500)
                    .message("停止失败: " + e.getMessage())
                    .build());
        }
    }
    
    /**
     * 清理迁移缓存
     */
    @PostMapping("/questions/clear-cache")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponse<String>> clearMigrationCache() {
        try {
            migrationService.clearMigrationCache();
            return ResponseEntity.ok(ApiResponse.success("缓存已清理"));
        } catch (Exception e) {
            log.error("清理缓存失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<String>builder()
                    .code(500)
                    .message("清理失败: " + e.getMessage())
                    .build());
        }
    }
    
    // ==================== 系统状态接口 ====================
    
    /**
     * 获取迁移系统状态
     */
    @GetMapping("/status")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMigrationStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("systemReady", true);
            status.put("timestamp", System.currentTimeMillis());
            status.put("version", "1.0.0");
            
            // 可以添加更多系统状态信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("totalMemory", runtime.totalMemory());
            systemInfo.put("freeMemory", runtime.freeMemory());
            systemInfo.put("maxMemory", runtime.maxMemory());
            systemInfo.put("availableProcessors", runtime.availableProcessors());
            
            status.put("systemInfo", systemInfo);
            
            return ResponseEntity.ok(ApiResponse.success(status));
            
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.<Map<String, Object>>builder()
                    .code(500)
                    .message("获取状态失败: " + e.getMessage())
                    .build());
        }
    }
}
