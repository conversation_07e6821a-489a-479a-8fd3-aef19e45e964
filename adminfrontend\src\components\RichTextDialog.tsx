import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  IconButton,
  Typography,
} from '@mui/material';
import { Close as CloseIcon, Fullscreen as FullscreenIcon, FullscreenExit as FullscreenExitIcon } from '@mui/icons-material';
import WangEditor, { PendingFile } from './WangEditor';


interface RichTextDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (content: string, files?: PendingFile[]) => void;
  title?: string;
  initialContent?: string;
  placeholder?: string;
}

const RichTextDialog: React.FC<RichTextDialogProps> = ({
  open,
  onClose,
  onSave,
  title = '富文本编辑器',
  initialContent = '',
  placeholder = '请输入内容...',
}) => {
  const [content, setContent] = useState(initialContent);
  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 当对话框打开时，设置初始内容
  useEffect(() => {
    if (open) {
      setContent(initialContent);
    }
  }, [open, initialContent]);

  const handleSave = () => {
    onSave(content, pendingFiles);
    onClose();
  };

  const handleCancel = () => {
    setContent(initialContent); // 恢复原始内容
    setPendingFiles([]); // 清空待上传文件
    onClose();
  };

  const handleFilesChange = (files: PendingFile[]) => {
    setPendingFiles(files);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
      <Dialog
        open={open}
        onClose={handleCancel}
        maxWidth={isFullscreen ? false : 'lg'}
        fullWidth
        fullScreen={isFullscreen}
        PaperProps={{
          sx: {
            height: isFullscreen ? '100vh' : '80vh',
            maxHeight: isFullscreen ? '100vh' : '80vh',
          },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            pb: 1,
          }}
        >
          {title}
          <Box>
            <IconButton onClick={toggleFullscreen} size="small" sx={{ mr: 1 }}>
              <FullscreenIcon />
            </IconButton>
            <IconButton onClick={handleCancel} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent
          sx={{
            p: 0,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              flex: 1,
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }}
          >
            <WangEditor
              value={content}
              onChange={setContent}
              onFilesChange={handleFilesChange}
              placeholder={placeholder}
              height="100%"
              showToolbar={true}
              mode="default"
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 2, pt: 1 }}>
          <Button onClick={handleCancel} color="inherit">
            取消
          </Button>
          <Button onClick={handleSave} variant="contained" color="primary">
            保存
          </Button>
        </DialogActions>
      </Dialog>
  );
};

export default RichTextDialog;
