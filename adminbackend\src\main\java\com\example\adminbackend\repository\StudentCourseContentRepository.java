package com.example.adminbackend.repository;

import com.example.adminbackend.model.StudentCourseContent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 学生课程内容数据访问层
 */
@Repository
public interface StudentCourseContentRepository extends JpaRepository<StudentCourseContent, Long> {

    /**
     * 根据学科版本ID查找所有学生课程内容记录
     */
    List<StudentCourseContent> findBySubjectVersionId(Long subjectVersionId);

    /**
     * 根据学科版本ID删除所有相关记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM StudentCourseContent scc WHERE scc.subjectVersion.id = :subjectVersionId")
    void deleteBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 根据学生ID查找所有课程内容记录
     */
    List<StudentCourseContent> findByStudentId(Long studentId);

    /**
     * 根据学科ID查找所有课程内容记录
     */
    List<StudentCourseContent> findBySubjectId(Long subjectId);

    /**
     * 根据试用ID查找所有课程内容记录
     */
    List<StudentCourseContent> findByTrialId(Long trialId);

    /**
     * 检查学科版本是否被学生课程内容使用
     */
    boolean existsBySubjectVersionId(Long subjectVersionId);

    /**
     * 统计学科版本的使用数量
     */
    @Query("SELECT COUNT(scc) FROM StudentCourseContent scc WHERE scc.subjectVersion.id = :subjectVersionId")
    long countBySubjectVersionId(@Param("subjectVersionId") Long subjectVersionId);

    /**
     * 根据学生ID和学科ID查找课程内容记录
     */
    List<StudentCourseContent> findByStudentIdAndSubjectId(Long studentId, Long subjectId);

    /**
     * 根据学生ID和学科版本ID查找课程内容记录
     */
    List<StudentCourseContent> findByStudentIdAndSubjectVersionId(Long studentId, Long subjectVersionId);
}
