package com.example.adminbackend.repository;

import com.example.adminbackend.model.SubjectVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 科目版本数据访问层
 */
@Repository
public interface SubjectVersionRepository extends JpaRepository<SubjectVersion, Long> {

    /**
     * 根据科目ID查找所有版本
     */
    List<SubjectVersion> findBySubjectIdOrderByCreatedAtAsc(Long subjectId);

    /**
     * 根据科目ID和版本名称查找版本
     */
    Optional<SubjectVersion> findBySubjectIdAndName(Long subjectId, String name);

    /**
     * 根据科目ID和版本名称查找所有匹配的版本（用于处理重复数据）
     */
    List<SubjectVersion> findAllBySubjectIdAndName(Long subjectId, String name);

    /**
     * 根据学校级别查找版本
     */
    List<SubjectVersion> findBySchoolLevel(String schoolLevel);

    /**
     * 根据科目ID和学校级别查找版本
     */
    List<SubjectVersion> findBySubjectIdAndSchoolLevel(Long subjectId, String schoolLevel);

    /**
     * 检查科目下是否存在指定名称的版本
     */
    boolean existsBySubjectIdAndName(Long subjectId, String name);

    /**
     * 根据科目ID、版本名称和学校级别查找版本
     */
    Optional<SubjectVersion> findBySubjectIdAndNameAndSchoolLevel(Long subjectId, String name, String schoolLevel);

    /**
     * 根据科目ID、版本名称和学校级别查找所有匹配的版本（用于处理重复数据）
     */
    List<SubjectVersion> findAllBySubjectIdAndNameAndSchoolLevel(Long subjectId, String name, String schoolLevel);

    /**
     * 检查科目下是否存在指定名称和学校级别的版本
     */
    boolean existsBySubjectIdAndNameAndSchoolLevel(Long subjectId, String name, String schoolLevel);

    /**
     * 获取有章节的科目版本列表
     */
    @Query("SELECT DISTINCT sv FROM SubjectVersion sv LEFT JOIN Chapter c ON c.subjectVersion.id = sv.id WHERE c.id IS NOT NULL ORDER BY sv.id, sv.name")
    List<SubjectVersion> findSubjectVersionsWithChapters();

    /**
     * 根据科目名称查找版本
     */
    @Query("SELECT sv FROM SubjectVersion sv JOIN sv.subject s WHERE s.name = :subjectName ORDER BY sv.createdAt ASC")
    List<SubjectVersion> findBySubjectName(@Param("subjectName") String subjectName);

    /**
     * 统计科目下的版本数量
     */
    long countBySubjectId(Long subjectId);
}
