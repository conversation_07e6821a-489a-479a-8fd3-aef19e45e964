import React from 'react';

export interface KeyData {
  type: string;
  label?: string;
  insert?: string;
  latex?: string;
  command?: string | string[];
  template?: string;
}

export interface MathQuillKeyboardProps {
  visible?: boolean;
  onKeyPress?: (keyData: KeyData) => void;
  onClose?: () => void;
  targetMathField?: any;
  className?: string;
  autoHandle?: boolean;
}

export interface UseMathQuillKeyboardReturn {
  keyboardVisible: boolean;
  targetMathField: any;
  onKeyPress: ((keyData: KeyData) => void) | null;
  showKeyboard: (mathField?: any, onKeyPress?: (keyData: KeyData) => void) => void;
  hideKeyboard: () => void;
  toggleKeyboard: (mathField?: any, onKeyPress?: (keyData: KeyData) => void) => void;
}

export interface GlobalKeyboardState {
  visible: boolean;
  targetField: any;
  onKeyPress: ((keyData: KeyData) => void) | null;
  listeners: Set<(state: GlobalKeyboardState) => void>;
}

export const GlobalKeyboardController: {
  show: (targetField?: any, onKeyPress?: (keyData: KeyData) => void) => void;
  hide: () => void;
  toggle: (targetField?: any, onKeyPress?: (keyData: KeyData) => void) => void;
  subscribe: (listener: (state: GlobalKeyboardState) => void) => () => void;
  getState: () => GlobalKeyboardState;
};

declare const MathQuillKeyboard: React.ComponentType<MathQuillKeyboardProps>;
export default MathQuillKeyboard;

export function useMathQuillKeyboard(): UseMathQuillKeyboardReturn;

export const MATH_SYMBOLS_CATEGORIES: any;
export const ALL_MATH_SYMBOLS: any;
export const NUMERIC_LAYOUT: any;
export const ENGLISH_KEYBOARD_LAYOUT: any;
