package com.example.adminbackend.service;

import com.example.adminbackend.model.VideoCollection;

import java.util.List;

/**
 * 视频合集服务接口
 */
public interface VideoCollectionService {

    /**
     * 获取所有视频合集
     */
    List<VideoCollection> getAllVideoCollections();

    /**
     * 根据ID获取视频合集
     */
    VideoCollection getVideoCollectionById(Long id);

    /**
     * 根据名称获取视频合集
     */
    VideoCollection getVideoCollectionByName(String name);

    /**
     * 创建视频合集
     */
    VideoCollection createVideoCollection(VideoCollection videoCollection);

    /**
     * 更新视频合集
     */
    VideoCollection updateVideoCollection(Long id, VideoCollection videoCollection);

    /**
     * 删除视频合集
     */
    void deleteVideoCollection(Long id);

    /**
     * 检查视频合集名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 根据关键词搜索视频合集
     */
    List<VideoCollection> searchVideoCollections(String keyword);

    /**
     * 根据视频URL查找或创建视频合集
     * 用于迁移过程中处理知识点的video字段
     */
    VideoCollection findOrCreateVideoCollectionByUrl(String videoUrl);

    /**
     * 根据视频URL和知识点名称查找或创建视频合集
     * 视频名称将使用知识点名称加时间字符串
     */
    VideoCollection findOrCreateVideoCollectionByUrlAndKnowledgePoint(String videoUrl, String knowledgePointName);

    /**
     * 向指定视频合集添加视频
     * @param collectionId 视频合集ID
     * @param videoUrl 视频URL
     * @return 是否添加成功
     */
    boolean addVideoToCollection(Long collectionId, String videoUrl);

    /**
     * 获取视频合集中的视频数量
     * @param collectionId 视频合集ID
     * @return 视频数量
     */
    int getVideoCountInCollection(Long collectionId);

    /**
     * 获取视频合集中的所有视频URL
     * @param collectionId 视频合集ID
     * @return 视频URL列表
     */
    List<String> getVideoUrlsInCollection(Long collectionId);

    /**
     * 更新视频合集中的视频URL
     * 如果新的视频URL与现有的不同，会清理旧视频并添加新视频
     * @param collectionId 视频合集ID
     * @param newVideoUrl 新的视频URL
     * @param knowledgePointName 知识点名称，用于创建新视频的标题
     * @return 是否更新成功
     */
    boolean updateVideoCollectionWithNewUrl(Long collectionId, String newVideoUrl, String knowledgePointName);
}
