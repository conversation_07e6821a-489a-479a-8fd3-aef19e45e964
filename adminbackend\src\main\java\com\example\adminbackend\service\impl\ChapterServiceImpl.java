package com.example.adminbackend.service.impl;

import com.example.adminbackend.model.Chapter;
import com.example.adminbackend.model.KnowledgePoint;
import com.example.adminbackend.repository.ChapterRepository;
import com.example.adminbackend.repository.KnowledgePointRepository;
import com.example.adminbackend.service.ChapterService;
import com.example.adminbackend.service.KnowledgePointService;
import com.example.adminbackend.dto.ChapterDTO;
import com.example.adminbackend.dto.KnowledgePointDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 章节服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChapterServiceImpl implements ChapterService {

    private final ChapterRepository chapterRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final KnowledgePointService knowledgePointService;

    @Override
    @Transactional(readOnly = true)
    public List<Chapter> getAllChapters() {
        log.info("获取所有章节");
        return chapterRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Chapter getChapterById(Long id) {
        log.info("根据ID获取章节: {}", id);
        return chapterRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("章节不存在: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChapterDTO> getChaptersBySubjectVersionId(Long subjectVersionId) {
        log.info("根据科目版本ID获取章节: {}", subjectVersionId);
        List<Chapter> chapters = chapterRepository.findBySubjectVersionIdOrderByOrderIndexAsc(subjectVersionId);
        
        return chapters.stream().map(chapter -> {
            ChapterDTO chapterDTO = new ChapterDTO();
            chapterDTO.setId(chapter.getId());
            chapterDTO.setName(chapter.getName());
            chapterDTO.setOrderIndex(chapter.getOrderIndex());
            chapterDTO.setCreatedAt(chapter.getCreatedAt());
            chapterDTO.setUpdatedAt(chapter.getUpdatedAt());
            chapterDTO.setSubjectVersionId(chapter.getSubjectVersion().getId());

            // eager fetch knowledge points
            List<KnowledgePointDTO> knowledgePointDTOs = chapter.getKnowledgePoints().stream().map(kp -> {
                KnowledgePointDTO kpDto = new KnowledgePointDTO();
                kpDto.setId(kp.getId());
                kpDto.setName(kp.getName());
                kpDto.setOrderIndex(kp.getOrderIndex());
                kpDto.setCoverImageUrl(kp.getCoverImageUrl());
                kpDto.setDescription(kp.getDescription());
                kpDto.setEnabled(kp.getEnabled());
                kpDto.setCreatedAt(kp.getCreatedAt());
                kpDto.setUpdatedAt(kp.getUpdatedAt());
                kpDto.setChapterId(kp.getChapter().getId());

                // 设置视频合集信息
                if (kp.getVideoCollection() != null) {
                    kpDto.setVideoCollectionId(kp.getVideoCollection().getId());
                    kpDto.setVideoCollectionName(kp.getVideoCollection().getName());
                }

                return kpDto;
            }).collect(java.util.stream.Collectors.toList());
            chapterDTO.setKnowledgePoints(knowledgePointDTOs);
            
            return chapterDTO;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Chapter getChapterBySubjectVersionIdAndName(Long subjectVersionId, String name) {
        log.info("根据科目版本ID和名称获取章节: subjectVersionId={}, name={}", subjectVersionId, name);
        // 使用findAll方法避免重复数据异常
        List<Chapter> results = chapterRepository.findAll().stream()
            .filter(c -> c.getSubjectVersion().getId().equals(subjectVersionId) && c.getName().equals(name))
            .collect(java.util.stream.Collectors.toList());
        if (results.isEmpty()) {
            throw new RuntimeException("章节不存在: " + name);
        }
        if (results.size() > 1) {
            log.warn("发现重复的章节记录: subjectVersionId={}, name={}, 数量={}, 使用第一个", subjectVersionId, name, results.size());
        }
        return results.get(0);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Chapter> getChaptersBySubjectId(Long subjectId) {
        log.info("根据科目ID获取章节: {}", subjectId);
        return chapterRepository.findBySubjectId(subjectId);
    }

    @Override
    @Transactional
    public Chapter createChapter(Chapter chapter) {
        log.info("创建章节: {}", chapter.getName());
        
        // 检查同一科目版本下名称是否已存在
        if (chapterRepository.existsBySubjectVersionIdAndName(
                chapter.getSubjectVersion().getId(), chapter.getName())) {
            throw new RuntimeException("章节名称已存在: " + chapter.getName());
        }
        
        // 如果没有设置顺序索引，自动设置为最大值+1
        if (chapter.getOrderIndex() == null) {
            Integer maxOrderIndex = chapterRepository.findMaxOrderIndexBySubjectVersionId(
                    chapter.getSubjectVersion().getId());
            chapter.setOrderIndex(maxOrderIndex == null ? 1 : maxOrderIndex + 1);
        } else {
            // 检查顺序索引是否已存在
            if (chapterRepository.existsBySubjectVersionIdAndOrderIndex(
                    chapter.getSubjectVersion().getId(), chapter.getOrderIndex())) {
                throw new RuntimeException("章节顺序索引已存在: " + chapter.getOrderIndex());
            }
        }
        
        return chapterRepository.save(chapter);
    }

    @Override
    @Transactional
    public Chapter updateChapter(Long id, Chapter chapter) {
        log.info("更新章节: {}", id);
        
        Chapter existingChapter = getChapterById(id);
        
        // 检查名称是否与同一科目版本下其他章节冲突
        if (!existingChapter.getName().equals(chapter.getName()) && 
            chapterRepository.existsBySubjectVersionIdAndName(
                    existingChapter.getSubjectVersion().getId(), chapter.getName())) {
            throw new RuntimeException("章节名称已存在: " + chapter.getName());
        }
        
        // 检查顺序索引是否与同一科目版本下其他章节冲突
        if (!existingChapter.getOrderIndex().equals(chapter.getOrderIndex()) && 
            chapterRepository.existsBySubjectVersionIdAndOrderIndex(
                    existingChapter.getSubjectVersion().getId(), chapter.getOrderIndex())) {
            throw new RuntimeException("章节顺序索引已存在: " + chapter.getOrderIndex());
        }
        
        existingChapter.setName(chapter.getName());
        existingChapter.setOrderIndex(chapter.getOrderIndex());
        
        return chapterRepository.save(existingChapter);
    }

    @Override
    @Transactional
    public void deleteChapter(Long id) {
        log.info("删除章节: {}", id);

        Chapter chapter = getChapterById(id);

        // 检查是否有关联的知识点
        if (chapter.getKnowledgePoints() != null && !chapter.getKnowledgePoints().isEmpty()) {
            throw new RuntimeException("无法删除章节，存在关联的知识点");
        }

        chapterRepository.delete(chapter);
    }

    @Override
    @Transactional
    public int deleteChapterCascade(Long id) {
        log.info("级联删除章节: {}", id);

        Chapter chapter = getChapterById(id);

        // 获取章节下的所有知识点
        List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdOrderByOrderIndexAsc(id);
        int totalDeletedFiles = 0;

        if (!knowledgePoints.isEmpty()) {
            log.info("章节 {} 存在 {} 个知识点，开始级联删除", chapter.getName(), knowledgePoints.size());

            // 删除每个知识点及其关联的视频和文件
            for (KnowledgePoint kp : knowledgePoints) {
                int deletedFiles = knowledgePointService.deleteKnowledgePointCascade(kp.getId());
                totalDeletedFiles += deletedFiles;
            }
        }

        // 删除章节
        chapterRepository.delete(chapter);
        log.info("章节级联删除完成: {} (ID: {}), 删除文件数: {}", chapter.getName(), id, totalDeletedFiles);

        return totalDeletedFiles;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsBySubjectVersionIdAndName(Long subjectVersionId, String name) {
        return chapterRepository.existsBySubjectVersionIdAndName(subjectVersionId, name);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Chapter> searchChapters(String keyword) {
        log.info("搜索章节: {}", keyword);
        return chapterRepository.findByNameContaining(keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Chapter> getChaptersWithKnowledgePoints() {
        log.info("获取有知识点的章节列表");
        return chapterRepository.findChaptersWithKnowledgePoints();
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getMaxOrderIndexBySubjectVersionId(Long subjectVersionId) {
        return chapterRepository.findMaxOrderIndexBySubjectVersionId(subjectVersionId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChapterDTO> getChaptersBySubjectIdAsDTO(Long subjectId) {
        log.info("根据科目ID获取章节（DTO格式）: {}", subjectId);
        List<Chapter> chapters = chapterRepository.findBySubjectId(subjectId);

        return chapters.stream().map(chapter -> {
            ChapterDTO dto = new ChapterDTO();
            dto.setId(chapter.getId());
            dto.setName(chapter.getName());
            dto.setOrderIndex(chapter.getOrderIndex());
            dto.setCreatedAt(chapter.getCreatedAt());
            dto.setUpdatedAt(chapter.getUpdatedAt());
            // 安全访问subjectVersion关联，避免懒加载异常
            if (chapter.getSubjectVersion() != null) {
                dto.setSubjectVersionId(chapter.getSubjectVersion().getId());
                dto.setSubjectVersionName(chapter.getSubjectVersion().getName());
                dto.setSchoolLevel(chapter.getSubjectVersion().getSchoolLevel());

                // 获取学科名称
                if (chapter.getSubjectVersion().getSubject() != null) {
                    dto.setSubjectName(chapter.getSubjectVersion().getSubject().getName());
                }
            }
            // 不包含knowledgePoints，避免懒加载问题
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChapterDTO> getChaptersWithKnowledgePointsAsDTO() {
        log.info("获取有知识点的章节列表（DTO格式）");
        List<Chapter> chapters = chapterRepository.findChaptersWithKnowledgePoints();

        return chapters.stream().map(chapter -> {
            ChapterDTO dto = new ChapterDTO();
            dto.setId(chapter.getId());
            dto.setName(chapter.getName());
            dto.setOrderIndex(chapter.getOrderIndex());
            dto.setCreatedAt(chapter.getCreatedAt());
            dto.setUpdatedAt(chapter.getUpdatedAt());
            // 安全访问subjectVersion关联，避免懒加载异常
            if (chapter.getSubjectVersion() != null) {
                dto.setSubjectVersionId(chapter.getSubjectVersion().getId());
                dto.setSubjectVersionName(chapter.getSubjectVersion().getName());
                dto.setSchoolLevel(chapter.getSubjectVersion().getSchoolLevel());

                // 获取学科名称
                if (chapter.getSubjectVersion().getSubject() != null) {
                    dto.setSubjectName(chapter.getSubjectVersion().getSubject().getName());
                }
            }
            // 不包含knowledgePoints，避免懒加载问题
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ChapterDTO getChapterByIdAsDTO(Long id) {
        log.info("根据ID获取章节（DTO格式）: {}", id);
        Chapter chapter = chapterRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("章节不存在: " + id));

        ChapterDTO dto = new ChapterDTO();
        dto.setId(chapter.getId());
        dto.setName(chapter.getName());
        dto.setOrderIndex(chapter.getOrderIndex());
        dto.setCreatedAt(chapter.getCreatedAt());
        dto.setUpdatedAt(chapter.getUpdatedAt());
        // 安全访问subjectVersion关联，避免懒加载异常
        if (chapter.getSubjectVersion() != null) {
            dto.setSubjectVersionId(chapter.getSubjectVersion().getId());
            dto.setSubjectVersionName(chapter.getSubjectVersion().getName());
            dto.setSchoolLevel(chapter.getSubjectVersion().getSchoolLevel());

            // 获取学科名称
            if (chapter.getSubjectVersion().getSubject() != null) {
                dto.setSubjectName(chapter.getSubjectVersion().getSubject().getName());
            }
        }
        // 不包含knowledgePoints，避免懒加载问题
        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChapterDTO> searchChaptersAsDTO(String keyword) {
        log.info("搜索章节（DTO格式）: {}", keyword);
        List<Chapter> chapters = chapterRepository.findByNameContaining(keyword);

        return chapters.stream().map(chapter -> {
            ChapterDTO dto = new ChapterDTO();
            dto.setId(chapter.getId());
            dto.setName(chapter.getName());
            dto.setOrderIndex(chapter.getOrderIndex());
            dto.setCreatedAt(chapter.getCreatedAt());
            dto.setUpdatedAt(chapter.getUpdatedAt());
            // 安全访问subjectVersion关联，避免懒加载异常
            if (chapter.getSubjectVersion() != null) {
                dto.setSubjectVersionId(chapter.getSubjectVersion().getId());
                dto.setSubjectVersionName(chapter.getSubjectVersion().getName());
                dto.setSchoolLevel(chapter.getSubjectVersion().getSchoolLevel());

                // 获取学科名称
                if (chapter.getSubjectVersion().getSubject() != null) {
                    dto.setSubjectName(chapter.getSubjectVersion().getSubject().getName());
                }
            }
            // 不包含knowledgePoints，避免懒加载问题
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChapterDTO> getAllChaptersAsDTO() {
        log.info("获取所有章节（DTO格式）");
        List<Chapter> chapters = chapterRepository.findAll();

        return chapters.stream().map(chapter -> {
            ChapterDTO dto = new ChapterDTO();
            dto.setId(chapter.getId());
            dto.setName(chapter.getName());
            dto.setOrderIndex(chapter.getOrderIndex());
            dto.setCreatedAt(chapter.getCreatedAt());
            dto.setUpdatedAt(chapter.getUpdatedAt());
            // 安全访问subjectVersion关联，避免懒加载异常
            if (chapter.getSubjectVersion() != null) {
                dto.setSubjectVersionId(chapter.getSubjectVersion().getId());
                dto.setSubjectVersionName(chapter.getSubjectVersion().getName());
                dto.setSchoolLevel(chapter.getSubjectVersion().getSchoolLevel());

                // 获取学科名称
                if (chapter.getSubjectVersion().getSubject() != null) {
                    dto.setSubjectName(chapter.getSubjectVersion().getSubject().getName());
                }
            }

            // 通过Repository安全获取知识点信息，避免懒加载问题
            List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdOrderByOrderIndexAsc(chapter.getId());
            if (!knowledgePoints.isEmpty()) {
                List<KnowledgePointDTO> knowledgePointDTOs = knowledgePoints.stream().map(kp -> {
                    KnowledgePointDTO kpDto = new KnowledgePointDTO();
                    kpDto.setId(kp.getId());
                    kpDto.setName(kp.getName());
                    kpDto.setOrderIndex(kp.getOrderIndex());
                    kpDto.setCoverImageUrl(kp.getCoverImageUrl());
                    kpDto.setDescription(kp.getDescription());
                    kpDto.setEnabled(kp.getEnabled());
                    kpDto.setCreatedAt(kp.getCreatedAt());
                    kpDto.setUpdatedAt(kp.getUpdatedAt());
                    kpDto.setChapterId(chapter.getId());

                    // 安全访问视频合集信息
                    if (kp.getVideoCollection() != null) {
                        kpDto.setVideoCollectionId(kp.getVideoCollection().getId());
                        kpDto.setVideoCollectionName(kp.getVideoCollection().getName());
                    }

                    return kpDto;
                }).collect(java.util.stream.Collectors.toList());
                dto.setKnowledgePoints(knowledgePointDTOs);
            }

            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }
}
