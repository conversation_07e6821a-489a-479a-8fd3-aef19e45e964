package com.example.adminbackend.dto.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Objects;

/**
 * 源数据学科信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceSubject {
    
    /**
     * 学科ID
     */
    private Long id;
    
    /**
     * 学科名称
     */
    private String name;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SourceSubject that = (SourceSubject) o;
        return Objects.equals(name, that.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
