import axios, { AxiosError, AxiosResponse } from 'axios';
import { ErrorHandler, ProcessedError } from '../utils/errorHandler';
import { RetryHandler } from '../utils/retryHandler';

// API基础配置
const API_BASE_URL = 'http://localhost:9092/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  timeout: 30000, // 30秒超时
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 增强错误处理
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 记录成功响应（生产环境关闭日志）
    // console.log('API响应成功:', {
    //   url: response.config.url,
    //   method: response.config.method,
    //   status: response.status
    // });
    return response;
  },
  (error: AxiosError) => {
    const processedError = ErrorHandler.handleApiError(error);

    // 记录错误
    console.error('API请求失败:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      error: processedError
    });

    // 处理认证错误
    if (ErrorHandler.needsReauth(processedError)) {
      handleAuthError();
    }

    // 抛出处理后的错误
    return Promise.reject(processedError);
  }
);



// 处理认证错误
function handleAuthError(): void {
  localStorage.removeItem('token');
  localStorage.removeItem('user');

  // 避免在登录页面重复跳转
  if (!window.location.pathname.includes('/login')) {
    window.location.href = '/login';
  }
}

// 创建带重试的API调用
function createRetryableCall<T>(apiCall: () => Promise<T>, retryConfig?: any) {
  return RetryHandler.createRetryableApiCall(apiCall, retryConfig);
}

// 用户相关API
export const authAPI = {
  // 登录
  login: async (phone: string, password: string) => {
    const response = await api.post('/auth/login', { phone, password });
    return response.data;
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    const response = await api.get('/users/me');
    return response.data;
  },
};

// 用户管理API
export const userAPI = {
  // 获取用户列表
  getUsers: async (page: number, size: number, isSuperAdmin: boolean = false, sortBy: string = 'createdAt', sortDir: string = 'desc') => {
    const url = isSuperAdmin
      ? `/users/super-admin/page?page=${page}&size=${size}&sortBy=${sortBy}&sortDir=${sortDir}`
      : `/users/admin/supervisors/page?page=${page}&size=${size}&sortBy=${sortBy}&sortDir=${sortDir}`;
    const response = await api.get(url);
    return response.data;
  },

  // 获取所有用户（不分页）- 用于仪表板统计
  getAllUsers: async (isSuperAdmin: boolean = false) => {
    const url = isSuperAdmin
      ? '/users/super-admin/all'
      : '/users/admin/supervisors';
    const response = await api.get(url);
    return response.data;
  },

  // 获取单个用户
  getUser: async (id: number, isSuperAdmin: boolean = false) => {
    if (!id) {
      return authAPI.getCurrentUser();
    }
    const url = isSuperAdmin
      ? `/users/super-admin/${id}`
      : `/users/admin/supervisor/${id}`;
    const response = await api.get(url);
    return response.data;
  },

  // 创建用户
  createUser: async (userData: any, isSuperAdmin: boolean = false) => {
    const url = isSuperAdmin
      ? '/users/super-admin/create'
      : '/users/admin/create-supervisor';
    const response = await api.post(url, userData);
    return response.data;
  },

  // 更新用户
  updateUser: async (id: number, userData: any, isSuperAdmin: boolean = false) => {
    const url = isSuperAdmin
      ? `/users/super-admin/${id}`
      : `/users/admin/supervisor/${id}`;
    const response = await api.put(url, userData);
    return response.data;
  },

  // 删除用户
  deleteUser: async (id: number, isSuperAdmin: boolean = false) => {
    const url = isSuperAdmin
      ? `/users/super-admin/${id}`
      : `/users/admin/supervisor/${id}`;
    const response = await api.delete(url);
    return response.data;
  },
};

// 督学管理专用 API（管理员使用）
export const supervisorAPI = {
  // 获取督学列表（分页）
  getSupervisors: async (page: number, size: number) => {
    const response = await api.get(`/users/admin/supervisors/page?page=${page}&size=${size}`);
    return response.data;
  },

  // 获取所有督学（不分页）
  getAllSupervisors: async () => {
    const response = await api.get('/users/admin/supervisors');
    return response.data;
  },

  // 获取单个督学
  getSupervisor: async (id: number) => {
    const response = await api.get(`/users/admin/supervisor/${id}`);
    return response.data;
  },

  // 创建督学
  createSupervisor: async (userData: any) => {
    // 确保角色是督学
    const supervisorData = { ...userData, role: 'SUPERVISOR' };
    const response = await api.post('/users/admin/create-supervisor', supervisorData);
    return response.data;
  },

  // 更新督学
  updateSupervisor: async (id: number, userData: any) => {
    // 确保角色是督学
    const supervisorData = { ...userData, role: 'SUPERVISOR' };
    const response = await api.put(`/users/admin/supervisor/${id}`, supervisorData);
    return response.data;
  },

  // 删除督学
  deleteSupervisor: async (id: number) => {
    await api.delete(`/users/admin/supervisor/${id}`);
  },
};

// 课程体系API
export const curriculumAPI = {
  // 获取所有科目
  getSubjects: async () => {
    const response = await api.get('/curriculum/subjects');
    return response.data;
  },

  // 获取有版本的科目列表
  getSubjectsWithVersions: async () => {
    const response = await api.get('/curriculum/subjects/with-versions');
    return response.data;
  },

  // 根据科目ID获取版本
  getVersionsBySubjectId: async (subjectId: number) => {
    const response = await api.get(`/curriculum/subjects/${subjectId}/versions`);
    return response.data;
  },

  // 获取所有章节
  getChapters: async () => {
    const response = await api.get('/curriculum/chapters');
    return response.data;
  },

  // 获取所有知识点
  getKnowledgePoints: async () => {
    const response = await api.get('/curriculum/knowledge-points');
    return response.data;
  },

  // 根据ID获取知识点
  getKnowledgePointById: async (id: number) => {
    const response = await api.get(`/curriculum/knowledge-points/${id}`);
    return response.data;
  },

  // 根据科目ID获取学科版本（简化版本，用于下拉选择）
  getSubjectVersions: async (subjectId: number) => {
    const response = await api.get(`/curriculum/subjects/${subjectId}/versions`);
    return response.data;
  },

  // 获取有章节的科目版本列表
  getSubjectVersionsWithChapters: async () => {
    const response = await api.get('/curriculum/subject-versions/with-chapters');
    return response.data;
  },

  // 根据科目版本ID获取章节
  getChaptersBySubjectVersionId: async (subjectVersionId: number) => {
    const response = await api.get(`/curriculum/subject-versions/${subjectVersionId}/chapters`);
    return response.data;
  },

  // 根据科目ID获取章节
  getChaptersBySubjectId: async (subjectId: number) => {
    const response = await api.get(`/curriculum/subjects/${subjectId}/chapters`);
    return response.data;
  },

  // 获取有知识点的章节列表
  getChaptersWithKnowledgePoints: async () => {
    const response = await api.get('/curriculum/chapters/with-knowledge-points');
    return response.data;
  },

  // 根据章节ID获取知识点
  getKnowledgePointsByChapterId: async (chapterId: number) => {
    const response = await api.get(`/curriculum/chapters/${chapterId}/knowledge-points`);
    return response.data;
  },

  // 根据科目版本ID获取知识点
  getKnowledgePointsBySubjectVersionId: async (subjectVersionId: number) => {
    const response = await api.get(`/curriculum/subject-versions/${subjectVersionId}/knowledge-points`);
    return response.data;
  },

  // 根据科目ID获取知识点
  getKnowledgePointsBySubjectId: async (subjectId: number) => {
    const response = await api.get(`/curriculum/subjects/${subjectId}/knowledge-points`);
    return response.data;
  },

  // 根据知识点ID获取其层级信息
  getKnowledgePointHierarchy: async (knowledgePointId: number) => {
    const response = await api.get(`/curriculum/knowledge-points/${knowledgePointId}/hierarchy`);
    return response.data;
  },

  // ==================== 学科管理接口 ====================

  // 创建学科
  createSubject: async (subject: { name: string; description?: string }) => {
    const response = await api.post('/curriculum/subjects', subject);
    return response.data;
  },

  // 更新学科
  updateSubject: async (id: number, subject: { name: string; description?: string }) => {
    const response = await api.put(`/curriculum/subjects/${id}`, subject);
    return response.data;
  },

  // 删除学科
  deleteSubject: async (id: number) => {
    const response = await api.delete(`/curriculum/subjects/${id}`);
    return response.data;
  },

  // ==================== 学科版本管理接口 ====================

  // 获取所有学科版本
  getAllSubjectVersions: async () => {
    const response = await api.get('/curriculum/subject-versions');
    return response.data;
  },

  // 根据ID获取学科版本
  getSubjectVersionById: async (id: number) => {
    const response = await api.get(`/curriculum/subject-versions/${id}`);
    return response.data;
  },

  // 创建学科版本
  createSubjectVersion: async (subjectVersion: {
    name: string;
    description?: string;
    schoolLevel: string;
    subjectId: number
  }) => {
    const response = await api.post('/curriculum/subject-versions', {
      name: subjectVersion.name,
      description: subjectVersion.description,
      schoolLevel: subjectVersion.schoolLevel,
      subject: { id: subjectVersion.subjectId }
    });
    return response.data;
  },

  // 更新学科版本
  updateSubjectVersion: async (id: number, subjectVersion: {
    name: string;
    description?: string;
    schoolLevel: string;
    subjectId: number
  }) => {
    const response = await api.put(`/curriculum/subject-versions/${id}`, {
      name: subjectVersion.name,
      description: subjectVersion.description,
      schoolLevel: subjectVersion.schoolLevel,
      subject: { id: subjectVersion.subjectId }
    });
    return response.data;
  },

  // 删除学科版本
  deleteSubjectVersion: async (id: number) => {
    const response = await api.delete(`/curriculum/subject-versions/${id}`);
    return response.data;
  },

  // ==================== 章节管理接口 ====================

  // 创建章节
  createChapter: async (chapter: { name: string; orderIndex: number; subjectVersionId: number }) => {
    const response = await api.post('/curriculum/chapters', {
      name: chapter.name,
      orderIndex: chapter.orderIndex,
      subjectVersion: { id: chapter.subjectVersionId }
    });
    return response.data;
  },

  // 更新章节
  updateChapter: async (id: number, chapter: { name: string; orderIndex: number }) => {
    const response = await api.put(`/curriculum/chapters/${id}`, chapter);
    return response.data;
  },

  // 删除章节
  deleteChapter: async (id: number) => {
    const response = await api.delete(`/curriculum/chapters/${id}`);
    return response.data;
  },

  // ==================== 知识点管理接口 ====================

  // 创建知识点
  createKnowledgePoint: async (knowledgePoint: {
    name: string;
    orderIndex: number;
    chapterId: number;
    description?: string;
    coverImageUrl?: string;
    enabled?: boolean;
  }) => {
    const response = await api.post('/curriculum/knowledge-points', {
      name: knowledgePoint.name,
      orderIndex: knowledgePoint.orderIndex,
      description: knowledgePoint.description,
      coverImageUrl: knowledgePoint.coverImageUrl,
      enabled: knowledgePoint.enabled ?? true,
      chapter: { id: knowledgePoint.chapterId }
    });
    return response.data;
  },

  // 更新知识点
  updateKnowledgePoint: async (id: number, knowledgePoint: {
    name: string;
    orderIndex: number;
    chapterId?: number;
    description?: string;
    coverImageUrl?: string;
    enabled?: boolean;
  }) => {
    const requestData: any = {
      name: knowledgePoint.name,
      orderIndex: knowledgePoint.orderIndex,
      description: knowledgePoint.description,
      coverImageUrl: knowledgePoint.coverImageUrl,
      enabled: knowledgePoint.enabled
    };

    // 如果提供了章节ID，则包含章节信息
    if (knowledgePoint.chapterId) {
      requestData.chapter = { id: knowledgePoint.chapterId };
    }

    const response = await api.put(`/curriculum/knowledge-points/${id}`, requestData);
    return response.data;
  },

  // 删除知识点
  deleteKnowledgePoint: async (id: number) => {
    const response = await api.delete(`/curriculum/knowledge-points/${id}`);
    return response.data;
  },

  // ==================== 知识点视频管理接口 ====================

  // 为知识点添加视频
  addVideoToKnowledgePoint: async (id: number, videoUrl: string) => {
    const response = await api.post(`/curriculum/knowledge-points/${id}/video`, {
      videoUrl: videoUrl
    });
    return response.data;
  },

  // 移除知识点的视频
  removeVideoFromKnowledgePoint: async (id: number) => {
    const response = await api.delete(`/curriculum/knowledge-points/${id}/video`);
    return response.data;
  },

  // 获取知识点的视频信息
  getKnowledgePointVideo: async (id: number) => {
    const response = await api.get(`/curriculum/knowledge-points/${id}/video`);
    return response.data;
  },

  // 获取视频集合详细信息
  getVideoCollectionDetails: async (collectionId: number) => {
    const response = await api.get(`/curriculum/video-collections/${collectionId}/details`);
    return response.data;
  },

  // 从视频集合中移除单个视频
  removeVideoFromCollection: async (collectionId: number, videoId: number) => {
    const response = await api.delete(`/curriculum/video-collections/${collectionId}/videos/${videoId}`);
    return response.data;
  },

  // ==================== 视频上传接口 ====================

  // 上传视频文件
  uploadVideo: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload/video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 上传图片文件
  uploadImage: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 上传音频文件
  uploadAudio: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload/audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 验证视频文件
  validateVideo: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload/validate-video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// 题库管理API
export const questionAPI = {
  // 获取题目列表（分页）
  getQuestions: async (params: {
    page?: number;
    size?: number;
    knowledgePointId?: number;
    questionType?: string;
    subject?: string;
    difficulty?: string;
    enabled?: boolean;
    search?: string;
    sortBy?: string;
    sortDir?: string;
  } = {}) => {
    const {
      page = 0,
      size = 10,
      knowledgePointId,
      questionType,
      subject,
      difficulty,
      enabled,
      search,
      sortBy = 'createdAt',
      sortDir = 'desc'
    } = params;

    const queryParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      sort: `${sortBy},${sortDir}`
    });

    if (knowledgePointId) queryParams.append('knowledgePointId', knowledgePointId.toString());
    if (questionType) queryParams.append('questionType', questionType);
    if (subject) queryParams.append('subject', subject);
    if (difficulty) queryParams.append('difficulty', difficulty);
    if (enabled !== undefined) queryParams.append('enabled', enabled.toString());
    if (search) queryParams.append('search', search);

    const response = await api.get(`/questions?${queryParams}`);
    return response.data;
  },

  // 根据知识点获取题目列表
  getQuestionsByKnowledgePoint: async (knowledgePointId: number) => {
    const response = await api.get(`/questions/knowledge-point/${knowledgePointId}`);
    return response.data;
  },

  // 获取单个题目详情
  getQuestion: async (id: number) => {
    const response = await api.get(`/questions/${id}`);
    return response.data;
  },

  // 创建题目
  createQuestion: async (questionData: any) => {
    const response = await api.post('/questions', questionData);
    return response.data;
  },

  // 专门为前端表单创建题目
  createQuestionFromForm: async (formData: any) => {
    const response = await api.post('/questions/create-from-form', formData);
    return response.data;
  },

  // 批量创建题目
  createQuestions: async (questionsData: any[]) => {
    const response = await api.post('/questions/batch', questionsData);
    return response.data;
  },

  // 更新题目
  updateQuestion: async (id: number, questionData: any) => {
    const response = await api.put(`/questions/${id}`, questionData);
    return response.data;
  },

  // 启用/禁用题目
  toggleQuestionEnabled: async (id: number, enabled: boolean) => {
    const response = await api.patch(`/questions/${id}/enabled?enabled=${enabled}`);
    return response.data;
  },

  // 删除题目
  deleteQuestion: async (id: number) => {
    await api.delete(`/questions/${id}`);
  },

  // 批量删除题目
  deleteQuestions: async (ids: number[]) => {
    await api.delete('/questions/batch', { data: ids });
  },

  // 导入题目
  importQuestions: async (knowledgePointId: number, questionBodies: any[]) => {
    const response = await api.post(`/questions/import?knowledgePointId=${knowledgePointId}`, questionBodies);
    return response.data;
  },

  // 导出题目
  exportQuestions: async (params: {
    knowledgePointId?: number;
    questionType?: string;
    subject?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.knowledgePointId) queryParams.append('knowledgePointId', params.knowledgePointId.toString());
    if (params.questionType) queryParams.append('questionType', params.questionType);
    if (params.subject) queryParams.append('subject', params.subject);

    const response = await api.get(`/questions/export?${queryParams}`);
    return response.data;
  },

  // 获取题目统计信息
  getQuestionStatistics: async (knowledgePointId?: number) => {
    const queryParams = knowledgePointId ? `?knowledgePointId=${knowledgePointId}` : '';
    const response = await api.get(`/questions/statistics${queryParams}`);
    return response.data;
  },

  // 验证题目JSON格式
  validateQuestionBody: async (questionBody: any) => {
    const response = await api.post('/questions/validate', questionBody);
    return response.data;
  },
};

// 数据迁移API
export const migrationAPI = {
  // 从JSON对象迁移单个题目（仅支持单个对象格式）
  migrateFromJson: async (questionData: any) => {
    const response = await api.post('/migration/questions/from-json', questionData);
    return response.data;
  },

  // 从JSON字符串迁移单个题目（仅支持单个对象格式）
  migrateFromJsonString: async (jsonData: string) => {
    const response = await api.post('/migration/questions/from-json-string', jsonData, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data;
  },



  // 验证单个题目数据格式（使用JSON字符串验证）
  validateSourceData: async (questionData: any) => {
    const jsonString = JSON.stringify(questionData);
    const response = await api.post('/migration/questions/validate-json', jsonString, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data;
  },

  // 验证JSON字符串格式
  validateJsonFormat: async (jsonData: string) => {
    const response = await api.post('/migration/questions/validate-json', jsonData, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data;
  },

  // 调试JSON数据格式
  debugJsonFormat: async (jsonData: string) => {
    const response = await api.post('/migration/questions/debug-json', jsonData, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data;
  },

  // 获取当前迁移进度
  getMigrationProgress: async () => {
    const response = await api.get('/migration/questions/progress');
    return response.data;
  },

  // 停止当前迁移任务
  stopMigration: async () => {
    const response = await api.post('/migration/questions/stop');
    return response.data;
  },

  // 清理迁移缓存
  clearMigrationCache: async () => {
    const response = await api.post('/migration/questions/clear-cache');
    return response.data;
  },

  // 获取迁移系统状态
  getMigrationStatus: async () => {
    const response = await api.get('/migration/status');
    return response.data;
  }
};



export default api;
