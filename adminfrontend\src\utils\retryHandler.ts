import { AxiosError } from 'axios';
import { ErrorHandler, ProcessedError } from './errorHandler';

// 重试配置接口
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  retryDelayMultiplier: number;
  maxRetryDelay: number;
  retryCondition?: (error: ProcessedError) => boolean;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryDelayMultiplier: 2,
  maxRetryDelay: 10000,
  retryCondition: (error: ProcessedError) => ErrorHandler.isRetryable(error)
};

/**
 * 重试处理工具类
 */
export class RetryHandler {
  
  /**
   * 执行带重试的异步操作
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
    let lastError: ProcessedError | null = null;
    
    for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        const processedError = ErrorHandler.handleApiError(error as AxiosError);
        lastError = processedError;
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === finalConfig.maxRetries) {
          throw processedError;
        }
        
        // 检查是否应该重试
        if (!finalConfig.retryCondition!(processedError)) {
          throw processedError;
        }
        
        // 计算延迟时间
        const delay = Math.min(
          finalConfig.retryDelay * Math.pow(finalConfig.retryDelayMultiplier, attempt),
          finalConfig.maxRetryDelay
        );
        
        console.warn(`操作失败，${delay}ms后进行第${attempt + 1}次重试:`, processedError.message);
        
        // 等待延迟时间
        await this.delay(delay);
      }
    }
    
    // 理论上不会到达这里，但为了类型安全
    throw new Error(lastError?.message || '重试失败');
  }
  
  /**
   * 延迟函数
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 创建带重试的API调用函数
   */
  static createRetryableApiCall<T>(
    apiCall: () => Promise<T>,
    config?: Partial<RetryConfig>
  ) {
    return () => this.executeWithRetry(apiCall, config);
  }
  
  /**
   * 指数退避重试
   */
  static async exponentialBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    return this.executeWithRetry(operation, {
      maxRetries,
      retryDelay: baseDelay,
      retryDelayMultiplier: 2,
      maxRetryDelay: 30000
    });
  }
  
  /**
   * 线性重试
   */
  static async linearRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    return this.executeWithRetry(operation, {
      maxRetries,
      retryDelay: delay,
      retryDelayMultiplier: 1,
      maxRetryDelay: delay
    });
  }
  
  /**
   * 立即重试（无延迟）
   */
  static async immediateRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 2
  ): Promise<T> {
    return this.executeWithRetry(operation, {
      maxRetries,
      retryDelay: 0,
      retryDelayMultiplier: 1,
      maxRetryDelay: 0
    });
  }
  
  /**
   * 网络错误专用重试
   */
  static async retryOnNetworkError<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    return this.executeWithRetry(operation, {
      maxRetries,
      retryDelay: 2000,
      retryDelayMultiplier: 1.5,
      maxRetryDelay: 10000,
      retryCondition: (error) => error.code === 0 // 网络错误
    });
  }
  
  /**
   * 服务器错误专用重试
   */
  static async retryOnServerError<T>(
    operation: () => Promise<T>,
    maxRetries: number = 2
  ): Promise<T> {
    return this.executeWithRetry(operation, {
      maxRetries,
      retryDelay: 3000,
      retryDelayMultiplier: 2,
      maxRetryDelay: 15000,
      retryCondition: (error) => error.code >= 500
    });
  }
}

/**
 * 重试装饰器工厂
 */
export function withRetry(config?: Partial<RetryConfig>) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;
    
    descriptor.value = (async function (this: any, ...args: any[]) {
      return RetryHandler.executeWithRetry(
        () => method.apply(this, args),
        config
      );
    }) as T;
    
    return descriptor;
  };
}

/**
 * 批量操作重试工具
 */
export class BatchRetryHandler {
  
  /**
   * 批量执行操作，失败的操作会重试
   */
  static async executeBatch<T, R>(
    items: T[],
    operation: (item: T) => Promise<R>,
    config?: Partial<RetryConfig>
  ): Promise<{ success: R[]; failed: { item: T; error: ProcessedError }[] }> {
    const success: R[] = [];
    const failed: { item: T; error: ProcessedError }[] = [];
    
    for (const item of items) {
      try {
        const result = await RetryHandler.executeWithRetry(
          () => operation(item),
          config
        );
        success.push(result);
      } catch (error) {
        failed.push({ item, error: error as ProcessedError });
      }
    }
    
    return { success, failed };
  }
  
  /**
   * 并发批量执行操作
   */
  static async executeBatchConcurrent<T, R>(
    items: T[],
    operation: (item: T) => Promise<R>,
    concurrency: number = 5,
    config?: Partial<RetryConfig>
  ): Promise<{ success: R[]; failed: { item: T; error: ProcessedError }[] }> {
    const success: R[] = [];
    const failed: { item: T; error: ProcessedError }[] = [];
    
    // 分批处理
    for (let i = 0; i < items.length; i += concurrency) {
      const batch = items.slice(i, i + concurrency);
      
      const promises = batch.map(async (item) => {
        try {
          const result = await RetryHandler.executeWithRetry(
            () => operation(item),
            config
          );
          return { success: true as const, result, item };
        } catch (error) {
          return { success: false as const, error: error as ProcessedError, item };
        }
      });

      const results = await Promise.all(promises);

      results.forEach((result) => {
        if (result.success) {
          success.push(result.result);
        } else {
          failed.push({ item: result.item, error: result.error });
        }
      });
    }
    
    return { success, failed };
  }
}
