package com.example.adminbackend.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "knowledge_points")
public class KnowledgePoint {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(name = "order_index", nullable = false)
    private Integer orderIndex;
    
    @Column(name = "cover_image_url")
    private String coverImageUrl;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Builder.Default
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 自定义练习题目数量
     * 如果设置了此值，则优先使用此配置而不是全局配置
     */
    @Column(name = "custom_practice_count")
    private Integer customPracticeCount;

    /**
     * 是否启用自定义练习配置
     */
    @Builder.Default
    @Column(name = "use_custom_config", nullable = false)
    private Boolean useCustomConfig = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chapter_id", nullable = false)
    @JsonBackReference
    private Chapter chapter;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "video_collection_id")
    private VideoCollection videoCollection;

    @OneToMany(mappedBy = "knowledgePoint", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<StudentProgress> studentProgresses;
    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }

    /**
     * 获取有效的练习题目数量
     * 如果启用了自定义配置且设置了数量，则返回自定义数量，否则返回null
     */
    public Integer getEffectivePracticeCount() {
        if (useCustomConfig && customPracticeCount != null && customPracticeCount > 0) {
            return customPracticeCount;
        }
        return null;
    }
}