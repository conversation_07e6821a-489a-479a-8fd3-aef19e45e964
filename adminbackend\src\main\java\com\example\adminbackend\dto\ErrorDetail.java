package com.example.adminbackend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 错误详情类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorDetail {
    
    /**
     * 错误字段名
     */
    private String field;
    
    /**
     * 错误值
     */
    private Object rejectedValue;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 错误代码
     */
    private String code;
    
    /**
     * 修复建议
     */
    private String suggestion;
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建字段验证错误
     */
    public static ErrorDetail fieldError(String field, Object rejectedValue, String message) {
        return ErrorDetail.builder()
                .field(field)
                .rejectedValue(rejectedValue)
                .message(message)
                .build();
    }
    
    /**
     * 创建字段验证错误（带建议）
     */
    public static ErrorDetail fieldError(String field, Object rejectedValue, String message, String suggestion) {
        return ErrorDetail.builder()
                .field(field)
                .rejectedValue(rejectedValue)
                .message(message)
                .suggestion(suggestion)
                .build();
    }
    
    /**
     * 创建业务逻辑错误
     */
    public static ErrorDetail businessError(String code, String message) {
        return ErrorDetail.builder()
                .code(code)
                .message(message)
                .build();
    }
    
    /**
     * 创建业务逻辑错误（带建议）
     */
    public static ErrorDetail businessError(String code, String message, String suggestion) {
        return ErrorDetail.builder()
                .code(code)
                .message(message)
                .suggestion(suggestion)
                .build();
    }
}
