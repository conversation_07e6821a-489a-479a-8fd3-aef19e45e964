import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Alert,
  Snackbar,
} from '@mui/material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import Layout from '../components/Layout';
import UserForm from '../components/UserForm';
import { useAuth } from '../contexts/AuthContext';
import { userAPI } from '../services/api';
import { getRoleLabel, getRoleColor, getRoleAvatarColor } from '../utils/roleUtils';
import { User, PageResponse } from '../types';

const UsersPage: React.FC = () => {
  const { isSuperAdmin } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalElements, setTotalElements] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  // const [roleFilter, setRoleFilter] = useState<string | null>(null);
  
  // 对话框状态
  const [openForm, setOpenForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  
  // 筛选菜单
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  
  // 消息提示
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // 显示消息提示
  const showSnackbar = useCallback((message: string, severity: 'success' | 'error' = 'success') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  // 加载用户数据
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response: PageResponse<User> = await userAPI.getUsers(page, pageSize, isSuperAdmin);
      setUsers(response.content);
      setTotalElements(response.totalElements);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      showSnackbar('获取用户列表失败', 'error');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, isSuperAdmin, showSnackbar]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // 过滤用户数据 - 由于使用服务器端分页，暂时使用客户端过滤
  // TODO: 将来可以将搜索和筛选功能移到服务器端
  const filteredUsers = useMemo(() => {
    if (!searchTerm && !statusFilter) {
      // 如果没有搜索条件，直接返回服务器数据
      return users;
    }

    return users.filter(user => {
      const matchesSearch = searchTerm === '' ||
        (user.fullName && user.fullName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.username && user.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.phone && user.phone.includes(searchTerm));

      const matchesStatus = statusFilter === null ||
        (statusFilter === 'active' && user.enabled) ||
        (statusFilter === 'inactive' && !user.enabled);

      return matchesSearch && matchesStatus;
    });
  }, [users, searchTerm, statusFilter]);

  // 处理表单提交
  const handleFormSubmit = () => {
    setOpenForm(false);
    const isCreating = !selectedUser;
    setSelectedUser(null);

    // 如果是创建新用户，跳转到第一页并清空搜索条件，确保能看到新用户
    if (isCreating) {
      setPage(0);
      setSearchTerm('');
      setStatusFilter(null);
    }

    fetchUsers();
    showSnackbar(isCreating ? '用户创建成功' : '用户更新成功');
  };

  // 处理删除用户
  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await userAPI.deleteUser(userToDelete.id, isSuperAdmin);
      setOpenDeleteDialog(false);
      setUserToDelete(null);
      fetchUsers();
      showSnackbar('用户删除成功');
    } catch (error) {
      console.error('删除用户失败:', error);
      showSnackbar('删除用户失败', 'error');
    }
  };

  // 表格列定义
  const columns: GridColDef[] = [
    {
      field: 'username',
      headerName: '用户信息',
      flex: 1.2,
      minWidth: 200,
      renderCell: (params: GridRenderCellParams) => {
        // 优化头像显示逻辑：优先使用姓名，其次用户名，最后用手机号
        const displayName = params.row.fullName || params.row.username || params.row.phone || '';
        const avatarText = displayName.substring(0, 1).toUpperCase();

        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            py: 1,
            minWidth: 0, // 允许内容收缩
          }}>
            <Avatar
              sx={{
                width: 36,
                height: 36,
                mr: 1.5,
                bgcolor: getRoleAvatarColor(params.row.role),
                fontSize: '0.875rem',
                fontWeight: 600,
                flexShrink: 0, // 头像不收缩
              }}
            >
              {avatarText}
            </Avatar>
            <Box sx={{
              minWidth: 0, // 允许文本区域收缩
              flex: 1,
            }}>
              <Typography
                variant="body2"
                fontWeight={600}
                sx={{
                  color: 'text.primary',
                  lineHeight: 1.3,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {params.row.username}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  color: 'text.secondary',
                  lineHeight: 1.2,
                  display: 'block',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {params.row.phone}
              </Typography>
            </Box>
          </Box>
        );
      }
    },
    {
      field: 'fullName',
      headerName: '姓名',
      flex: 0.8,
      minWidth: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography
          variant="body2"
          fontWeight={500}
          sx={{
            color: 'text.primary',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {params.value || '-'}
        </Typography>
      )
    },
    {
      field: 'role',
      headerName: '角色',
      flex: 0.8,
      minWidth: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={getRoleLabel(params.value)}
          size="small"
          color={getRoleColor(params.value)}
          variant="filled"
          sx={{
            fontWeight: 500,
            fontSize: '0.75rem',
          }}
        />
      )
    },
    {
      field: 'enabled',
      headerName: '状态',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          icon={params.value ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
          label={params.value ? '已启用' : '已禁用'}
          size="small"
          color={params.value ? 'success' : 'default'}
          variant="outlined"
          sx={{
            fontWeight: 500,
            fontSize: '0.75rem',
          }}
        />
      )
    },
    {
      field: 'actions',
      headerName: '操作',
      width: 100,
      sortable: false,
      align: 'center',
      headerAlign: 'center',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
          <Tooltip title="编辑">
            <IconButton
              size="small"
              onClick={() => {
                setSelectedUser(params.row);
                setOpenForm(true);
              }}
              sx={{
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  color: 'primary.contrastText',
                }
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="删除">
            <IconButton
              size="small"
              onClick={() => {
                setUserToDelete(params.row);
                setOpenDeleteDialog(true);
              }}
              sx={{
                color: 'error.main',
                '&:hover': {
                  backgroundColor: 'error.light',
                  color: 'error.contrastText',
                }
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Layout>
      <Box>
        {/* 页面标题和操作栏 */}
        <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            <Typography variant="h5" fontWeight="bold">
              {isSuperAdmin ? '用户管理' : '督学管理'}
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <TextField
                placeholder="搜索用户..."
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                sx={{ minWidth: 200 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
              />
              
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                onClick={(e) => setFilterAnchorEl(e.currentTarget)}
              >
                筛选
                {statusFilter && (
                  <Chip
                    label={1}
                    size="small"
                    color="primary"
                    sx={{ ml: 1, height: 16, minWidth: 16 }}
                  />
                )}
              </Button>
              
              <Button 
                variant="contained" 
                startIcon={<AddIcon />}
                onClick={() => setOpenForm(true)}
              >
                {isSuperAdmin ? '添加用户' : '添加督学'}
              </Button>
            </Box>
          </Box>
        </Paper>

        {/* 用户列表 */}
        <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
          <DataGrid
            rows={filteredUsers}
            columns={columns}
            loading={loading}
            rowCount={searchTerm || statusFilter ? filteredUsers.length : totalElements}
            pageSizeOptions={[5, 10, 25, 50]}
            paginationMode={searchTerm || statusFilter ? "client" : "server"}
            paginationModel={{ page, pageSize }}
            onPaginationModelChange={(model) => {
              setPage(model.page);
              setPageSize(model.pageSize);
            }}
            disableRowSelectionOnClick
            autoHeight
            rowHeight={80}
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell:focus': { outline: 'none' },
              '& .MuiDataGrid-columnHeader:focus': { outline: 'none' },
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid',
                borderColor: 'divider',
                py: 1,
              },
              '& .MuiDataGrid-row:hover': {
                backgroundColor: 'action.hover',
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: 'grey.50',
                borderBottom: '2px solid',
                borderColor: 'divider',
              },
              '& .MuiDataGrid-columnHeaderTitle': {
                fontWeight: 600,
              }
            }}
          />
        </Paper>
      </Box>

      {/* 筛选菜单 */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={() => setFilterAnchorEl(null)}
      >
        <MenuItem onClick={() => { setStatusFilter(null); setFilterAnchorEl(null); }}>
          全部状态
        </MenuItem>
        <MenuItem onClick={() => { setStatusFilter('active'); setFilterAnchorEl(null); }}>
          已启用
        </MenuItem>
        <MenuItem onClick={() => { setStatusFilter('inactive'); setFilterAnchorEl(null); }}>
          已禁用
        </MenuItem>
      </Menu>

      {/* 用户表单对话框 */}
      <Dialog open={openForm} onClose={() => setOpenForm(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedUser ? '编辑用户' : '添加用户'}
        </DialogTitle>
        <DialogContent>
          <UserForm 
            user={selectedUser} 
            onSubmit={handleFormSubmit}
            onCancel={() => setOpenForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            您确定要删除用户 "{userToDelete?.fullName || userToDelete?.username}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>取消</Button>
          <Button onClick={handleDeleteUser} color="error" variant="contained">
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default UsersPage;
