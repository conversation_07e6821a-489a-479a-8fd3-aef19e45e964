package com.example.adminbackend.dto;

import com.example.adminbackend.model.Question;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 创建题目请求DTO
 */
@Data
public class QuestionCreateRequest {

    @NotNull(message = "知识点ID不能为空")
    private Long knowledgePointId;

    @NotNull(message = "题目类型不能为空")
    private Question.QuestionType questionType;

    @NotNull(message = "题目内容不能为空")
    private Map<String, Object> body;

    private Boolean enabled = true;
}
