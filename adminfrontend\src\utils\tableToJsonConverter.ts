/**
 * 表格数据到JSON转换工具
 */

interface TableRow {
  [key: string]: string | number;
}

interface QuestionJSON {
  id: string;
  type: string;
  subject: string;
  difficulty: string;
  content: string;
  material?: string; // 嵌套题型的材料内容
  answer: any; // 根据题型不同，可能是string、string[]或boolean
  subQuestions?: any[]; // 嵌套题型的子题目数组
  explanation: string;
  options?: string[];
  tags?: string[];
  remarks?: string;
}

interface QuestionGroup {
  isNested: boolean;
  rows: TableRow[];
  startRowIndex: number;
  questionType: string;
}

interface ConversionResult {
  success: boolean;
  data: QuestionJSON[];
  errors: string[];
}

export class TableToJsonConverter {
  
  // 列名映射 (移除题目ID，改为自动生成)
  private readonly columnMapping = {
    '题目类型': 'type',
    '学科': 'subject',
    '难度': 'difficulty',
    '题目内容': 'content',
    '材料内容': 'material',
    '子题目序号': 'subQuestionIndex',
    '子题目内容': 'subQuestionContent',
    '答案': 'answer',
    '子题目JSON': 'subQuestions',
    '解析': 'explanation',
    '选项A': 'option_a',
    '选项B': 'option_b',
    '选项C': 'option_c',
    '选项D': 'option_d',
    '选项E': 'option_e',
    '选项F': 'option_f',
    '标签': 'tags',
    '备注': 'remarks'
  };

  // 选择题类型
  private readonly choiceTypes = ['SINGLE_CHOICE', 'MULTIPLE_CHOICE'];

  // 嵌套题型
  private readonly nestedTypes = ['READING_COMPREHENSION', 'LISTENING', 'CLOZE_TEST'];

  /**
   * 转换表格数据为JSON格式
   */
  convertTableToJson(tableData: TableRow[]): ConversionResult {
    const result: ConversionResult = {
      success: true,
      data: [],
      errors: []
    };

    try {
      // 先分组处理嵌套题型
      const questionGroups = this.groupQuestionRows(tableData);

      questionGroups.forEach((group, index) => {
        try {
          let questionJson: QuestionJSON;

          if (group.isNested) {
            // 处理嵌套题型（多行合并）
            questionJson = this.convertNestedQuestionGroup(group);
          } else {
            // 处理简单题型（单行）
            questionJson = this.convertSingleRow(group.rows[0], group.startRowIndex + 1);
          }

          result.data.push(questionJson);
        } catch (error) {
          result.success = false;
          const errorMessage = error instanceof Error ? error.message : String(error);
          result.errors.push(`题目组${index + 1}转换失败: ${errorMessage}`);
        }
      });
    } catch (error) {
      result.success = false;
      const errorMessage = error instanceof Error ? error.message : String(error);
      result.errors.push(`数据分组失败: ${errorMessage}`);
    }

    return result;
  }

  /**
   * 转换单行数据
   */
  private convertSingleRow(row: TableRow, rowNumber: number): QuestionJSON {
    // 自动生成时间戳ID
    const autoId = this.generateQuestionId();

    // 基础字段转换
    const questionJson: QuestionJSON = {
      id: autoId, // 使用自动生成的时间戳ID
      type: this.getStringValue(row, '题目类型'),
      subject: this.getStringValue(row, '学科'),
      difficulty: this.getStringValue(row, '难度'),
      content: this.getStringValue(row, '题目内容'),
      answer: this.getStringValue(row, '答案'),
      explanation: this.getStringValue(row, '解析')
    };

    // 处理嵌套题型的特殊字段
    if (this.nestedTypes.includes(questionJson.type)) {
      questionJson.material = this.getStringValue(row, '材料内容');
      const subQuestionsStr = this.getStringValue(row, '子题目JSON');
      if (subQuestionsStr) {
        try {
          questionJson.subQuestions = JSON.parse(subQuestionsStr);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          throw new Error(`子题目JSON格式错误: ${errorMessage}`);
        }
      }
    }

    // 验证必需字段
    this.validateRequiredFields(questionJson, rowNumber);

    // 处理选择题选项
    if (this.choiceTypes.includes(questionJson.type)) {
      questionJson.options = this.extractOptions(row);
      this.validateChoiceQuestion(questionJson, rowNumber);
    }

    // 处理可选字段
    this.addOptionalFields(questionJson, row);

    // 处理答案格式
    questionJson.answer = this.formatAnswer(questionJson.answer, questionJson.type);

    return questionJson;
  }

  /**
   * 获取字符串值
   */
  private getStringValue(row: TableRow, columnName: string): string {
    const value = row[columnName];
    return value ? String(value).trim() : '';
  }

  /**
   * 生成唯一的题目ID
   */
  private generateQuestionId(): string {
    const timestamp = Date.now().toString();
    const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${timestamp}${randomSuffix}`;
  }

  /**
   * 验证必需字段
   */
  private validateRequiredFields(questionJson: QuestionJSON, rowNumber: number): void {
    // 基础必需字段（所有题型都需要）
    const baseRequiredFields = ['type', 'subject', 'difficulty', 'content', 'explanation'];

    baseRequiredFields.forEach(field => {
      if (!questionJson[field as keyof QuestionJSON]) {
        throw new Error(`必需字段 ${field} 不能为空`);
      }
    });

    // 根据题型验证特定字段
    if (this.nestedTypes.includes(questionJson.type)) {
      // 嵌套题型额外必需字段
      if (questionJson.type === 'READING_COMPREHENSION' || questionJson.type === 'CLOZE_TEST') {
        // 阅读理解和完形填空需要material字段
        if (!questionJson.material) {
          throw new Error('阅读理解和完形填空必须填写材料内容');
        }
      }
      // 听力题不需要material字段，音频内容在content字段中

      if (!questionJson.subQuestions || questionJson.subQuestions.length === 0) {
        throw new Error('嵌套题型必须填写子题目JSON');
      }
    } else {
      // 简单题型额外必需字段
      if (!questionJson.answer) {
        throw new Error('简单题型必须填写答案');
      }
    }

    // 验证枚举值
    const enums = {
      type: ['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'FILL_IN_BLANK', 'TRUE_FALSE', 'READING_COMPREHENSION', 'LISTENING', 'CLOZE_TEST', 'MATCHING'],
      subject: ['ENGLISH', 'MATH', 'PHYSICS', 'CHEMISTRY', 'BIOLOGY', 'HISTORY', 'GEOGRAPHY'],
      difficulty: ['EASY', 'MEDIUM', 'HARD']
    };

    Object.entries(enums).forEach(([field, validValues]) => {
      const value = questionJson[field as keyof QuestionJSON] as string;
      if (!validValues.includes(value)) {
        throw new Error(`${field} 字段值无效: ${value}`);
      }
    });
  }

  /**
   * 提取选择题选项
   */
  private extractOptions(row: TableRow): string[] {
    const options: string[] = [];
    const optionColumns = ['选项A', '选项B', '选项C', '选项D', '选项E', '选项F'];
    
    optionColumns.forEach(column => {
      const value = this.getStringValue(row, column);
      if (value) {
        options.push(value);
      }
    });

    return options;
  }

  /**
   * 验证选择题
   */
  private validateChoiceQuestion(questionJson: QuestionJSON, rowNumber: number): void {
    if (!questionJson.options || questionJson.options.length < 2) {
      throw new Error('选择题至少需要2个选项');
    }

    // 验证答案格式 - 在格式化之前验证原始答案
    const rawAnswer = String(questionJson.answer).toUpperCase();
    const validLetters = ['A', 'B', 'C', 'D', 'E', 'F'].slice(0, questionJson.options.length);

    if (questionJson.type === 'SINGLE_CHOICE') {
      if (rawAnswer.length !== 1 || !validLetters.includes(rawAnswer)) {
        throw new Error(`单选题答案必须是 ${validLetters.join('、')} 中的一个字母`);
      }
    } else if (questionJson.type === 'MULTIPLE_CHOICE') {
      const answerLetters = rawAnswer.split(/[,，\s]+/).filter(a => a);
      if (answerLetters.length === 0) {
        throw new Error('多选题答案不能为空');
      }

      answerLetters.forEach(letter => {
        if (!validLetters.includes(letter)) {
          throw new Error(`多选题答案包含无效选项: ${letter}`);
        }
      });
    }
  }

  /**
   * 格式化答案 - 根据题型返回正确的数据格式
   */
  private formatAnswer(answer: string, questionType: string): any {
    const trimmedAnswer = answer.trim();

    switch (questionType) {
      case 'SINGLE_CHOICE':
        // 单选题：返回单个字母字符串
        return trimmedAnswer.toUpperCase();

      case 'MULTIPLE_CHOICE':
        // 多选题：返回字母数组
        return trimmedAnswer.toUpperCase()
          .replace(/[,，\s]+/g, ',')
          .split(',')
          .filter(a => a.trim())
          .sort(); // 返回数组，不是字符串

      case 'FILL_IN_BLANK':
        // 填空题：返回字符串数组
        return [trimmedAnswer];

      case 'TRUE_FALSE':
        // 判断题：返回布尔值
        const lowerAnswer = trimmedAnswer.toLowerCase();
        if (lowerAnswer === 'true' || lowerAnswer === '是' || lowerAnswer === '对' || lowerAnswer === 'yes') {
          return true;
        } else if (lowerAnswer === 'false' || lowerAnswer === '否' || lowerAnswer === '错' || lowerAnswer === 'no') {
          return false;
        } else {
          throw new Error(`判断题答案格式错误，应为 true/false 或 是/否: ${trimmedAnswer}`);
        }

      default:
        // 其他题型：返回原始字符串
        return trimmedAnswer;
    }
  }

  /**
   * 验证转换后的JSON数据
   */
  validateConvertedData(questions: QuestionJSON[]): string[] {
    const errors: string[] = [];
    const idSet = new Set<string>();

    questions.forEach((question, index) => {
      const rowNumber = index + 1;

      // 检查ID重复
      if (idSet.has(question.id)) {
        errors.push(`第${rowNumber}行: 题目ID重复 - ${question.id}`);
      } else {
        idSet.add(question.id);
      }

      // 检查内容长度
      if (question.content.length > 2000) {
        errors.push(`第${rowNumber}行: 题目内容过长（超过2000字符）`);
      }

      if (question.explanation.length > 2000) {
        errors.push(`第${rowNumber}行: 解析内容过长（超过2000字符）`);
      }

      // 检查选项数量
      if (question.options && question.options.length > 6) {
        errors.push(`第${rowNumber}行: 选项数量过多（最多6个）`);
      }

      // 检查标签数量
      if (question.tags && question.tags.length > 10) {
        errors.push(`第${rowNumber}行: 标签数量过多（最多10个）`);
      }
    });

    return errors;
  }

  /**
   * 获取转换统计信息
   */
  getConversionStats(questions: QuestionJSON[]): {
    total: number;
    byType: Record<string, number>;
    bySubject: Record<string, number>;
    byDifficulty: Record<string, number>;
  } {
    const stats = {
      total: questions.length,
      byType: {} as Record<string, number>,
      bySubject: {} as Record<string, number>,
      byDifficulty: {} as Record<string, number>
    };

    questions.forEach(question => {
      // 按类型统计
      stats.byType[question.type] = (stats.byType[question.type] || 0) + 1;
      
      // 按学科统计
      stats.bySubject[question.subject] = (stats.bySubject[question.subject] || 0) + 1;
      
      // 按难度统计
      stats.byDifficulty[question.difficulty] = (stats.byDifficulty[question.difficulty] || 0) + 1;
    });

    return stats;
  }

  /**
   * 将表格行分组，处理嵌套题型的多行情况
   */
  private groupQuestionRows(tableData: TableRow[]): QuestionGroup[] {
    const groups: QuestionGroup[] = [];
    let currentGroup: QuestionGroup | null = null;

    tableData.forEach((row, index) => {
      const questionType = this.getStringValue(row, '题目类型');
      const subQuestionIndex = this.getStringValue(row, '子题目序号');
      const isNested = this.nestedTypes.includes(questionType);



      // 验证题目类型
      if (!questionType) {
        throw new Error(`第${index + 1}行：题目类型不能为空`);
      }

      if (isNested && subQuestionIndex) {
        // 嵌套题型的子题目行
        if (!currentGroup || currentGroup.questionType !== questionType || !currentGroup.isNested) {
          // 开始新的嵌套题目组
          currentGroup = {
            isNested: true,
            rows: [row],
            startRowIndex: index,
            questionType: questionType
          };
          groups.push(currentGroup);
        } else {
          // 添加到当前嵌套题目组
          currentGroup.rows.push(row);
        }
      } else if (isNested && !subQuestionIndex) {
        // 嵌套题型但没有子题目序号，可能是使用JSON方式
        currentGroup = {
          isNested: false, // 按简单题型处理
          rows: [row],
          startRowIndex: index,
          questionType: questionType
        };
        groups.push(currentGroup);
      } else {
        // 简单题型
        currentGroup = {
          isNested: false,
          rows: [row],
          startRowIndex: index,
          questionType: questionType
        };
        groups.push(currentGroup);
      }
    });

    // 验证嵌套题目组的完整性
    this.validateQuestionGroups(groups);



    return groups;
  }

  /**
   * 验证题目组的完整性
   */
  private validateQuestionGroups(groups: QuestionGroup[]): void {
    groups.forEach((group, index) => {
      if (group.isNested) {
        if (group.rows.length === 0) {
          throw new Error(`题目组${index + 1}：嵌套题型没有子题目`);
        }

        // 检查第一行的题目类型和材料内容
        const firstRow = group.rows[0];
        const questionType = this.getStringValue(firstRow, '题目类型');
        const material = this.getStringValue(firstRow, '材料内容');

        // 只有阅读理解和完形填空需要材料内容
        if (questionType === 'READING_COMPREHENSION' || questionType === 'CLOZE_TEST') {
          if (!material) {
            throw new Error(`题目组${index + 1}：${questionType === 'READING_COMPREHENSION' ? '阅读理解' : '完形填空'}必须在第一行填写材料内容`);
          }
        }
        // 听力题不需要材料内容，音频在题目内容中
      }
    });
  }

  /**
   * 转换嵌套题型组（多行合并为一个题目）
   */
  private convertNestedQuestionGroup(group: QuestionGroup): QuestionJSON {
    if (!group.isNested || group.rows.length === 0) {
      throw new Error('无效的嵌套题目组');
    }

    const firstRow = group.rows[0];


    // 自动生成时间戳ID
    const autoId = this.generateQuestionId();

    // 基础字段从第一行获取
    const questionJson: QuestionJSON = {
      id: autoId,
      type: this.getStringValue(firstRow, '题目类型'),
      subject: this.getStringValue(firstRow, '学科'),
      difficulty: this.getStringValue(firstRow, '难度'),
      content: this.getStringValue(firstRow, '题目内容'), // 嵌套题型也需要主题目内容
      answer: '', // 嵌套题型不使用answer字段，答案在子题目中
      explanation: this.getStringValue(firstRow, '解析')
    };

    // 根据题型设置material字段
    if (questionJson.type === 'READING_COMPREHENSION' || questionJson.type === 'CLOZE_TEST') {
      // 阅读理解和完形填空需要material字段，直接从第一行获取
      const material = this.getStringValue(firstRow, '材料内容');
      questionJson.material = material;

    }
    // 听力题不需要material字段，音频内容在content字段中

    // 处理子题目
    const subQuestions = this.processSubQuestions(group.rows);


    questionJson.subQuestions = subQuestions;

    // 处理可选字段
    this.addOptionalFields(questionJson, firstRow);

    // 验证嵌套题型
    this.validateNestedQuestion(questionJson);

    return questionJson;
  }

  /**
   * 处理子题目数组
   */
  private processSubQuestions(rows: TableRow[]): any[] {
    const subQuestions: any[] = [];
    rows.forEach((row, index) => {
      const subQuestionIndex = this.getStringValue(row, '子题目序号');
      const subQuestionContent = this.getStringValue(row, '子题目内容');
      const answer = this.getStringValue(row, '答案');

      if (subQuestionIndex && subQuestionContent && answer) {
        try {
          const subQuestion = this.createSubQuestion(subQuestionIndex, subQuestionContent, answer, row);
          subQuestions.push(subQuestion);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          throw new Error(`子题目${subQuestionIndex}处理失败: ${errorMessage}`);
        }
      } else if (subQuestionIndex || subQuestionContent || answer) {
        // 部分字段有值但不完整
        throw new Error(`子题目${subQuestionIndex || index + 1}信息不完整，请检查子题目序号、内容和答案字段`);
      }
    });
    return subQuestions;
  }

  /**
   * 创建单个子题目
   */
  private createSubQuestion(index: string, content: string, answer: string, row: TableRow): any {
    // 检测子题目类型
    const options = this.extractOptions(row);
    const subQuestionType = options.length > 0 ? 'SINGLE_CHOICE' : 'FILL_IN_BLANK';

    const subQuestion: any = {
      id: `q${index}`,
      type: subQuestionType,
      content: content,
      answer: this.formatAnswer(answer, subQuestionType)
    };

    // 添加选项（如果有）
    if (options.length > 0) {
      subQuestion.options = options;

      // 验证选择题答案
      if (subQuestionType === 'SINGLE_CHOICE') {
        const validLetters = ['A', 'B', 'C', 'D', 'E', 'F'].slice(0, options.length);
        if (!validLetters.includes(answer.toUpperCase())) {
          throw new Error(`答案 "${answer}" 不在有效选项范围内 (${validLetters.join(', ')})`);
        }
      }
    }

    return subQuestion;
  }

  /**
   * 添加可选字段（标签和备注）
   */
  private addOptionalFields(questionJson: QuestionJSON, row: TableRow): void {
    // 处理标签
    const tagsValue = this.getStringValue(row, '标签');
    if (tagsValue) {
      questionJson.tags = tagsValue.split(/[,，]/).map(tag => tag.trim()).filter(tag => tag);
    }

    // 处理备注
    const remarksValue = this.getStringValue(row, '备注');
    if (remarksValue) {
      questionJson.remarks = remarksValue;
    }
  }

  /**
   * 验证嵌套题型
   */
  private validateNestedQuestion(questionJson: QuestionJSON): void {
    // 只有阅读理解和完形填空需要material字段
    if (questionJson.type === 'READING_COMPREHENSION' || questionJson.type === 'CLOZE_TEST') {
      if (!questionJson.material) {
        throw new Error(`${questionJson.type === 'READING_COMPREHENSION' ? '阅读理解' : '完形填空'}必须填写材料内容`);
      }
    }
    // 听力题不需要material字段，音频内容在content字段中

    if (!questionJson.subQuestions || questionJson.subQuestions.length === 0) {
      throw new Error('嵌套题型必须包含至少一个子题目');
    }
  }
}

// 导出单例实例
export const tableToJsonConverter = new TableToJsonConverter();
