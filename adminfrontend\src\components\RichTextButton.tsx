import React, { useState } from 'react';
import { IconButton, Tooltip } from '@mui/material';
import { Edit as EditIcon } from '@mui/icons-material';
import RichTextDialog from './RichTextDialog';
import { PendingFile } from './WangEditor';

interface RichTextButtonProps {
  value: string;
  onChange: (value: string) => void;
  onFilesReady?: (files: PendingFile[]) => void;
  title?: string;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const RichTextButton: React.FC<RichTextButtonProps> = ({
  value,
  onChange,
  onFilesReady,
  title = '富文本编辑',
  placeholder = '请输入内容...',
  disabled = false,
  size = 'small',
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleOpenDialog = () => {
    if (!disabled) {
      setDialogOpen(true);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleSave = (content: string, files?: PendingFile[]) => {
    onChange(content);
    if (files && files.length > 0) {
      onFilesReady?.(files);
    }
  };

  return (
    <>
      <Tooltip title={title}>
        <span>
          <IconButton
            onClick={handleOpenDialog}
            disabled={disabled}
            size={size}
            color="primary"
            sx={{
              ml: 1,
              minWidth: '40px',
              minHeight: '40px',
              border: '1px solid',
              borderColor: 'primary.main',
              borderRadius: 1,
              '&:hover': {
                backgroundColor: 'primary.main',
                color: 'white',
              },
            }}
          >
            <EditIcon />
          </IconButton>
        </span>
      </Tooltip>

      <RichTextDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        onSave={handleSave}
        title={title}
        initialContent={value}
        placeholder={placeholder}
      />
    </>
  );
};

export default RichTextButton;
