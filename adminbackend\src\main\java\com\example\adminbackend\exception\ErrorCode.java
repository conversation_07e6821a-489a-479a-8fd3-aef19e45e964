package com.example.adminbackend.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误码枚举
 */
@Getter
@AllArgsConstructor
public enum ErrorCode {
    
    // ==================== 通用错误 (1000-1999) ====================
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(1000, "系统内部错误"),
    PARAMETER_ERROR(1001, "参数错误"),
    VALIDATION_ERROR(1002, "数据验证失败"),
    REQUEST_METHOD_NOT_SUPPORTED(1003, "请求方法不支持"),
    MEDIA_TYPE_NOT_SUPPORTED(1004, "媒体类型不支持"),
    REQUEST_TIMEOUT(1005, "请求超时"),
    
    // ==================== 认证授权错误 (2000-2999) ====================
    UNAUTHORIZED(2000, "未授权访问"),
    LOGIN_FAILED(2001, "登录失败"),
    TOKEN_INVALID(2002, "令牌无效"),
    TOKEN_EXPIRED(2003, "令牌已过期"),
    ACCESS_DENIED(2004, "没有权限访问此资源"),
    ACCOUNT_DISABLED(2005, "账户已被禁用"),
    ACCOUNT_LOCKED(2006, "账户已被锁定"),
    PASSWORD_EXPIRED(2007, "密码已过期"),
    
    // ==================== 用户管理错误 (3000-3999) ====================
    USER_NOT_FOUND(3000, "用户不存在"),
    USER_ALREADY_EXISTS(3001, "用户已存在"),
    USERNAME_ALREADY_EXISTS(3002, "用户名已存在"),
    PHONE_ALREADY_EXISTS(3003, "手机号已存在"),
    INVALID_PHONE_FORMAT(3004, "手机号格式不正确"),
    INVALID_PASSWORD_FORMAT(3005, "密码格式不正确"),
    PASSWORD_NOT_MATCH(3006, "密码不匹配"),
    CANNOT_DELETE_SELF(3007, "不能删除自己"),
    CANNOT_MODIFY_SUPER_ADMIN(3008, "不能修改超级管理员"),
    INSUFFICIENT_PERMISSIONS(3009, "权限不足"),
    
    // ==================== 题库管理错误 (4000-4999) ====================
    QUESTION_NOT_FOUND(4000, "题目不存在"),
    QUESTION_TYPE_INVALID(4001, "题目类型无效"),
    QUESTION_CONTENT_INVALID(4002, "题目内容格式无效"),
    QUESTION_JSON_INVALID(4003, "题目JSON格式无效"),
    QUESTION_ANSWER_INVALID(4004, "题目答案格式无效"),
    QUESTION_OPTIONS_INVALID(4005, "题目选项格式无效"),
    QUESTION_DIFFICULTY_INVALID(4006, "题目难度无效"),
    QUESTION_SUBJECT_INVALID(4007, "题目科目无效"),
    QUESTION_KNOWLEDGE_POINT_NOT_FOUND(4008, "知识点不存在"),
    QUESTION_IMPORT_FAILED(4009, "题目导入失败"),
    QUESTION_EXPORT_FAILED(4010, "题目导出失败"),
    QUESTION_BATCH_DELETE_FAILED(4011, "批量删除题目失败"),
    
    // ==================== 课程体系错误 (5000-5999) ====================
    SUBJECT_NOT_FOUND(5000, "科目不存在"),
    CHAPTER_NOT_FOUND(5001, "章节不存在"),
    KNOWLEDGE_POINT_NOT_FOUND(5002, "知识点不存在"),
    SUBJECT_VERSION_NOT_FOUND(5003, "科目版本不存在"),
    CURRICULUM_HIERARCHY_ERROR(5004, "课程体系层级错误"),
    
    // ==================== 文件处理错误 (6000-6999) ====================
    FILE_NOT_FOUND(6000, "文件不存在"),
    FILE_UPLOAD_FAILED(6001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(6002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(6003, "文件大小超出限制"),
    FILE_PROCESSING_FAILED(6004, "文件处理失败"),
    
    // ==================== 数据库错误 (7000-7999) ====================
    DATABASE_ERROR(7000, "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION(7001, "数据完整性约束违反"),
    DUPLICATE_KEY_ERROR(7002, "数据重复"),
    FOREIGN_KEY_CONSTRAINT_VIOLATION(7003, "外键约束违反"),
    DATA_ACCESS_ERROR(7004, "数据访问错误"),
    
    // ==================== 外部服务错误 (8000-8999) ====================
    EXTERNAL_SERVICE_ERROR(8000, "外部服务调用失败"),
    NETWORK_ERROR(8001, "网络连接错误"),
    SERVICE_UNAVAILABLE(8002, "服务不可用"),
    TIMEOUT_ERROR(8003, "服务调用超时"),
    
    // ==================== 业务逻辑错误 (9000-9999) ====================
    BUSINESS_LOGIC_ERROR(9000, "业务逻辑错误"),
    OPERATION_NOT_ALLOWED(9001, "操作不被允许"),
    RESOURCE_CONFLICT(9002, "资源冲突"),
    RESOURCE_LOCKED(9003, "资源被锁定"),
    QUOTA_EXCEEDED(9004, "配额超出限制");
    
    /**
     * 错误码
     */
    private final int code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    /**
     * 根据错误码获取枚举
     */
    public static ErrorCode fromCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return SYSTEM_ERROR;
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this.code == SUCCESS.code;
    }
    
    /**
     * 判断是否为客户端错误
     */
    public boolean isClientError() {
        return this.code >= 400 && this.code < 500;
    }
    
    /**
     * 判断是否为服务器错误
     */
    public boolean isServerError() {
        return this.code >= 500;
    }
}
