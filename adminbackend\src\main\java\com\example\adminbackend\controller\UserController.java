package com.example.adminbackend.controller;

import com.example.adminbackend.dto.UserDTO;
import com.example.adminbackend.service.UserService;
import com.example.adminbackend.model.Role;
import com.example.adminbackend.model.User;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;

    // 超级管理员接口 - 创建任何角色的用户
    @PostMapping("/super-admin/create")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<User> createUserBySuperAdmin(@Valid @RequestBody UserDTO userDTO, 
                                                      @AuthenticationPrincipal User currentUser) {
        userDTO.setCreator(currentUser.getUsername());
        User createdUser = userService.createUser(userDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
    }

    // 超级管理员接口 - 获取所有用户
    @GetMapping("/super-admin/all")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<List<User>> getAllUsersBySuperAdmin() {
        List<User> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    // 超级管理员接口 - 分页获取所有用户
    @GetMapping("/super-admin/page")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Page<User>> getAllUsersBySuperAdmin(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        Pageable pageable = PageRequest.of(page, size,
            sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending());
        Page<User> users = userService.getAllUsers(pageable);
        return ResponseEntity.ok(users);
    }

    // 超级管理员接口 - 根据角色获取用户
    @GetMapping("/super-admin/role/{role}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<List<User>> getUsersByRoleBySuperAdmin(@PathVariable Role role) {
        List<User> users = userService.getUsersByRole(role);
        return ResponseEntity.ok(users);
    }

    // 超级管理员接口 - 获取单个用户
    @GetMapping("/super-admin/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<User> getUserBySuperAdmin(@PathVariable Long id) {
        User user = userService.getUserById(id);
        return ResponseEntity.ok(user);
    }

    // 超级管理员接口 - 更新用户
    @PutMapping("/super-admin/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<User> updateUserBySuperAdmin(@PathVariable Long id, 
                                                      @Valid @RequestBody UserDTO userDTO) {
        User updatedUser = userService.updateUser(id, userDTO);
        return ResponseEntity.ok(updatedUser);
    }

    // 超级管理员接口 - 删除用户
    @DeleteMapping("/super-admin/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Void> deleteUserBySuperAdmin(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }

    // 管理员接口 - 创建督学用户
    @PostMapping("/admin/create-supervisor")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<User> createSupervisorByAdmin(@Valid @RequestBody UserDTO userDTO, 
                                                       @AuthenticationPrincipal User currentUser) {
        // 确保只能创建督学角色
        if (userDTO.getRole() != Role.SUPERVISOR) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        
        userDTO.setCreator(currentUser.getUsername());
        User createdUser = userService.createUser(userDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
    }

    // 管理员接口 - 获取所有督学用户
    @GetMapping("/admin/supervisors")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<List<User>> getSupervisorsByAdmin() {
        List<User> supervisors = userService.getUsersByRole(Role.SUPERVISOR);
        return ResponseEntity.ok(supervisors);
    }

    // 管理员接口 - 分页获取所有督学用户
    @GetMapping("/admin/supervisors/page")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Page<User>> getSupervisorsByAdmin(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        Pageable pageable = PageRequest.of(page, size,
            sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending());
        Page<User> supervisors = userService.getUsersByRole(Role.SUPERVISOR, pageable);
        return ResponseEntity.ok(supervisors);
    }

    // 管理员接口 - 获取单个督学用户
    @GetMapping("/admin/supervisor/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<User> getSupervisorByAdmin(@PathVariable Long id) {
        User user = userService.getUserById(id);
        if (user.getRole() != Role.SUPERVISOR) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        return ResponseEntity.ok(user);
    }

    // 管理员接口 - 更新督学用户
    @PutMapping("/admin/supervisor/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<User> updateSupervisorByAdmin(@PathVariable Long id, 
                                                       @Valid @RequestBody UserDTO userDTO) {
        // 确保只能更新督学角色
        User existingUser = userService.getUserById(id);
        if (existingUser.getRole() != Role.SUPERVISOR || userDTO.getRole() != Role.SUPERVISOR) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        
        User updatedUser = userService.updateUser(id, userDTO);
        return ResponseEntity.ok(updatedUser);
    }

    // 管理员接口 - 删除督学用户
    @DeleteMapping("/admin/supervisor/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
    public ResponseEntity<Void> deleteSupervisorByAdmin(@PathVariable Long id) {
        User existingUser = userService.getUserById(id);
        if (existingUser.getRole() != Role.SUPERVISOR) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }

    // 获取当前登录用户信息
    @GetMapping("/me")
    public ResponseEntity<User> getCurrentUser(@AuthenticationPrincipal User currentUser) {
        return ResponseEntity.ok(currentUser);
    }
} 